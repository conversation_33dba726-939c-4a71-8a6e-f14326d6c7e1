# 🔧 Guía de Debugging - Volcano App Frontend

Esta guía explica cómo usar el sistema de debugging avanzado implementado en el frontend de Volcano App para detectar, analizar y resolver errores de manera más eficiente.

## 🚀 Características del Sistema

### 1. **Captura Automática de Errores**
- ✅ Errores de JavaScript no capturados
- ✅ Promesas rechazadas no manejadas
- ✅ Errores de React (Error Boundary)
- ✅ Errores de red y API
- ✅ Errores de UI y componentes

### 2. **Logging Estructurado**
- ✅ Logs categorizados (react, api, ui, network, general)
- ✅ Niveles de severidad (error, warn, info, debug)
- ✅ Timestamps y metadatos automáticos
- ✅ Stack traces completos

### 3. **Panel de Debugging Visual**
- ✅ Interfaz gráfica para revisar logs
- ✅ Filtros por nivel y categoría
- ✅ Exportación de logs para análisis
- ✅ Estadísticas en tiempo real

### 4. **Monitor del Servidor Vite**
- ✅ Captura errores de compilación
- ✅ Análisis de patrones de error
- ✅ Logs estructurados del servidor
- ✅ Estadísticas de errores

## 🛠️ Cómo Usar el Sistema

### **Iniciar con Debugging Habilitado**

```bash
# Opción 1: Servidor normal
npm run dev

# Opción 2: Servidor con monitor de debugging avanzado
npm run dev:debug

# Opción 3: Ver estadísticas de errores
npm run debug:stats
```

### **Acceder al Panel de Debugging**

1. **Atajo de Teclado**: `Ctrl + Shift + D`
2. **Consola del Navegador**: 
   ```javascript
   // Abrir panel
   window.volcanoDebug.manager.showPanel = true;
   
   // Ver logs
   window.volcanoDebug.getLogs();
   
   // Ver errores
   window.volcanoDebug.getErrors();
   
   // Exportar datos
   window.volcanoDebug.export();
   ```

### **Logging Manual**

```javascript
import { debug } from './utils/debug';

// Diferentes niveles de logging
debug.error('Error crítico', errorData, 'api');
debug.warn('Advertencia importante', warnData, 'ui');
debug.info('Información general', infoData, 'general');
debug.log('Debug detallado', debugData, 'react');
```

### **Categorías Disponibles**
- `react` - Errores y logs de componentes React
- `api` - Errores de llamadas a API y red
- `ui` - Problemas de interfaz de usuario
- `network` - Problemas de conectividad
- `general` - Logs generales de la aplicación

## 📊 Interpretación de Errores Comunes

### **Errores de Select.Item**
```
Error: A Select.Item must have a value prop that is not an empty string
```
**Solución**: Verificar que todos los `Select.Item` tengan una prop `value` válida.

### **Errores de Módulos**
```
Error: Failed to resolve module './components/...'
```
**Solución**: Verificar rutas de imports y existencia de archivos.

### **Errores de TypeScript**
```
Error: Type 'string' is not assignable to type 'number'
```
**Solución**: Revisar tipos y interfaces en el código.

### **Errores de Red**
```
Error: ECONNREFUSED localhost:3001
```
**Solución**: Verificar que el backend esté ejecutándose.

## 🔍 Análisis de Logs

### **Estructura de un Log**
```json
{
  "timestamp": "2024-01-15T10:30:00.000Z",
  "level": "error",
  "category": "api",
  "message": "Error al cargar datos",
  "data": { "status": 500, "url": "/api/alerts" },
  "stack": "Error: ...",
  "userAgent": "Mozilla/5.0...",
  "url": "http://localhost:3000/dashboard"
}
```

### **Filtros Útiles**
```javascript
// Solo errores de los últimos 5 minutos
const recentErrors = window.volcanoDebug.getLogs({
  level: 'error',
  since: new Date(Date.now() - 5 * 60 * 1000).toISOString()
});

// Solo logs de API
const apiLogs = window.volcanoDebug.getLogs({
  category: 'api'
});
```

## 📁 Archivos de Log

### **Ubicación de Logs del Servidor**
```
backoffice/frontend/logs/
├── vite-debug.log      # Logs generales del servidor
├── vite-errors.log     # Errores estructurados
└── debug-YYYY-MM-DD.json  # Exports de debugging
```

### **Formato de Logs del Servidor**
```
[2024-01-15T10:30:00.000Z] [ERROR] Error message here
[2024-01-15T10:30:01.000Z] [INFO] Server started on port 3000
```

## 🚨 Resolución de Problemas

### **El Panel de Debugging No Abre**
1. Verificar que los imports estén correctos
2. Comprobar la consola del navegador por errores
3. Intentar `window.volcanoDebug` en la consola

### **No Se Capturan Errores**
1. Verificar que `import { debug } from './utils/debug'` esté presente
2. Comprobar que el sistema esté habilitado en desarrollo
3. Revisar la configuración de Vite

### **Logs No Se Guardan**
1. Verificar permisos de escritura en `/logs`
2. Comprobar que el script de monitor esté ejecutándose
3. Revisar la configuración del sistema de archivos

## 🔧 Configuración Avanzada

### **Variables de Entorno**
```bash
# Habilitar debugging en producción (NO recomendado)
VITE_DEBUG=true

# Nivel de logging
VITE_LOG_LEVEL=debug

# Máximo número de logs en memoria
VITE_MAX_LOGS=1000
```

### **Personalización del Debug Manager**
```javascript
import { debugManager } from './utils/debug';

// Configurar máximo de logs
debugManager.maxLogs = 2000;

// Habilitar/deshabilitar
debugManager.isEnabled = true;

// Limpiar logs
debugManager.clearLogs();
```

## 📈 Mejores Prácticas

1. **Usar categorías apropiadas** para facilitar el filtrado
2. **Incluir contexto relevante** en los datos del log
3. **No loggear información sensible** (passwords, tokens)
4. **Exportar logs regularmente** para análisis offline
5. **Revisar estadísticas** para identificar patrones de errores

## 🤝 Colaboración

### **Reportar Errores**
1. Exportar logs del período relevante
2. Incluir pasos para reproducir el error
3. Adjuntar información del entorno (browser, OS)
4. Proporcionar el ID del error si está disponible

### **Análisis de Errores en Equipo**
1. Usar `npm run debug:stats` para obtener resumen
2. Compartir archivos de log exportados
3. Documentar soluciones encontradas
4. Actualizar esta guía con nuevos patrones

---

## 📞 Soporte

Si encuentras problemas con el sistema de debugging o necesitas ayuda:

1. Revisar esta guía primero
2. Comprobar logs del servidor con `npm run debug:stats`
3. Exportar logs del cliente para análisis
4. Contactar al equipo de desarrollo con información detallada

**¡El debugging efectivo es clave para un desarrollo exitoso!** 🚀
