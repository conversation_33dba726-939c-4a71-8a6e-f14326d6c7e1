#!/usr/bin/env node

/**
 * 🔍 Diagnóstico Automático de Red para Volcano App
 * 
 * Este script diagnostica automáticamente problemas de conectividad
 * entre la aplicación móvil y el backend.
 * 
 * Uso: node scripts/diagnose-network.js
 */

const { exec } = require('child_process');
const os = require('os');
const fs = require('fs');
const path = require('path');

// Colores para la consola
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m'
};

function log(message, color = 'white') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function getLocalIP() {
  const interfaces = os.networkInterfaces();
  for (const name of Object.keys(interfaces)) {
    for (const interface of interfaces[name]) {
      if (interface.family === 'IPv4' && !interface.internal) {
        return interface.address;
      }
    }
  }
  return null;
}

function execPromise(command) {
  return new Promise((resolve) => {
    exec(command, (error, stdout, stderr) => {
      resolve({ error, stdout, stderr });
    });
  });
}

async function checkPort(port) {
  const command = process.platform === 'win32' 
    ? `netstat -an | findstr :${port}`
    : `netstat -an | grep :${port}`;
    
  const result = await execPromise(command);
  return result.stdout.includes(`:${port}`);
}

async function checkHTTP(url) {
  const command = process.platform === 'win32'
    ? `curl -s -o nul -w "%{http_code}" ${url}`
    : `curl -s -o /dev/null -w "%{http_code}" ${url}`;
    
  const result = await execPromise(command);
  return result.stdout.trim() === '200';
}

function checkFileExists(filePath) {
  return fs.existsSync(path.join(__dirname, '..', filePath));
}

function readFileContent(filePath) {
  try {
    return fs.readFileSync(path.join(__dirname, '..', filePath), 'utf8');
  } catch (error) {
    return null;
  }
}

function analyzeApiConfig() {
  const apiContent = readFileContent('services/api.ts');
  if (!apiContent) return { exists: false };
  
  const hasGetBaseURL = apiContent.includes('getBaseURL');
  const hasPlatformDetection = apiContent.includes('Platform.OS');
  const hasHostUri = apiContent.includes('Constants.expoConfig?.hostUri');
  
  return {
    exists: true,
    hasGetBaseURL,
    hasPlatformDetection,
    hasHostUri,
    configured: hasGetBaseURL && hasPlatformDetection && hasHostUri
  };
}

function analyzeBackendConfig() {
  const envContent = readFileContent('backoffice/backend/.env');
  if (!envContent) return { exists: false };
  
  const hostMatch = envContent.match(/HOST=(.+)/);
  const portMatch = envContent.match(/PORT=(.+)/);
  const corsMatch = envContent.match(/CORS_ORIGIN=(.+)/);
  
  return {
    exists: true,
    host: hostMatch ? hostMatch[1].trim() : 'localhost',
    port: portMatch ? portMatch[1].trim() : '3001',
    cors: corsMatch ? corsMatch[1].trim() : '',
    hostIsCorrect: hostMatch && (hostMatch[1].trim() === '0.0.0.0' || hostMatch[1].includes('192.168'))
  };
}

async function main() {
  log('\n🔍 DIAGNÓSTICO DE RED - VOLCANO APP\n', 'cyan');
  
  // 1. Información del sistema
  log('📋 INFORMACIÓN DEL SISTEMA', 'blue');
  const localIP = getLocalIP();
  log(`   OS: ${os.platform()} ${os.arch()}`);
  log(`   IP Local: ${localIP || 'No detectada'}`, localIP ? 'green' : 'red');
  
  // 2. Verificar archivos de configuración
  log('\n📁 ARCHIVOS DE CONFIGURACIÓN', 'blue');
  const apiConfig = analyzeApiConfig();
  const backendConfig = analyzeBackendConfig();
  
  log(`   services/api.ts: ${apiConfig.exists ? '✅' : '❌'}`, apiConfig.exists ? 'green' : 'red');
  if (apiConfig.exists) {
    log(`     - Detección de plataforma: ${apiConfig.hasPlatformDetection ? '✅' : '❌'}`, apiConfig.hasPlatformDetection ? 'green' : 'red');
    log(`     - Configuración dinámica: ${apiConfig.configured ? '✅' : '❌'}`, apiConfig.configured ? 'green' : 'red');
  }
  
  log(`   backend/.env: ${backendConfig.exists ? '✅' : '❌'}`, backendConfig.exists ? 'green' : 'red');
  if (backendConfig.exists) {
    log(`     - HOST: ${backendConfig.host}`, backendConfig.hostIsCorrect ? 'green' : 'red');
    log(`     - PORT: ${backendConfig.port}`);
    log(`     - CORS configurado: ${backendConfig.cors ? '✅' : '❌'}`, backendConfig.cors ? 'green' : 'red');
  }
  
  // 3. Verificar puertos
  log('\n🔌 PUERTOS Y CONECTIVIDAD', 'blue');
  const port = backendConfig.port || '3002';
  const portOpen = await checkPort(port);
  log(`   Puerto ${port} abierto: ${portOpen ? '✅' : '❌'}`, portOpen ? 'green' : 'red');
  
  // 4. Verificar conectividad HTTP
  if (portOpen && localIP) {
    log('\n🌐 CONECTIVIDAD HTTP', 'blue');
    
    const localhostHealth = await checkHTTP(`http://localhost:${port}/health`);
    log(`   localhost:${port}/health: ${localhostHealth ? '✅' : '❌'}`, localhostHealth ? 'green' : 'red');
    
    const networkHealth = await checkHTTP(`http://${localIP}:${port}/health`);
    log(`   ${localIP}:${port}/health: ${networkHealth ? '✅' : '❌'}`, networkHealth ? 'green' : 'red');
    
    const localhostAPI = await checkHTTP(`http://localhost:${port}/api/mobile/health`);
    log(`   localhost:${port}/api/mobile/health: ${localhostAPI ? '✅' : '❌'}`, localhostAPI ? 'green' : 'red');
    
    const networkAPI = await checkHTTP(`http://${localIP}:${port}/api/mobile/health`);
    log(`   ${localIP}:${port}/api/mobile/health: ${networkAPI ? '✅' : '❌'}`, networkAPI ? 'green' : 'red');
  }
  
  // 5. Diagnóstico y recomendaciones
  log('\n🎯 DIAGNÓSTICO Y RECOMENDACIONES', 'yellow');
  
  const issues = [];
  const fixes = [];
  
  if (!localIP) {
    issues.push('No se pudo detectar la IP local');
    fixes.push('Verificar conexión de red');
  }
  
  if (!apiConfig.configured) {
    issues.push('API service no configurado para móvil');
    fixes.push('Implementar detección de plataforma en services/api.ts');
  }
  
  if (!backendConfig.hostIsCorrect) {
    issues.push(`Backend HOST=${backendConfig.host} no permite conexiones de red`);
    fixes.push('Cambiar HOST=0.0.0.0 en backend/.env');
  }
  
  if (!portOpen) {
    issues.push('Backend no está corriendo o puerto cerrado');
    fixes.push('Ejecutar: cd backoffice/backend && npm run dev');
  }
  
  if (backendConfig.cors && localIP && !backendConfig.cors.includes(localIP)) {
    issues.push('CORS no incluye la IP local');
    fixes.push(`Agregar http://${localIP}:8081 a CORS_ORIGIN`);
  }
  
  if (issues.length === 0) {
    log('   🎉 ¡No se detectaron problemas!', 'green');
    log('   La configuración parece correcta para desarrollo móvil.', 'green');
  } else {
    log(`   ⚠️  Se detectaron ${issues.length} problema(s):`, 'red');
    issues.forEach((issue, i) => {
      log(`   ${i + 1}. ${issue}`, 'red');
    });
    
    log('\n🔧 SOLUCIONES RECOMENDADAS:', 'yellow');
    fixes.forEach((fix, i) => {
      log(`   ${i + 1}. ${fix}`, 'yellow');
    });
  }
  
  // 6. Comandos útiles
  log('\n📋 COMANDOS ÚTILES', 'blue');
  log('   Verificar puertos: netstat -an | findstr :3002');
  log('   Probar API: curl http://localhost:3002/api/mobile/health');
  if (localIP) {
    log(`   Probar red: curl http://${localIP}:3002/api/mobile/health`);
  }
  log('   Iniciar backend: cd backoffice/backend && npm run dev');
  log('   Iniciar móvil: npx expo start');
  
  log('\n📚 DOCUMENTACIÓN:', 'blue');
  log('   README_MOBILE_NETWORK.md - Solución rápida');
  log('   docs/MOBILE_NETWORK_CONFIGURATION.md - Configuración detallada');
  log('   docs/DEVELOPMENT_SETUP_GUIDE.md - Guía completa de setup');
  
  log('\n✅ Diagnóstico completado.\n', 'cyan');
}

// Ejecutar diagnóstico
main().catch((error) => {
  log(`❌ Error durante el diagnóstico: ${error.message}`, 'red');
  process.exit(1);
});
