/**
 * 🌋 Volcano App Backend - Rutas para App Móvil
 * Endpoints específicos para la aplicación móvil
 */

import { Router } from 'express';
import { body } from 'express-validator';
import { handleValidationErrors } from '@/middleware/validation';
import { asyncHandler } from '@/middleware/errorHandler';

// Importar controladores
import {
  getCurrentAlert,
  getAllZones,
  reportLocation,
  checkLocationInZones,
  getMobileConfig,
  bulkSync,
  checkAppVersion,
  batchReportLocation
} from '@/controllers/mobile';

// =====================================================
// ROUTER MÓVIL
// =====================================================

const router = Router();

// =====================================================
// VALIDACIONES ESPECÍFICAS PARA MÓVIL
// =====================================================

const validateLocationReport = [
  body('anonymous_id')
    .notEmpty()
    .withMessage('Anonymous ID is required')
    .isLength({ min: 10, max: 100 })
    .withMessage('Anonymous ID must be between 10 and 100 characters'),
  body('latitude')
    .isFloat({ min: -90, max: 90 })
    .withMessage('Latitude must be between -90 and 90'),
  body('longitude')
    .isFloat({ min: -180, max: 180 })
    .withMessage('Longitude must be between -180 and 180'),
  body('accuracy')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Accuracy must be a positive number'),
  body('app_version')
    .optional()
    .isLength({ max: 20 })
    .withMessage('App version must not exceed 20 characters'),
  body('device_type')
    .optional()
    .isIn(['ios', 'android', 'web'])
    .withMessage('Device type must be ios, android, or web'),
  handleValidationErrors()
];

const validateLocationCheck = [
  body('latitude')
    .isFloat({ min: -90, max: 90 })
    .withMessage('Latitude must be between -90 and 90'),
  body('longitude')
    .isFloat({ min: -180, max: 180 })
    .withMessage('Longitude must be between -180 and 180'),
  handleValidationErrors()
];

// =====================================================
// RUTAS PÚBLICAS (SIN AUTENTICACIÓN ESTRICTA)
// =====================================================

/**
 * Obtener alerta volcánica actual
 * GET /mobile/alerts/current
 */
router.get('/alerts/current', 
  asyncHandler(getCurrentAlert)
);

/**
 * Obtener todas las zonas de seguridad
 * GET /mobile/zones/all
 */
router.get('/zones/all', 
  asyncHandler(getAllZones)
);

/**
 * Obtener configuración para app móvil
 * GET /mobile/config
 */
router.get('/config', 
  asyncHandler(getMobileConfig)
);

// =====================================================
// RUTAS DE UBICACIÓN
// =====================================================

/**
 * Reportar ubicación de usuario anónimo
 * POST /mobile/location/report
 */
router.post('/location/report', 
  validateLocationReport,
  asyncHandler(reportLocation)
);

/**
 * Verificar ubicación en zonas de seguridad
 * POST /mobile/location/check
 */
router.post('/location/check', 
  validateLocationCheck,
  asyncHandler(checkLocationInZones)
);

// =====================================================
// NUEVAS RUTAS PARA SINCRONIZACIÓN Y OPTIMIZACIÓN
// =====================================================

/**
 * Sincronización masiva para apps móviles
 * POST /mobile/sync
 */
router.post('/sync', [
  body('lastSync').optional().isISO8601().withMessage('lastSync must be a valid ISO 8601 date'),
  body('deviceInfo').optional().isObject().withMessage('deviceInfo must be an object'),
  body('appVersion').optional().isString().withMessage('appVersion must be a string'),
  body('requestedData').optional().isArray().withMessage('requestedData must be an array'),
  handleValidationErrors
], asyncHandler(bulkSync));

/**
 * Verificar compatibilidad de versión de app
 * GET /mobile/version/check
 */
router.get('/version/check', asyncHandler(checkAppVersion));

/**
 * Reportar ubicaciones en lote (para sincronización offline)
 * POST /mobile/location/batch
 */
router.post('/location/batch', [
  body('locations').isArray({ min: 1, max: 100 }).withMessage('locations must be an array with 1-100 items'),
  body('locations.*.anonymous_id').notEmpty().withMessage('anonymous_id is required for each location'),
  body('locations.*.latitude').isFloat({ min: -90, max: 90 }).withMessage('latitude must be between -90 and 90'),
  body('locations.*.longitude').isFloat({ min: -180, max: 180 }).withMessage('longitude must be between -180 and 180'),
  body('locations.*.accuracy').optional().isFloat({ min: 0 }).withMessage('accuracy must be a positive number'),
  body('locations.*.timestamp').optional().isISO8601().withMessage('timestamp must be a valid ISO 8601 date'),
  body('deviceInfo').optional().isObject().withMessage('deviceInfo must be an object'),
  handleValidationErrors
], asyncHandler(batchReportLocation));

// =====================================================
// RUTAS DE INFORMACIÓN
// =====================================================

/**
 * Información sobre API móvil
 * GET /mobile/_info
 */
router.get('/_info', (req, res) => {
  res.json({
    success: true,
    data: {
      name: 'Volcano App Mobile API',
      version: '1.0.0',
      endpoints: {
        alerts: {
          current: 'GET /api/mobile/alerts/current'
        },
        zones: {
          all: 'GET /api/mobile/zones/all'
        },
        location: {
          report: 'POST /api/mobile/location/report',
          check: 'POST /api/mobile/location/check',
          batch: 'POST /api/mobile/location/batch'
        },
        sync: {
          bulk: 'POST /api/mobile/sync'
        },
        version: {
          check: 'GET /api/mobile/version/check'
        },
        config: 'GET /api/mobile/config'
      },
      authentication: {
        required: false,
        note: 'Most endpoints are public for mobile app access'
      },
      rate_limiting: {
        enabled: true,
        window: '15 minutes',
        max_requests: 100
      },
      location: {
        coordinate_system: 'WGS84 (EPSG:4326)',
        accuracy_unit: 'meters',
        anonymous_tracking: true,
        data_retention: '24 hours'
      },
      safety_status: {
        values: ['safe', 'danger', 'evacuation', 'emergency', 'outside', 'unknown'],
        descriptions: {
          safe: 'User is in a designated safe zone',
          danger: 'User is in a danger zone - immediate evacuation required',
          evacuation: 'User is in an evacuation zone - follow evacuation procedures',
          emergency: 'User is in an emergency zone - stay alert',
          outside: 'User is outside defined safety zones',
          unknown: 'Unable to determine safety status'
        }
      }
    },
    timestamp: new Date()
  });
});

/**
 * Health check específico para móvil
 * GET /mobile/health
 */
router.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    service: 'mobile-api',
    timestamp: new Date(),
    uptime: process.uptime()
  });
});

// =====================================================
// EXPORTACIÓN
// =====================================================

export default router;
