{"name": "volcano-app-frontend", "version": "1.0.0", "description": "Frontend administrativo para el sistema de gestión de Volcano App - Usando shadcn/ui", "private": true, "scripts": {"dev": "vite", "dev:debug": "node scripts/debug-monitor.js", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint src --ext ts,tsx --fix", "type-check": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:watch": "vitest --watch", "test:ci": "vitest run --coverage --reporter=verbose", "test:components": "vitest --run src/components", "test:hooks": "vitest --run src/hooks", "test:services": "vitest --run src/services", "test:contexts": "vitest --run src/contexts", "test:utils": "vitest --run src/utils", "debug:stats": "node scripts/debug-monitor.js --stats"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-query": "^5.8.4", "@tanstack/react-query-devtools": "^5.8.4", "axios": "^1.6.2", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "cmdk": "^0.2.0", "date-fns": "^2.30.0", "leaflet": "^1.9.4", "leaflet-draw": "^1.0.4", "lucide-react": "^0.294.0", "react": "^18.2.0", "react-day-picker": "^8.10.1", "react-dom": "^18.2.0", "react-hook-form": "^7.56.4", "react-leaflet": "^4.2.1", "react-router-dom": "^6.20.1", "recharts": "^2.8.0", "sonner": "^1.4.0", "tailwind-merge": "^2.0.0", "vaul": "^0.9.0", "zod": "^3.25.46"}, "devDependencies": {"@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@types/leaflet": "^1.9.18", "@types/leaflet-draw": "^1.0.8", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "@vitest/ui": "^0.34.6", "autoprefixer": "^10.4.21", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "tailwindcss-animate": "^1.0.7", "typescript": "^5.2.2", "vite": "^5.0.0", "vitest": "^0.34.6"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}