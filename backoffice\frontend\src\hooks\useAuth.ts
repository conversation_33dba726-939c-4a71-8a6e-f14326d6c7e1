/**
 * 🌋 Volcano App Frontend - Hook de Autenticación
 * Gestión del estado de autenticación y tokens
 */

import { useState, useEffect, createContext, useContext } from 'react';

interface User {
  id: string;
  email: string;
  full_name: string;
  role: string;
}

interface AuthTokens {
  access_token: string;
  refresh_token: string;
  expires_in: number;
}

interface AuthContextType {
  user: User | null;
  tokens: AuthTokens | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (tokens: AuthTokens) => void;
  logout: () => void;
  refreshToken: () => Promise<boolean>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

export function useAuthState() {
  const [user, setUser] = useState<User | null>(null);
  const [tokens, setTokens] = useState<AuthTokens | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Cargar tokens del localStorage al iniciar
  useEffect(() => {
    const savedTokens = localStorage.getItem('volcano_auth_tokens');
    if (savedTokens) {
      try {
        const parsedTokens = JSON.parse(savedTokens);
        setTokens(parsedTokens);
        // Aquí podrías validar el token y obtener info del usuario
        fetchUserProfile(parsedTokens.access_token);
      } catch (error) {
        console.error('Error parsing saved tokens:', error);
        localStorage.removeItem('volcano_auth_tokens');
      }
    }
    setIsLoading(false);
  }, []);

  const fetchUserProfile = async (accessToken: string) => {
    try {
      const response = await fetch('/auth/me', {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
        },
      });

      if (response.ok) {
        const result = await response.json();
        setUser(result.data);
      } else {
        // Token inválido, limpiar estado
        logout();
      }
    } catch (error) {
      console.error('Error fetching user profile:', error);
      logout();
    }
  };

  const login = (newTokens: AuthTokens) => {
    setTokens(newTokens);
    localStorage.setItem('volcano_auth_tokens', JSON.stringify(newTokens));
    fetchUserProfile(newTokens.access_token);
  };

  const logout = () => {
    setUser(null);
    setTokens(null);
    localStorage.removeItem('volcano_auth_tokens');
  };

  const refreshToken = async (): Promise<boolean> => {
    if (!tokens?.refresh_token) return false;

    try {
      const response = await fetch('/auth/refresh', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          refresh_token: tokens.refresh_token,
        }),
      });

      if (response.ok) {
        const result = await response.json();
        login(result.data.tokens);
        return true;
      } else {
        logout();
        return false;
      }
    } catch (error) {
      console.error('Error refreshing token:', error);
      logout();
      return false;
    }
  };

  return {
    user,
    tokens,
    isAuthenticated: !!user && !!tokens,
    isLoading,
    login,
    logout,
    refreshToken,
  };
}
