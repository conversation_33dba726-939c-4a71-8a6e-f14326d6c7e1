# Migración a shadcn/ui - Volcano App Frontend

## 📋 Resumen

Este proyecto ha sido migrado exitosamente de usar Radix UI directamente a **shadcn/ui**, un sistema de componentes moderno que proporciona componentes pre-configurados, accesibles y personalizables.

## 🎯 ¿Qué es shadcn/ui?

shadcn/ui es una colección de componentes reutilizables construidos usando:
- **Radix UI** (primitivos accesibles)
- **Tailwind CSS** (estilos)
- **class-variance-authority** (variantes de componentes)
- **TypeScript** (tipado fuerte)

## 🔄 Cambios Realizados

### Dependencias Actualizadas
- ✅ Removidas dependencias directas innecesarias (`@headlessui/react`, `@heroicons/react`, `react-hot-toast`, `yup`)
- ✅ Mantenidas dependencias de Radix UI (gestionadas automáticamente por shadcn/ui)
- ✅ Agregadas dependencias core de shadcn/ui

### Componentes Migrados
- ✅ **Accordion** - Componentes de acordeón
- ✅ **Alert Dialog** - Diálogos de alerta
- ✅ **Avatar** - Avatares de usuario
- ✅ **Badge** - Insignias y etiquetas
- ✅ **Button** - Botones (actualizado)
- ✅ **Card** - Tarjetas de contenido
- ✅ **Checkbox** - Casillas de verificación
- ✅ **Dialog** - Diálogos modales (actualizado)
- ✅ **Dropdown Menu** - Menús desplegables
- ✅ **Input** - Campos de entrada
- ✅ **Label** - Etiquetas de formulario
- ✅ **Popover** - Componentes emergentes
- ✅ **Progress** - Barras de progreso
- ✅ **Scroll Area** - Áreas de desplazamiento
- ✅ **Select** - Selectores (actualizado)
- ✅ **Separator** - Separadores
- ✅ **Switch** - Interruptores
- ✅ **Table** - Tablas
- ✅ **Tabs** - Pestañas
- ✅ **Textarea** - Áreas de texto
- ✅ **Toast** - Notificaciones
- ✅ **Tooltip** - Información sobre herramientas

### Hooks y Utilidades
- ✅ **use-toast** - Hook para gestionar notificaciones
- ✅ **Toaster** - Componente contenedor de toasts

## 🚀 Uso de Componentes

### Importación
```typescript
import { Button } from "@/components/ui/button"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { useToast } from "@/hooks/use-toast"
```

### Ejemplo de Uso
```typescript
function MyComponent() {
  const { toast } = useToast()

  return (
    <div>
      <Button 
        variant="default" 
        onClick={() => toast({ title: "¡Éxito!", description: "Operación completada" })}
      >
        Mostrar Toast
      </Button>
    </div>
  )
}
```

## 🎨 Personalización

### Variables CSS
Los componentes usan variables CSS definidas en `src/index.css`:
```css
:root {
  --background: 0 0% 100%;
  --foreground: 240 10% 3.9%;
  --primary: 240 5.9% 10%;
  /* ... más variables */
}
```

### Configuración
La configuración de shadcn/ui está en `components.json`:
```json
{
  "style": "new-york",
  "rsc": false,
  "tsx": true,
  "tailwind": {
    "config": "tailwind.config.js",
    "css": "src/index.css",
    "baseColor": "zinc",
    "cssVariables": true
  },
  "aliases": {
    "components": "@/components",
    "utils": "@/lib/utils",
    "ui": "@/components/ui"
  }
}
```

## 📦 Agregar Nuevos Componentes

Para agregar un nuevo componente de shadcn/ui:

```bash
npx shadcn@latest add [component-name]
```

Ejemplos:
```bash
npx shadcn@latest add calendar
npx shadcn@latest add form
npx shadcn@latest add data-table
```

## 🔧 Comandos Útiles

```bash
# Desarrollo
npm run dev

# Build
npm run build

# Type checking
npm run type-check

# Linting
npm run lint
npm run lint:fix

# Tests
npm run test
npm run test:ui
npm run test:coverage
```

## 📚 Recursos

- [Documentación oficial de shadcn/ui](https://ui.shadcn.com/)
- [Componentes disponibles](https://ui.shadcn.com/docs/components)
- [Guía de personalización](https://ui.shadcn.com/docs/theming)
- [Radix UI Primitives](https://www.radix-ui.com/primitives)

## ✅ Estado de la Migración

- ✅ **Dependencias**: Limpiadas y organizadas
- ✅ **Componentes**: Todos migrados a shadcn/ui
- ✅ **TypeScript**: Sin errores
- ✅ **Build**: Compilación exitosa
- ✅ **Desarrollo**: Servidor funcionando
- ✅ **Documentación**: Actualizada

---

**Fecha de migración**: Junio 2024  
**Versión de shadcn/ui**: 2.6.0  
**Estado**: ✅ Completado
