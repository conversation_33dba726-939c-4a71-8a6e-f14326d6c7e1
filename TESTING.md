# 🌋 Volcano App - Guía de Testing

Esta guía describe la estrategia de testing completa para el proyecto Volcano App, incluyendo tests unitarios, de integración y end-to-end.

## 📋 Índice

- [Arquitectura de Testing](#arquitectura-de-testing)
- [Configuración](#configuración)
- [Ejecutar Tests](#ejecutar-tests)
- [Cobertura de Código](#cobertura-de-código)
- [Frameworks y Herramientas](#frameworks-y-herramientas)
- [Estructura de Tests](#estructura-de-tests)
- [Mejores Prácticas](#mejores-prácticas)
- [CI/CD](#cicd)

## 🏗️ Arquitectura de Testing

### Componentes del Sistema

```
volcanoApp/
├── 📱 Mobile App (React Native + Expo)
│   ├── Jest + React Native Testing Library
│   └── Tests: components, hooks, services, navigation
├── 🖥️ Frontend Backoffice (React + Vite)
│   ├── Vitest + React Testing Library
│   └── Tests: components, hooks, services, contexts
└── ⚙️ Backend API (Node.js + Express)
    ├── Jest + Supertest
    └── Tests: controllers, middleware, services, routes
```

### Tipos de Tests

- **Unitarios**: Componentes, funciones, hooks individuales
- **Integración**: Flujos completos, APIs, servicios
- **E2E**: Flujos de usuario completos (futuro)

## ⚙️ Configuración

### Aplicación Móvil

```bash
# Instalar dependencias de testing
npm install --save-dev jest @testing-library/react-native @testing-library/jest-native

# Configuración en jest.config.js
preset: 'jest-expo'
setupFilesAfterEnv: ['<rootDir>/jest.setup.js']
```

### Frontend del Backoffice

```bash
# Instalar dependencias de testing
npm install --save-dev vitest @testing-library/react @testing-library/jest-dom

# Configuración en vitest.config.ts
environment: 'jsdom'
setupFiles: ['./src/test/setup.ts']
```

### Backend

```bash
# Instalar dependencias de testing
npm install --save-dev jest supertest @types/jest @types/supertest

# Configuración en jest.config.js
testEnvironment: 'node'
setupFilesAfterEnv: ['<rootDir>/src/tests/setup.ts']
```

## 🚀 Ejecutar Tests

### Comandos Principales

```bash
# Ejecutar todos los tests del proyecto
npm run test:all

# Ejecutar tests por componente
npm run test:all:mobile     # Solo aplicación móvil
npm run test:all:frontend   # Solo frontend del backoffice
npm run test:all:backend    # Solo backend

# Tests con cobertura
npm run test:coverage:all

# Tests en modo watch (desarrollo)
npm run test:watch          # En cada directorio
```

### Comandos Específicos por Componente

#### Aplicación Móvil
```bash
npm test                    # Tests básicos
npm run test:watch         # Modo watch
npm run test:coverage      # Con cobertura
npm run test:ci           # Para CI/CD
```

#### Frontend del Backoffice
```bash
cd backoffice/frontend
npm test                   # Tests básicos
npm run test:watch        # Modo watch
npm run test:coverage     # Con cobertura
npm run test:ui           # Interfaz visual de Vitest
npm run test:components   # Solo componentes
npm run test:hooks        # Solo hooks
npm run test:services     # Solo servicios
```

#### Backend
```bash
cd backoffice/backend
npm test                   # Tests básicos
npm run test:watch        # Modo watch
npm run test:coverage     # Con cobertura
npm run test:controllers  # Solo controladores
npm run test:services     # Solo servicios
npm run test:middleware   # Solo middleware
```

## 📊 Cobertura de Código

### Umbrales de Cobertura

| Componente | Líneas | Funciones | Ramas | Declaraciones |
|------------|--------|-----------|-------|---------------|
| Mobile App | 70%    | 70%       | 70%   | 70%          |
| Frontend   | 70%    | 70%       | 70%   | 70%          |
| Backend    | 70%    | 70%       | 70%   | 70%          |

### Reportes de Cobertura

```bash
# Generar reportes de cobertura
npm run test:coverage:all

# Ubicación de reportes
./coverage/                    # Mobile app
./backoffice/frontend/coverage/ # Frontend
./backoffice/backend/coverage/  # Backend
```

## 🛠️ Frameworks y Herramientas

### Aplicación Móvil
- **Jest**: Framework de testing principal
- **React Native Testing Library**: Testing de componentes
- **Jest Native**: Matchers adicionales para React Native
- **Expo Testing**: Utilidades específicas de Expo

### Frontend del Backoffice
- **Vitest**: Framework de testing (más rápido que Jest)
- **React Testing Library**: Testing de componentes React
- **Jest DOM**: Matchers para elementos DOM
- **User Event**: Simulación de interacciones de usuario

### Backend
- **Jest**: Framework de testing principal
- **Supertest**: Testing de APIs HTTP
- **Node Test**: Testing de servicios Node.js

## 📁 Estructura de Tests

### Aplicación Móvil
```
components/
├── __tests__/
│   ├── InteractiveMap.test.tsx
│   └── NotificationBanner.test.tsx
app/(tabs)/
├── __tests__/
│   ├── explore.test.tsx
│   └── index.test.tsx
services/
├── __tests__/
│   ├── api.test.ts
│   ├── notifications.test.ts
│   └── location.test.ts
hooks/
├── __tests__/
│   └── useApi.test.tsx
```

### Frontend del Backoffice
```
src/
├── components/
│   ├── zones/
│   │   └── __tests__/
│   │       └── ZonesTable.test.tsx
│   └── __tests__/
│       ├── ZoneModal.test.tsx
│       └── InteractiveMapPage.test.tsx
├── hooks/
│   └── __tests__/
│       └── useAuth.test.tsx
├── services/
│   └── __tests__/
│       └── supabase.test.ts
└── contexts/
    └── __tests__/
        └── AuthContext.test.tsx
```

### Backend
```
src/
├── tests/
│   ├── controllers/
│   │   ├── zones.test.ts
│   │   └── alerts.test.ts
│   ├── middleware/
│   │   └── auth.test.ts
│   ├── services/
│   │   └── websocket.test.ts
│   └── setup.ts
```

## ✅ Mejores Prácticas

### Nomenclatura
- Archivos de test: `*.test.ts` o `*.test.tsx`
- Describe blocks: Nombre del componente/función
- Test cases: Comportamiento esperado en tercera persona

### Estructura de Tests
```typescript
describe('ComponentName', () => {
  beforeEach(() => {
    // Setup común
  });

  it('should render correctly', () => {
    // Test de renderizado básico
  });

  it('should handle user interactions', () => {
    // Test de interacciones
  });

  it('should handle error states', () => {
    // Test de manejo de errores
  });
});
```

### Mocking
- Mock servicios externos (APIs, Supabase, etc.)
- Mock componentes pesados en tests unitarios
- Usar mocks específicos por test cuando sea necesario

### Assertions
- Usar matchers específicos y descriptivos
- Verificar tanto el estado como el comportamiento
- Incluir tests de casos edge y errores

## 🔄 CI/CD

### GitHub Actions

El proyecto incluye un workflow de CI/CD que:

1. **Ejecuta tests en paralelo** para cada componente
2. **Genera reportes de cobertura** individuales
3. **Consolida resultados** en un reporte final
4. **Falla el build** si algún test no pasa

### Configuración

```yaml
# .github/workflows/test.yml
- Mobile App Tests (Ubuntu)
- Frontend Tests (Ubuntu)  
- Backend Tests (Ubuntu + PostgreSQL)
- E2E Tests (Condicional)
- Code Quality Analysis
- Coverage Report Consolidation
```

### Variables de Entorno para CI

```bash
# Backend
NODE_ENV=test
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/volcano_test
JWT_SECRET=test-jwt-secret-for-ci
SUPABASE_URL=https://test.supabase.co
SUPABASE_SERVICE_ROLE_KEY=test-service-role-key
```

## 🚨 Troubleshooting

### Problemas Comunes

1. **Tests lentos**: Verificar mocks y configuración de timeout
2. **Fallos intermitentes**: Revisar async/await y timing
3. **Problemas de memoria**: Limpiar mocks en beforeEach/afterEach
4. **Errores de importación**: Verificar configuración de paths y aliases

### Debugging

```bash
# Ejecutar tests en modo debug
npm test -- --verbose
npm test -- --detectOpenHandles
npm test -- --forceExit

# Tests específicos
npm test -- --testNamePattern="should render correctly"
npm test -- ComponentName.test.tsx
```

## 📚 Recursos Adicionales

- [Jest Documentation](https://jestjs.io/docs/getting-started)
- [React Testing Library](https://testing-library.com/docs/react-testing-library/intro/)
- [Vitest Documentation](https://vitest.dev/guide/)
- [React Native Testing Library](https://callstack.github.io/react-native-testing-library/)
- [Supertest Documentation](https://github.com/visionmedia/supertest)

---

**Nota**: Esta guía se actualiza continuamente. Para contribuir con mejoras, por favor abre un issue o pull request.
