/**
 * 🌋 Volcano App Backend - Rutas de Auditoría
 * Endpoints para consulta de logs de auditoría del sistema
 */

import { Router } from 'express';
import { requireMinimumRole } from '@/middleware/auth';
import { 
  validateUUID,
  validatePagination
} from '@/middleware/validation';
import { asyncHandler } from '@/middleware/errorHandler';
import { UserRole } from '@/types';

// Importar controladores
import {
  getAuditLogs,
  getAuditLogById,
  getAuditStats,
  getRecentActivity
} from '@/controllers/audit';

// =====================================================
// ROUTER DE AUDITORÍA
// =====================================================

const router = Router();

// =====================================================
// RUTAS DE CONSULTA (REQUIEREN PERMISOS ADMIN)
// =====================================================

/**
 * Obtener logs de auditoría con filtros y paginación
 * GET /audit
 * Requiere rol mínimo: ADMIN
 */
router.get('/', 
  requireMinimumRole(UserRole.ADMIN),
  validatePagination,
  asyncHandler(getAuditLogs)
);

/**
 * Obtener estadísticas de auditoría
 * GET /audit/stats
 * Requiere rol mínimo: ADMIN
 */
router.get('/stats', 
  requireMinimumRole(UserRole.ADMIN),
  asyncHandler(getAuditStats)
);

/**
 * Obtener actividad reciente del sistema
 * GET /audit/recent
 * Requiere rol mínimo: OPERATOR
 */
router.get('/recent', 
  requireMinimumRole(UserRole.OPERATOR),
  asyncHandler(getRecentActivity)
);

/**
 * Obtener un log de auditoría específico por ID
 * GET /audit/:id
 * Requiere rol mínimo: ADMIN
 */
router.get('/:id', 
  requireMinimumRole(UserRole.ADMIN),
  validateUUID('id'),
  asyncHandler(getAuditLogById)
);

// =====================================================
// RUTAS DE INFORMACIÓN
// =====================================================

/**
 * Información sobre auditoría
 * GET /audit/_info
 */
router.get('/_info',
  requireMinimumRole(UserRole.OPERATOR),
  (req, res) => {
    res.json({
      success: true,
      data: {
        endpoints: {
          list: 'GET /api/audit',
          get: 'GET /api/audit/:id',
          stats: 'GET /api/audit/stats',
          recent: 'GET /api/audit/recent'
        },
        actions: [
          'CREATE',
          'UPDATE',
          'DELETE',
          'LOGIN',
          'LOGOUT'
        ],
        tables: [
          'volcano_alerts',
          'safety_zones',
          'admin_users',
          'system_config'
        ],
        permissions: {
          view_logs: 'ADMIN only',
          view_stats: 'ADMIN only',
          view_recent: 'OPERATOR or higher'
        },
        pagination: {
          default_page: 1,
          default_limit: 100,
          max_limit: 100
        },
        filters: {
          table_name: 'Filter by table name',
          action: 'Filter by action type',
          user_id: 'Filter by user ID',
          record_id: 'Filter by record ID',
          start_date: 'Filter by start date (ISO 8601)',
          end_date: 'Filter by end date (ISO 8601)'
        },
        sorting: {
          fields: ['created_at', 'action', 'table_name'],
          orders: ['asc', 'desc']
        }
      },
      timestamp: new Date()
    });
  }
);

// =====================================================
// EXPORTACIÓN
// =====================================================

export default router;
