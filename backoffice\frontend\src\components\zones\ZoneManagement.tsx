/**
 * 🌋 Volcano App Backoffice - Gestión de Zonas de Seguridad
 * Componente principal para la gestión completa de zonas de seguridad
 */

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import { Download, Filter, Plus, Search, Upload } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { ZoneModal } from '../ZoneModal';
import { InteractiveZoneMap } from './InteractiveZoneMap';
import { ZonesTable } from './ZonesTable';

// =====================================================
// TIPOS Y INTERFACES
// =====================================================

export interface Zone {
  id: string;
  name: string;
  description: string;
  zone_type: 'SAFE' | 'EMERGENCY' | 'DANGER' | 'EVACUATION' | 'RESTRICTED';
  geometry: {
    type: 'Polygon';
    coordinates: number[][][];
  };
  center_lat?: number;
  center_lng?: number;
  capacity?: number;
  contact_info?: any;
  facilities?: string[];
  is_active: boolean;
  version: number;
  created_at: string;
  updated_at: string;
  created_by?: {
    full_name: string;
    email: string;
  };
}

export interface ZoneFilters {
  search: string;
  zone_type: string;
  is_active: string;
  sort_by: string;
  sort_order: 'asc' | 'desc';
}

// =====================================================
// COMPONENTE PRINCIPAL
// =====================================================

export function ZoneManagement() {
  const { toast } = useToast();

  console.log('🎛️ ZoneManagement component rendered');

  // Estados
  const [zones, setZones] = useState<Zone[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedZone, setSelectedZone] = useState<Zone | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [activeTab, setActiveTab] = useState('table');
  
  // Filtros
  const [filters, setFilters] = useState<ZoneFilters>({
    search: '',
    zone_type: '',
    is_active: '',
    sort_by: 'created_at',
    sort_order: 'desc'
  });

  // Estadísticas
  const [stats, setStats] = useState({
    total: 0,
    active: 0,
    by_type: {} as Record<string, number>
  });

  // =====================================================
  // FUNCIONES DE API
  // =====================================================

  const fetchZones = async () => {
    try {
      setLoading(true);
      
      const queryParams = new URLSearchParams();
      if (filters.search) queryParams.append('search', filters.search);
      if (filters.zone_type) queryParams.append('zone_type', filters.zone_type);
      if (filters.is_active) queryParams.append('is_active', filters.is_active);
      queryParams.append('sort_by', filters.sort_by);
      queryParams.append('sort_order', filters.sort_order);
      queryParams.append('limit', '100'); // Obtener todas las zonas para el mapa

      const response = await fetch(`/api/zones?${queryParams}`);
      const data = await response.json();

      if (data.success) {
        setZones(data.data);
        calculateStats(data.data);
      } else {
        throw new Error(data.error || 'Failed to fetch zones');
      }
    } catch (error) {
      console.error('Error fetching zones:', error);
      toast({
        title: 'Error',
        description: 'No se pudieron cargar las zonas de seguridad',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const calculateStats = (zonesData: Zone[]) => {
    const total = zonesData.length;
    const active = zonesData.filter(z => z.is_active).length;
    const by_type = zonesData.reduce((acc, zone) => {
      acc[zone.zone_type] = (acc[zone.zone_type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    setStats({ total, active, by_type });
  };

  const handleCreateZone = (geometry?: any) => {
    console.log('🔍 handleCreateZone called with geometry:', geometry);
    setSelectedZone(null);
    setIsCreating(true);
    setIsModalOpen(true);

    // Si se proporciona geometría desde el mapa, podríamos usarla aquí
    if (geometry) {
      console.log('🔍 Geometry from map:', geometry);
      // TODO: Integrar geometría del mapa con el modal
    }
  };

  const handleEditZone = (zone: Zone, newGeometry?: any) => {
    console.log('🔍 handleEditZone called:', zone, newGeometry);
    setSelectedZone(zone);
    setIsCreating(false);
    setIsModalOpen(true);

    // Si se proporciona nueva geometría, podríamos actualizarla directamente
    if (newGeometry) {
      console.log('🔍 New geometry from map:', newGeometry);
      // TODO: Actualizar zona con nueva geometría
    }
  };

  const handleDeleteZone = async (zoneId: string) => {
    if (!confirm('¿Estás seguro de que quieres eliminar esta zona?')) {
      return;
    }

    try {
      const response = await fetch(`/api/zones/${zoneId}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (data.success) {
        toast({
          title: 'Éxito',
          description: 'Zona eliminada correctamente',
        });
        fetchZones();
      } else {
        throw new Error(data.error || 'Failed to delete zone');
      }
    } catch (error) {
      console.error('Error deleting zone:', error);
      toast({
        title: 'Error',
        description: 'No se pudo eliminar la zona',
        variant: 'destructive',
      });
    }
  };

  const handleZoneCreated = (newZone: Zone) => {
    setIsModalOpen(false);
    fetchZones();
    toast({
      title: 'Éxito',
      description: 'Zona creada correctamente',
    });
  };

  const handleZoneUpdated = (updatedZone: Zone) => {
    setIsModalOpen(false);
    fetchZones();
    toast({
      title: 'Éxito',
      description: 'Zona actualizada correctamente',
    });
  };

  // =====================================================
  // EFECTOS
  // =====================================================

  useEffect(() => {
    fetchZones();
  }, [filters]);

  // =====================================================
  // RENDER
  // =====================================================

  const getZoneTypeColor = (type: string) => {
    switch (type) {
      case 'SAFE': return 'bg-green-100 text-green-800';
      case 'EMERGENCY': return 'bg-blue-100 text-blue-800';
      case 'EVACUATION': return 'bg-yellow-100 text-yellow-800';
      case 'DANGER': return 'bg-red-100 text-red-800';
      case 'RESTRICTED': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Zonas de Seguridad</h1>
          <p className="text-muted-foreground">
            Gestiona las zonas de seguridad volcánica del sistema
          </p>
        </div>
        <Button onClick={handleCreateZone} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Nueva Zona
        </Button>
      </div>

      {/* Estadísticas */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total de Zonas</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Zonas Activas</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.active}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Zonas Seguras</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {stats.by_type.SAFE || 0}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Zonas de Peligro</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {stats.by_type.DANGER || 0}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filtros */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-4 w-4" />
            Filtros
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Buscar zonas..."
                value={filters.search}
                onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                className="pl-10"
              />
            </div>
            
            <Select
              value={filters.zone_type}
              onValueChange={(value) => setFilters(prev => ({ ...prev, zone_type: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Tipo de zona" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Todos los tipos</SelectItem>
                <SelectItem value="SAFE">Segura</SelectItem>
                <SelectItem value="EMERGENCY">Emergencia</SelectItem>
                <SelectItem value="EVACUATION">Evacuación</SelectItem>
                <SelectItem value="DANGER">Peligro</SelectItem>
                <SelectItem value="RESTRICTED">Restringida</SelectItem>
              </SelectContent>
            </Select>

            <Select
              value={filters.is_active}
              onValueChange={(value) => setFilters(prev => ({ ...prev, is_active: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Estado" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Todos los estados</SelectItem>
                <SelectItem value="true">Activas</SelectItem>
                <SelectItem value="false">Inactivas</SelectItem>
              </SelectContent>
            </Select>

            <div className="flex gap-2">
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Exportar
              </Button>
              <Button variant="outline" size="sm">
                <Upload className="h-4 w-4 mr-2" />
                Importar
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Contenido Principal */}
      <Tabs value={activeTab} onValueChange={(value) => {
        console.log('🔄 Changing tab to:', value);
        setActiveTab(value);
      }}>
        <TabsList>
          <TabsTrigger value="table">Vista de Tabla</TabsTrigger>
          <TabsTrigger value="map">Vista de Mapa</TabsTrigger>
        </TabsList>

        <TabsContent value="table" className="space-y-4">
          <ZonesTable
            zones={zones}
            onCreateZone={handleCreateZone}
            onEditZone={handleEditZone}
            onDeleteZone={handleDeleteZone}
          />
        </TabsContent>

        <TabsContent value="map" className="space-y-4">
          {console.log('🗺️ Rendering map tab content with zones:', zones.length)}
          <InteractiveZoneMap
            zones={zones}
            onZoneCreate={handleCreateZone}
            onZoneEdit={handleEditZone}
            onZoneDelete={handleDeleteZone}
            onRefresh={fetchZones}
          />
        </TabsContent>
      </Tabs>

      {/* Modal de Zona */}
      <ZoneModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        zone={selectedZone}
        isCreating={isCreating}
        onZoneCreated={handleZoneCreated}
        onZoneUpdated={handleZoneUpdated}
      />
    </div>
  );
}

export default ZoneManagement;
