/**
 * 🌋 Volcano App Backend - Controlador de Configuración del Sistema
 * Gestión de configuraciones del sistema
 */

import { Response } from 'express';
import { supabaseAdmin, getSystemConfig, updateSystemConfig } from '@/services/supabase';
import { logAuditAction } from '@/services/supabase';
import { logger } from '@/utils/logger';
import { AuthenticatedRequest } from '@/types';

// =====================================================
// OBTENER TODAS LAS CONFIGURACIONES
// =====================================================

/**
 * Obtener todas las configuraciones del sistema
 * GET /api/config
 */
export async function getAllConfigs(req: AuthenticatedRequest, res: Response) {
  try {
    const { data: configs, error } = await supabaseAdmin
      .from('system_config')
      .select('*')
      .order('key', { ascending: true });

    if (error) {
      throw error;
    }

    res.json({
      success: true,
      data: configs || [],
      timestamp: new Date()
    });

  } catch (error) {
    logger.error('Get all configs error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get system configuration',
      timestamp: new Date()
    });
  }
}

// =====================================================
// OBTENER CONFIGURACIÓN POR CLAVE
// =====================================================

/**
 * Obtener una configuración específica por clave
 * GET /api/config/:key
 */
export async function getConfigByKey(req: AuthenticatedRequest, res: Response) {
  try {
    const { key } = req.params;

    const value = await getSystemConfig(key);

    if (value === null) {
      return res.status(404).json({
        success: false,
        error: 'Configuration not found',
        timestamp: new Date()
      });
    }

    res.json({
      success: true,
      data: {
        key,
        value
      },
      timestamp: new Date()
    });

  } catch (error) {
    logger.error('Get config by key error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get configuration',
      timestamp: new Date()
    });
  }
}

// =====================================================
// ACTUALIZAR CONFIGURACIÓN
// =====================================================

/**
 * Actualizar o crear una configuración
 * PUT /api/config/:key
 */
export async function updateConfig(req: AuthenticatedRequest, res: Response) {
  try {
    const { key } = req.params;
    const { value, description } = req.body;

    if (value === undefined) {
      return res.status(400).json({
        success: false,
        error: 'Value is required',
        timestamp: new Date()
      });
    }

    // Obtener valor anterior para auditoría
    const oldValue = await getSystemConfig(key);

    // Actualizar configuración
    await updateSystemConfig(key, value, req.user?.id);

    // Si se proporciona descripción, actualizarla también
    if (description !== undefined) {
      const { error: descError } = await supabaseAdmin
        .from('system_config')
        .update({ description })
        .eq('key', key);

      if (descError) {
        logger.warn('Failed to update config description:', descError);
      }
    }

    // Log de auditoría
    await logAuditAction(
      req.user?.id || '',
      'UPDATE',
      'system_config',
      key,
      { key, value: oldValue },
      { key, value },
      req.ip,
      req.get('User-Agent')
    );

    res.json({
      success: true,
      data: {
        key,
        value,
        description
      },
      message: 'Configuration updated successfully',
      timestamp: new Date()
    });

  } catch (error) {
    logger.error('Update config error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update configuration',
      timestamp: new Date()
    });
  }
}

// =====================================================
// ELIMINAR CONFIGURACIÓN
// =====================================================

/**
 * Eliminar una configuración
 * DELETE /api/config/:key
 */
export async function deleteConfig(req: AuthenticatedRequest, res: Response) {
  try {
    const { key } = req.params;

    // Obtener valor actual para auditoría
    const currentValue = await getSystemConfig(key);

    if (currentValue === null) {
      return res.status(404).json({
        success: false,
        error: 'Configuration not found',
        timestamp: new Date()
      });
    }

    // Eliminar configuración
    const { error } = await supabaseAdmin
      .from('system_config')
      .delete()
      .eq('key', key);

    if (error) {
      throw error;
    }

    // Log de auditoría
    await logAuditAction(
      req.user?.id || '',
      'DELETE',
      'system_config',
      key,
      { key, value: currentValue },
      null,
      req.ip,
      req.get('User-Agent')
    );

    res.json({
      success: true,
      message: 'Configuration deleted successfully',
      timestamp: new Date()
    });

  } catch (error) {
    logger.error('Delete config error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete configuration',
      timestamp: new Date()
    });
  }
}

// =====================================================
// OBTENER CONFIGURACIONES PÚBLICAS
// =====================================================

/**
 * Obtener configuraciones públicas (sin autenticación)
 * GET /api/config/public
 */
export async function getPublicConfigs(req: any, res: Response) {
  try {
    // Claves de configuración que son públicas
    const publicKeys = [
      'volcano_coordinates',
      'emergency_contacts',
      'mobile_app_version',
      'system_status',
      'maintenance_mode'
    ];

    const { data: configs, error } = await supabaseAdmin
      .from('system_config')
      .select('key, value')
      .in('key', publicKeys);

    if (error) {
      throw error;
    }

    // Convertir a objeto
    const configObject = configs?.reduce((acc: any, config: any) => {
      acc[config.key] = config.value;
      return acc;
    }, {}) || {};

    res.json({
      success: true,
      data: configObject,
      timestamp: new Date()
    });

  } catch (error) {
    logger.error('Get public configs error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get public configuration',
      timestamp: new Date()
    });
  }
}

// =====================================================
// RESTABLECER CONFIGURACIONES POR DEFECTO
// =====================================================

/**
 * Restablecer configuraciones a valores por defecto
 * POST /api/config/reset
 */
export async function resetConfigs(req: AuthenticatedRequest, res: Response) {
  try {
    const defaultConfigs = [
      {
        key: 'volcano_coordinates',
        value: {
          lat: -39.420000,
          lng: -71.939167,
          name: 'Volcán Villarrica'
        },
        description: 'Coordenadas del volcán principal'
      },
      {
        key: 'emergency_contacts',
        value: {
          emergency: '133',
          police: '133',
          fire: '132',
          medical: '131'
        },
        description: 'Números de emergencia'
      },
      {
        key: 'mobile_app_version',
        value: '1.0.0',
        description: 'Versión mínima requerida de la app móvil'
      },
      {
        key: 'system_status',
        value: 'operational',
        description: 'Estado del sistema'
      },
      {
        key: 'maintenance_mode',
        value: false,
        description: 'Modo de mantenimiento'
      }
    ];

    // Insertar o actualizar configuraciones por defecto
    const { error } = await supabaseAdmin
      .from('system_config')
      .upsert(
        defaultConfigs.map(config => ({
          ...config,
          updated_by: req.user?.id,
          updated_at: new Date().toISOString()
        })),
        { onConflict: 'key' }
      );

    if (error) {
      throw error;
    }

    // Log de auditoría
    await logAuditAction(
      req.user?.id || '',
      'UPDATE',
      'system_config',
      'multiple',
      null,
      { action: 'reset_to_defaults', count: defaultConfigs.length },
      req.ip,
      req.get('User-Agent')
    );

    res.json({
      success: true,
      data: defaultConfigs,
      message: 'Configurations reset to defaults successfully',
      timestamp: new Date()
    });

  } catch (error) {
    logger.error('Reset configs error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to reset configurations',
      timestamp: new Date()
    });
  }
}

// =====================================================
// EXPORTACIONES
// =====================================================

export default {
  getAllConfigs,
  getConfigByKey,
  updateConfig,
  deleteConfig,
  getPublicConfigs,
  resetConfigs
};
