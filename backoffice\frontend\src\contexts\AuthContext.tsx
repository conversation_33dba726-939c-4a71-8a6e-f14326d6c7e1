/**
 * 🌋 Volcano App Frontend - Contexto de Autenticación
 * Manejo del estado de autenticación y tokens JWT
 */

import React, { createContext, ReactNode, useContext, useEffect, useState } from 'react';
import toast from 'react-hot-toast';
import { useLocation, useNavigate } from 'react-router-dom';
import { authService } from '../services/authService';
import { AuthTokens, User } from '../types/auth';

// =====================================================
// TIPOS
// =====================================================

interface AuthContextType {
  user: User | null;
  tokens: AuthTokens | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  refreshToken: () => Promise<void>;
  updateUser: (user: User) => void;
}

// =====================================================
// CONTEXTO
// =====================================================

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// =====================================================
// HOOK PERSONALIZADO
// =====================================================

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// =====================================================
// PROVIDER
// =====================================================

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null);
  const [tokens, setTokens] = useState<AuthTokens | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const navigate = useNavigate();
  const location = useLocation();

  // =====================================================
  // FUNCIONES DE AUTENTICACIÓN
  // =====================================================

  const login = async (email: string, password: string) => {
    try {
      setIsLoading(true);
      const response = await authService.login(email, password);
      
      setUser(response.user);
      setTokens(response.tokens);
      
      // Guardar tokens en localStorage
      localStorage.setItem('access_token', response.tokens.access_token);
      localStorage.setItem('refresh_token', response.tokens.refresh_token);
      
      // Configurar token en el servicio
      authService.setAuthToken(response.tokens.access_token);
      
      toast.success(`¡Bienvenido, ${response.user.full_name}!`);
      
      // Redireccionar a la página solicitada o dashboard
      const from = location.state?.from?.pathname || '/dashboard';
      navigate(from, { replace: true });
      
    } catch (error: any) {
      const message = error.response?.data?.error || 'Error al iniciar sesión';
      toast.error(message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    try {
      // Llamar al endpoint de logout
      authService.logout().catch(() => {
        // Ignorar errores del logout en el servidor
      });
      
      // Limpiar estado local
      setUser(null);
      setTokens(null);
      
      // Limpiar localStorage
      localStorage.removeItem('access_token');
      localStorage.removeItem('refresh_token');
      
      // Limpiar token del servicio
      authService.setAuthToken(null);
      
      toast.success('Sesión cerrada correctamente');
      navigate('/login', { replace: true });
      
    } catch (error) {
      console.error('Error during logout:', error);
      // Forzar logout local incluso si hay error
      setUser(null);
      setTokens(null);
      localStorage.clear();
      navigate('/login', { replace: true });
    }
  };

  const refreshToken = async () => {
    try {
      const refreshTokenValue = localStorage.getItem('refresh_token');
      if (!refreshTokenValue) {
        throw new Error('No refresh token available');
      }

      const response = await authService.refreshToken(refreshTokenValue);
      
      setUser(response.user);
      setTokens(response.tokens);
      
      // Actualizar tokens en localStorage
      localStorage.setItem('access_token', response.tokens.access_token);
      localStorage.setItem('refresh_token', response.tokens.refresh_token);
      
      // Configurar nuevo token en el servicio
      authService.setAuthToken(response.tokens.access_token);
      
    } catch (error) {
      console.error('Error refreshing token:', error);
      logout();
      throw error;
    }
  };

  const updateUser = (updatedUser: User) => {
    setUser(updatedUser);
  };

  // =====================================================
  // INICIALIZACIÓN
  // =====================================================

  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const accessToken = localStorage.getItem('access_token');
        const refreshTokenValue = localStorage.getItem('refresh_token');
        
        if (!accessToken || !refreshTokenValue) {
          setIsLoading(false);
          return;
        }

        // Configurar token en el servicio
        authService.setAuthToken(accessToken);
        
        try {
          // Intentar obtener el perfil del usuario
          const userProfile = await authService.getProfile();
          setUser(userProfile);
          setTokens({
            access_token: accessToken,
            refresh_token: refreshTokenValue,
            expires_in: 0 // Se actualizará en el próximo refresh
          });
          
        } catch (error: any) {
          // Si el token expiró, intentar renovarlo
          if (error.response?.status === 401) {
            try {
              await refreshToken();
            } catch (refreshError) {
              // Si no se puede renovar, hacer logout
              logout();
            }
          } else {
            throw error;
          }
        }
        
      } catch (error) {
        console.error('Error initializing auth:', error);
        // Limpiar tokens inválidos
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, []);

  // =====================================================
  // AUTO-REFRESH DE TOKENS
  // =====================================================

  useEffect(() => {
    if (!tokens?.access_token) return;

    // Configurar auto-refresh 5 minutos antes de que expire el token
    const refreshInterval = setInterval(async () => {
      try {
        await refreshToken();
      } catch (error) {
        console.error('Auto-refresh failed:', error);
        clearInterval(refreshInterval);
      }
    }, 20 * 60 * 1000); // 20 minutos

    return () => clearInterval(refreshInterval);
  }, [tokens]);

  // =====================================================
  // INTERCEPTOR PARA MANEJAR ERRORES 401
  // =====================================================

  useEffect(() => {
    const interceptor = authService.setupResponseInterceptor(
      async () => {
        try {
          await refreshToken();
          return true;
        } catch (error) {
          logout();
          return false;
        }
      }
    );

    return () => {
      authService.removeResponseInterceptor(interceptor);
    };
  }, []);

  // =====================================================
  // VALOR DEL CONTEXTO
  // =====================================================

  const value: AuthContextType = {
    user,
    tokens,
    isLoading,
    isAuthenticated: !!user && !!tokens,
    login,
    logout,
    refreshToken,
    updateUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}
