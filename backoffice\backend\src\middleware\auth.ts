/**
 * 🌋 Volcano App Backend - Middleware de Autenticación
 * Manejo de JWT y autorización por roles
 */

import { CONFIG } from '@/config/env';
import { supabaseAdmin } from '@/services/supabase';
import { AuthenticatedRequest, JwtPayload, USER_ROLE_HIERARCHY, UserRole } from '@/types';
import { logger, logSecurity } from '@/utils/logger';
import { NextFunction, Response } from 'express';
import jwt from 'jsonwebtoken';

// =====================================================
// CONFIGURACIÓN JWT
// =====================================================

const { JWT_SECRET, JWT_REFRESH_SECRET } = CONFIG.JWT;

// =====================================================
// UTILIDADES JWT
// =====================================================

/**
 * Genera tokens de acceso y refresh
 */
export function generateTokens(payload: Omit<JwtPayload, 'iat' | 'exp'>) {
  const accessToken = jwt.sign(
    payload as jwt.JwtPayload,
    JWT_SECRET as string,
    { expiresIn: CONFIG.JWT.JWT_EXPIRES_IN } as jwt.SignOptions
  );

  const refreshToken = jwt.sign(
    payload as jwt.JwtPayload,
    JWT_REFRESH_SECRET as string,
    { expiresIn: CONFIG.JWT.JWT_REFRESH_EXPIRES_IN } as jwt.SignOptions
  );

  return {
    access_token: accessToken,
    refresh_token: refreshToken,
    expires_in: 24 * 60 * 60 // 24 horas en segundos
  };
}

/**
 * Verifica un token JWT
 */
export function verifyToken(token: string, isRefreshToken = false): JwtPayload {
  const secret = isRefreshToken ? JWT_REFRESH_SECRET as string : JWT_SECRET as string;
  return jwt.verify(token, secret) as JwtPayload;
}

/**
 * Extrae el token del header Authorization
 */
function extractTokenFromHeader(authHeader?: string): string | null {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  return authHeader.substring(7);
}

// =====================================================
// MIDDLEWARE DE AUTENTICACIÓN
// =====================================================

/**
 * Middleware para verificar autenticación JWT
 */
export function authenticateToken() {
  return async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      // En desarrollo, permitir acceso sin autenticación si está configurado
      if (CONFIG.SERVER.NODE_ENV === 'development' && process.env.DISABLE_AUTH_IN_DEV === 'true') {
        // Crear un usuario mock para desarrollo con UUID válido
        req.user = {
          id: '00000000-0000-0000-0000-000000000001',
          email: '<EMAIL>',
          role: 'ADMIN' as any,
          full_name: 'Developer User'
        };
        return next();
      }

      const token = extractTokenFromHeader(req.headers.authorization);

      if (!token) {
        logSecurity('Missing authentication token', {
          ip: req.ip,
          userAgent: req.get('User-Agent'),
          url: req.url
        });

        return res.status(401).json({
          success: false,
          error: 'Access token required',
          timestamp: new Date()
        });
      }

      // Verificar token
      const decoded = verifyToken(token);

      // Obtener información completa del usuario
      const { data: user, error } = await supabaseAdmin
        .from('admin_users')
        .select('id, email, full_name, role, is_active')
        .eq('id', decoded.user_id)
        .eq('is_active', true)
        .single();

      if (error || !user) {
        logSecurity('Invalid or expired token', {
          userId: decoded.user_id,
          ip: req.ip,
          userAgent: req.get('User-Agent')
        });
        
        return res.status(401).json({
          success: false,
          error: 'Invalid or expired token',
          timestamp: new Date()
        });
      }

      // Agregar usuario al request
      req.user = {
        id: user.id,
        email: user.email,
        role: user.role,
        full_name: user.full_name
      };

      next();
    } catch (error) {
      if (error instanceof jwt.JsonWebTokenError) {
        logSecurity('JWT verification failed', {
          error: error.message,
          ip: req.ip,
          userAgent: req.get('User-Agent')
        });
        
        return res.status(401).json({
          success: false,
          error: 'Invalid token',
          timestamp: new Date()
        });
      }

      logger.error('Authentication middleware error:', error);
      return res.status(500).json({
        success: false,
        error: 'Internal server error',
        timestamp: new Date()
      });
    }
  };
}

// =====================================================
// MIDDLEWARE DE AUTORIZACIÓN
// =====================================================

/**
 * Middleware para verificar roles específicos
 */
export function requireRole(...allowedRoles: UserRole[]) {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required',
        timestamp: new Date()
      });
    }

    if (!allowedRoles.includes(req.user.role)) {
      logSecurity('Insufficient permissions', {
        userId: req.user.id,
        userRole: req.user.role,
        requiredRoles: allowedRoles,
        ip: req.ip,
        url: req.url
      }, 'medium');
      
      return res.status(403).json({
        success: false,
        error: 'Insufficient permissions',
        timestamp: new Date()
      });
    }

    next();
  };
}

/**
 * Middleware para verificar nivel mínimo de rol
 */
export function requireMinimumRole(minimumRole: UserRole) {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required',
        timestamp: new Date()
      });
    }

    const userLevel = USER_ROLE_HIERARCHY[req.user.role];
    const requiredLevel = USER_ROLE_HIERARCHY[minimumRole];

    if (userLevel < requiredLevel) {
      logSecurity('Insufficient role level', {
        userId: req.user.id,
        userRole: req.user.role,
        userLevel,
        requiredRole: minimumRole,
        requiredLevel,
        ip: req.ip,
        url: req.url
      }, 'medium');
      
      return res.status(403).json({
        success: false,
        error: 'Insufficient permissions',
        timestamp: new Date()
      });
    }

    next();
  };
}

/**
 * Middleware para verificar que el usuario puede acceder a sus propios datos
 */
export function requireOwnershipOrAdmin(userIdParam = 'userId') {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required',
        timestamp: new Date()
      });
    }

    const targetUserId = req.params[userIdParam];
    const isOwner = req.user.id === targetUserId;
    const isAdmin = req.user.role === UserRole.ADMIN;

    if (!isOwner && !isAdmin) {
      logSecurity('Unauthorized access attempt', {
        userId: req.user.id,
        targetUserId,
        ip: req.ip,
        url: req.url
      }, 'high');
      
      return res.status(403).json({
        success: false,
        error: 'Access denied',
        timestamp: new Date()
      });
    }

    next();
  };
}

// =====================================================
// MIDDLEWARE OPCIONAL
// =====================================================

/**
 * Middleware para autenticación opcional (no falla si no hay token)
 */
export function optionalAuth() {
  return async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const token = extractTokenFromHeader(req.headers.authorization);

      if (!token) {
        return next();
      }

      const decoded = verifyToken(token);

      const { data: user, error } = await supabaseAdmin
        .from('admin_users')
        .select('id, email, full_name, role, is_active')
        .eq('id', decoded.user_id)
        .eq('is_active', true)
        .single();

      if (!error && user) {
        req.user = {
          id: user.id,
          email: user.email,
          role: user.role,
          full_name: user.full_name
        };
      }

      next();
    } catch (error) {
      // En autenticación opcional, ignoramos errores de token
      next();
    }
  };
}

// =====================================================
// UTILIDADES DE SESIÓN
// =====================================================

/**
 * Actualiza el último login del usuario
 */
export async function updateLastLogin(userId: string): Promise<void> {
  try {
    const { error } = await supabaseAdmin
      .from('admin_users')
      .update({ last_login: new Date().toISOString() })
      .eq('id', userId);

    if (error) {
      logger.error('Failed to update last login:', error);
    }
  } catch (error) {
    logger.error('Error updating last login:', error);
  }
}

/**
 * Verifica si un usuario está activo
 */
export async function isUserActive(userId: string): Promise<boolean> {
  try {
    const { data, error } = await supabaseAdmin
      .from('admin_users')
      .select('is_active')
      .eq('id', userId)
      .single();

    if (error) {
      logger.error('Error checking user active status:', error);
      return false;
    }

    return data?.is_active || false;
  } catch (error) {
    logger.error('Failed to check user active status:', error);
    return false;
  }
}

// =====================================================
// EXPORTACIONES
// =====================================================

export default {
  generateTokens,
  verifyToken,
  authenticateToken,
  requireRole,
  requireMinimumRole,
  requireOwnershipOrAdmin,
  optionalAuth,
  updateLastLogin,
  isUserActive
};
