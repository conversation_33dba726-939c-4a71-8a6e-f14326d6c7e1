# 🌋 Volcano App - Correcciones de Reporte de Ubicación

## 📋 Resumen de Problemas Corregidos

Se han identificado y corregido los siguientes errores en el sistema de reporte de ubicación:

### 1. **Error de `anonymous_id` faltante**
- **Problema**: El campo `anonymous_id` no se estaba enviando en las solicitudes
- **Causa**: No había un sistema para generar y gestionar identificadores únicos de dispositivo
- **Solución**: Creado servicio `deviceId.ts` que genera y almacena IDs únicos

### 2. **Error de formato de coordenadas**
- **Problema**: Se enviaban `lat` y `lng` en lugar de `latitude` y `longitude`
- **Causa**: Inconsistencia entre la interfaz frontend y la API backend
- **Solución**: Actualizada interfaz `LocationReport` y todos los usos

### 3. **Falta de validación de coordenadas**
- **Problema**: No se validaban las coordenadas antes del envío
- **Causa**: Validación solo en el backend
- **Solución**: Agregada validación en el frontend antes del envío

### 4. **Error de módulo AsyncStorage no encontrado**
- **Problema**: `@react-native-async-storage/async-storage could not be found`
- **Causa**: Dependencia no instalada y problemas de resolución de módulos
- **Solución**: Instalada dependencia e implementada importación dinámica con fallback

## 🔧 Archivos Modificados

### Nuevos Archivos
- `services/deviceId.ts` - Servicio para gestión de device_id único
- `scripts/test-location-report.js` - Script de pruebas para validar correcciones

### Archivos Modificados
- `services/api.ts` - Actualizada interfaz `LocationReport`
- `hooks/useApi.ts` - Mejorados hooks con validación y device_id automático
- `components/InteractiveMap.tsx` - Corregido formato de datos enviados
- `components/ApiTestPanel.tsx` - Actualizado para usar nuevo formato
- `app/_layout.tsx` - Agregada inicialización automática de device_id

## 🚀 Funcionalidades Implementadas

### 1. **Generación Automática de Device ID**
```typescript
// El device_id se genera automáticamente al iniciar la app
const deviceId = await getDeviceId();
// Formato: "volcano_platform_version_timestamp_random"
// Longitud: 10-100 caracteres (cumple requisitos del backend)
```

### 4. **Importación Dinámica de AsyncStorage**
```typescript
// Importación dinámica con fallback a memoria
async function getAsyncStorage() {
  if (!AsyncStorage) {
    try {
      AsyncStorage = (await import('@react-native-async-storage/async-storage')).default;
    } catch (error) {
      // Fallback a almacenamiento en memoria si AsyncStorage no está disponible
      AsyncStorage = memoryStorageFallback;
    }
  }
  return AsyncStorage;
}
```

### 2. **Validación de Coordenadas**
```typescript
// Validación antes del envío
if (latitude < -90 || latitude > 90) {
  throw new Error('Invalid latitude. Must be between -90 and 90');
}
if (longitude < -180 || longitude > 180) {
  throw new Error('Invalid longitude. Must be between -180 and 180');
}
```

### 3. **Formato Correcto de Datos**
```typescript
// Antes (incorrecto)
{
  lat: -39.2706,
  lng: -71.9728
}

// Después (correcto)
{
  anonymous_id: "volcano_ios_1.0.0_1234567890_abc123",
  latitude: -39.2706,
  longitude: -71.9728,
  accuracy: 10.5,
  timestamp: "2024-01-15T10:30:00.000Z",
  app_version: "1.0.0",
  device_type: "ios"
}
```

## 🧪 Cómo Probar las Correcciones

### 1. **Ejecutar Script de Pruebas**
```bash
cd volcanoApp
node scripts/test-location-report.js
```

### 2. **Probar en la App Móvil**
1. Iniciar el backend: `cd backoffice/backend && npm run dev`
2. Iniciar la app móvil: `npm start`
3. Ir a la pestaña "Mapa" y presionar "Actualizar ubicación"
4. Verificar en los logs que no hay errores 400

### 3. **Verificar en el Panel de Pruebas**
1. Ir a la pestaña "Configuración" en la app
2. Usar el panel de pruebas de API
3. Probar "Test Location Report"

## 📊 Validaciones Implementadas

### Frontend (antes del envío)
- ✅ Device ID válido (10-100 caracteres)
- ✅ Latitud entre -90 y 90
- ✅ Longitud entre -180 y 180
- ✅ Formato correcto de campos

### Backend (validación express-validator)
- ✅ `anonymous_id` requerido y longitud válida
- ✅ `latitude` y `longitude` en rangos correctos
- ✅ Campos opcionales con validación de tipo

## 🔍 Logs de Debugging

### Device ID
```
📱 Generated new device ID: volcano_ios_1.0.0_1234567890_abc123
📱 Retrieved existing device ID: volcano_ios_1.0.0_1234567890_abc123
```

### Reporte de Ubicación
```
🌐 API Request: POST /mobile/location/report
✅ API Response: 200 /mobile/location/report
✅ Location reported successfully
```

### Errores Corregidos
```
// Antes
❌ API Response Error: 400 {"error": "anonymous_id is required"}
❌ API Response Error: 400 {"error": "Invalid latitude"}

// Después
✅ Location reported successfully
```

## 🛠️ Mantenimiento

### Regenerar Device ID (si es necesario)
```typescript
import { regenerateDeviceId } from '@/services/deviceId';
const newDeviceId = await regenerateDeviceId();
```

### Limpiar Device ID (para testing)
```typescript
import { clearDeviceId } from '@/services/deviceId';
await clearDeviceId();
```

### Verificar Device ID actual
```typescript
import { getDeviceId, validateDeviceId } from '@/services/deviceId';
const deviceId = await getDeviceId();
const isValid = validateDeviceId(deviceId);
```

## ✅ Estado Actual

- ✅ **anonymous_id**: Se genera y envía automáticamente
- ✅ **Coordenadas**: Formato correcto (latitude/longitude)
- ✅ **Validación**: Frontend y backend validando correctamente
- ✅ **Compatibilidad**: Funciona con reportes individuales y en lote
- ✅ **Persistencia**: Device ID se mantiene entre sesiones
- ✅ **Logs**: Información clara para debugging

## 🎯 Próximos Pasos

1. **Probar en dispositivos físicos** para verificar funcionamiento completo
2. **Implementar retry automático** para reportes fallidos
3. **Agregar métricas** de éxito/fallo de reportes
4. **Optimizar frecuencia** de reportes según movimiento del usuario

---

**Nota**: Todas las correcciones mantienen compatibilidad con el backend existente y siguen las mejores prácticas de React Native y TypeScript.
