/**
 * 🌋 Interactive Map Component - Leaflet Map with Zone Drawing
 */

import React, { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ygon, useMap } from 'react-leaflet';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import { Edit, Save, Trash2, Plus } from 'lucide-react';

// Fix for default markers in React Leaflet
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

interface Zone {
  id: string;
  name: string;
  description: string;
  zone_type: 'SAFE' | 'EMERGENCY' | 'EVACUATION' | 'DANGER';
  geometry: {
    type: string;
    coordinates: number[][][];
  };
  is_active: boolean;
  version: number;
}

interface Alert {
  id: string;
  title: string;
  alert_level: 'NORMAL' | 'ADVISORY' | 'WATCH' | 'WARNING' | 'EMERGENCY';
  volcano_name: string;
  volcano_lat: number;
  volcano_lng: number;
  is_active: boolean;
}

interface InteractiveMapProps {
  zones: Zone[];
  alerts: Alert[];
  onZoneUpdate?: (zoneId: string, newGeometry: any) => void;
  onZoneCreate?: (geometry: any) => void;
}

// Componente para controles de dibujo
function DrawingControls({ onStartDrawing }: { onStartDrawing: () => void }) {
  return (
    <div className="absolute top-4 right-4 z-[1000] bg-white rounded-lg shadow-lg p-2">
      <button
        onClick={onStartDrawing}
        className="flex items-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm"
      >
        <Plus className="h-4 w-4" />
        <span>Dibujar Zona</span>
      </button>
    </div>
  );
}

// Componente para mostrar información de zona
function ZoneInfo({ zone, onEdit, onDelete }: { 
  zone: Zone; 
  onEdit: () => void; 
  onDelete: () => void; 
}) {
  const getZoneColor = (type: string) => {
    switch (type) {
      case 'SAFE': return '#10b981'; // green
      case 'EMERGENCY': return '#3b82f6'; // blue
      case 'EVACUATION': return '#f59e0b'; // yellow
      case 'DANGER': return '#ef4444'; // red
      default: return '#6b7280'; // gray
    }
  };

  const getZoneIcon = (type: string) => {
    switch (type) {
      case 'SAFE': return '🛡️';
      case 'EMERGENCY': return '🏥';
      case 'EVACUATION': return '🚨';
      case 'DANGER': return '⚠️';
      default: return '📍';
    }
  };

  return (
    <div className="min-w-[200px]">
      <div className="flex items-center space-x-2 mb-2">
        <span className="text-lg">{getZoneIcon(zone.zone_type)}</span>
        <h3 className="font-semibold text-gray-900">{zone.name}</h3>
      </div>
      <p className="text-sm text-gray-600 mb-3">{zone.description}</p>
      <div className="flex items-center space-x-2 text-xs text-gray-500 mb-3">
        <span 
          className="px-2 py-1 rounded-full text-white font-medium"
          style={{ backgroundColor: getZoneColor(zone.zone_type) }}
        >
          {zone.zone_type}
        </span>
        <span>v{zone.version}</span>
      </div>
      <div className="flex space-x-2">
        <button
          onClick={onEdit}
          className="flex items-center space-x-1 px-2 py-1 bg-blue-100 text-blue-700 rounded text-xs hover:bg-blue-200"
        >
          <Edit className="h-3 w-3" />
          <span>Editar</span>
        </button>
        <button
          onClick={onDelete}
          className="flex items-center space-x-1 px-2 py-1 bg-red-100 text-red-700 rounded text-xs hover:bg-red-200"
        >
          <Trash2 className="h-3 w-3" />
          <span>Eliminar</span>
        </button>
      </div>
    </div>
  );
}

export function InteractiveMap({ zones, alerts, onZoneUpdate, onZoneCreate }: InteractiveMapProps) {
  const [isDrawing, setIsDrawing] = useState(false);
  const [selectedZone, setSelectedZone] = useState<Zone | null>(null);
  const mapRef = useRef<L.Map | null>(null);

  // Coordenadas del Volcán Villarrica
  const volcanoPosition: [number, number] = [-39.420000, -71.939167];

  const getZoneColor = (type: string) => {
    switch (type) {
      case 'SAFE': return '#10b981';
      case 'EMERGENCY': return '#3b82f6';
      case 'EVACUATION': return '#f59e0b';
      case 'DANGER': return '#ef4444';
      default: return '#6b7280';
    }
  };

  const getAlertColor = (level: string) => {
    switch (level) {
      case 'NORMAL': return '#10b981';
      case 'ADVISORY': return '#f59e0b';
      case 'WATCH': return '#f97316';
      case 'WARNING': return '#ef4444';
      case 'EMERGENCY': return '#dc2626';
      default: return '#6b7280';
    }
  };

  const handleStartDrawing = () => {
    setIsDrawing(true);
    // Aquí se implementaría la lógica de dibujo con Leaflet Draw
    console.log('Iniciando modo de dibujo...');
  };

  const handleZoneEdit = (zone: Zone) => {
    setSelectedZone(zone);
    console.log('Editando zona:', zone.name);
  };

  const handleZoneDelete = (zone: Zone) => {
    if (confirm(`¿Estás seguro de que quieres eliminar la zona "${zone.name}"?`)) {
      console.log('Eliminando zona:', zone.name);
    }
  };

  return (
    <div className="relative h-[600px] w-full rounded-lg overflow-hidden border border-gray-200">
      <MapContainer
        center={volcanoPosition}
        zoom={10}
        style={{ height: '100%', width: '100%' }}
        ref={mapRef}
      >
        <TileLayer
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
        />

        {/* Marcadores de Alertas */}
        {alerts.filter(alert => alert.is_active).map((alert) => (
          <Marker
            key={alert.id}
            position={[alert.volcano_lat, alert.volcano_lng]}
            icon={L.divIcon({
              className: 'custom-alert-marker',
              html: `<div style="
                background-color: ${getAlertColor(alert.alert_level)};
                width: 20px;
                height: 20px;
                border-radius: 50%;
                border: 2px solid white;
                box-shadow: 0 2px 4px rgba(0,0,0,0.3);
              "></div>`,
              iconSize: [20, 20],
              iconAnchor: [10, 10]
            })}
          >
            <Popup>
              <div className="min-w-[200px]">
                <h3 className="font-semibold text-gray-900 mb-1">{alert.title}</h3>
                <p className="text-sm text-gray-600 mb-2">{alert.volcano_name}</p>
                <span 
                  className="px-2 py-1 rounded-full text-white text-xs font-medium"
                  style={{ backgroundColor: getAlertColor(alert.alert_level) }}
                >
                  {alert.alert_level}
                </span>
              </div>
            </Popup>
          </Marker>
        ))}

        {/* Polígonos de Zonas */}
        {zones.filter(zone => zone.is_active).map((zone) => (
          <Polygon
            key={zone.id}
            positions={zone.geometry.coordinates[0].map(coord => [coord[1], coord[0]] as [number, number])}
            pathOptions={{
              color: getZoneColor(zone.zone_type),
              fillColor: getZoneColor(zone.zone_type),
              fillOpacity: 0.3,
              weight: 2
            }}
            eventHandlers={{
              click: () => setSelectedZone(zone)
            }}
          >
            <Popup>
              <ZoneInfo
                zone={zone}
                onEdit={() => handleZoneEdit(zone)}
                onDelete={() => handleZoneDelete(zone)}
              />
            </Popup>
          </Polygon>
        ))}
      </MapContainer>

      {/* Controles de Dibujo */}
      <DrawingControls onStartDrawing={handleStartDrawing} />

      {/* Leyenda */}
      <div className="absolute bottom-4 left-4 z-[1000] bg-white rounded-lg shadow-lg p-3">
        <h4 className="text-sm font-semibold text-gray-900 mb-2">Leyenda</h4>
        <div className="space-y-1 text-xs">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 rounded-full bg-green-500"></div>
            <span>🛡️ Zona Segura</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 rounded-full bg-blue-500"></div>
            <span>🏥 Emergencia</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
            <span>🚨 Evacuación</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 rounded-full bg-red-500"></div>
            <span>⚠️ Peligro</span>
          </div>
        </div>
      </div>

      {/* Estado de Dibujo */}
      {isDrawing && (
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-[1000] bg-blue-600 text-white px-4 py-2 rounded-lg shadow-lg">
          <p className="text-sm">Modo de dibujo activo - Haz clic en el mapa para crear una zona</p>
        </div>
      )}
    </div>
  );
}
