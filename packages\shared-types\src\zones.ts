/**
 * 🌋 Volcano App - Tipos de Zonas de Seguridad Compartidos
 */

export enum ZoneType {
  SAFE = 'SAFE',
  EMERGENCY = 'EMERGENCY',
  DANGER = 'DANGER',
  EVACUATION = 'EVACUATION',
  RESTRICTED = 'RESTRICTED'
}

export interface SafetyZone {
  id: string;
  name: string;
  description?: string;
  zone_type: ZoneType;
  geometry: any; // GeoJSON geometry
  center_lat?: number; // Latitud del centro de la zona
  center_lng?: number; // Longitud del centro de la zona
  capacity?: number;
  contact_info?: Record<string, any>;
  facilities?: Record<string, any>;
  is_active: boolean;
  version: number;
  created_by?: string;
  created_at: string;
  updated_at: string;
  metadata?: Record<string, any>;
  admin_users?: {
    full_name: string;
    email: string;
  };
}

export interface CreateZoneRequest {
  name: string;
  description?: string;
  zone_type: ZoneType;
  geometry: any;
  center_lat?: number;
  center_lng?: number;
  capacity?: number;
  contact_info?: Record<string, any>;
  facilities?: Record<string, any>;
  metadata?: Record<string, any>;
}

export interface UpdateZoneRequest {
  name?: string;
  description?: string;
  zone_type?: ZoneType;
  geometry?: any;
  center_lat?: number;
  center_lng?: number;
  capacity?: number;
  contact_info?: Record<string, any>;
  facilities?: Record<string, any>;
  is_active?: boolean;
  metadata?: Record<string, any>;
}

export interface ZoneFilters {
  page?: number;
  limit?: number;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
  search?: string;
  zone_type?: ZoneType;
  is_active?: boolean;
}

// Configuraciones de UI para zonas
export const ZONE_TYPE_COLORS = {
  [ZoneType.SAFE]: {
    bg: '#10b981',
    text: '#ffffff',
    border: '#059669',
    fill: 'rgba(16, 185, 129, 0.3)'
  },
  [ZoneType.EMERGENCY]: {
    bg: '#f59e0b',
    text: '#ffffff',
    border: '#d97706',
    fill: 'rgba(245, 158, 11, 0.3)'
  },
  [ZoneType.DANGER]: {
    bg: '#ef4444',
    text: '#ffffff',
    border: '#dc2626',
    fill: 'rgba(239, 68, 68, 0.3)'
  },
  [ZoneType.EVACUATION]: {
    bg: '#8b5cf6',
    text: '#ffffff',
    border: '#7c3aed',
    fill: 'rgba(139, 92, 246, 0.3)'
  },
  [ZoneType.RESTRICTED]: {
    bg: '#6b7280',
    text: '#ffffff',
    border: '#4b5563',
    fill: 'rgba(107, 114, 128, 0.3)'
  }
} as const;

export const ZONE_TYPE_LABELS = {
  [ZoneType.SAFE]: 'Zona Segura',
  [ZoneType.EMERGENCY]: 'Centro de Emergencia',
  [ZoneType.DANGER]: 'Zona de Peligro',
  [ZoneType.EVACUATION]: 'Ruta de Evacuación',
  [ZoneType.RESTRICTED]: 'Zona Restringida'
} as const;

export const ZONE_TYPE_ICONS = {
  [ZoneType.SAFE]: '🛡️',
  [ZoneType.EMERGENCY]: '🏥',
  [ZoneType.DANGER]: '⚠️',
  [ZoneType.EVACUATION]: '🚨',
  [ZoneType.RESTRICTED]: '🚫'
} as const;

// Tipos geoespaciales
export type Coordinates = [number, number]; // [longitude, latitude]

export interface GeoJSONGeometry {
  type: 'Point' | 'Polygon' | 'LineString' | 'MultiPoint' | 'MultiPolygon' | 'MultiLineString';
  coordinates: any;
}

export interface LocationPoint {
  latitude: number;
  longitude: number;
  accuracy?: number;
}
