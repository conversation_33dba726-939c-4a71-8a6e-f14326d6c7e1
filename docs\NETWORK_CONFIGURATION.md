# 🌐 Configuración de Red - Volcano App

## 📋 Resumen

Este documento explica cómo funciona la configuración de red en el sistema volcanoApp y cómo manejar cambios de IP durante el desarrollo.

## 🏗️ Arquitectura de Red

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │    Backend      │    │   Mobile App    │
│   (Puerto 3000) │◄──►│   (Puerto 3001) │◄──►│   (Puerto 8081) │
│   localhost     │    │   0.0.0.0       │    │   IP Dinámica   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │    Supabase     │
                       │   (Cloud DB)    │
                       └─────────────────┘
```

## 📁 Archivos de Configuración

### 1. **Variables de Entorno**

#### **`.env` (Aplicación Móvil)**
```bash
# API Configuration
EXPO_PUBLIC_API_URL=http://[IP]:3001/api
EXPO_PUBLIC_WEBSOCKET_URL=http://[IP]:3001

# Network Configuration  
EXPO_PUBLIC_DEV_HOST_IP=[IP]

# Supabase Configuration
EXPO_PUBLIC_SUPABASE_URL=https://gdcbnnlmxwazgnwpfelt.supabase.co
EXPO_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

#### **`backoffice/backend/.env` (Backend)**
```bash
# CORS Configuration
CORS_ORIGIN=http://localhost:5173,http://localhost:3000,http://localhost:3001,http://localhost:8081,http://[IP]:8081,exp://[IP]:8081,http://[IP]:3001

# Server Configuration
PORT=3001
HOST=0.0.0.0
```

### 2. **Configuración Dinámica**

#### **`services/network-config.ts`** (Nuevo archivo centralizado)
- ✅ Detección automática de IP
- ✅ Configuración unificada para API y WebSocket
- ✅ Fallbacks inteligentes
- ✅ Validación de configuración

## 🔧 Proceso de Cambio de IP

### **Método Manual (Actual)**

1. **Obtener IP actual:**
   ```bash
   # Windows
   ipconfig | findstr "IPv4"
   
   # Mac/Linux  
   ifconfig | grep "inet "
   ```

2. **Actualizar archivos:**
   - `.env` (móvil)
   - `backoffice/backend/.env` (CORS)

3. **Reiniciar servicios:**
   ```bash
   npm run start:all
   ```

### **Método Automático (Recomendado)**

El nuevo sistema utiliza `services/network-config.ts` que:

1. **Detecta IP automáticamente** desde `Constants.expoConfig?.hostUri`
2. **Usa variables de entorno** como fallback
3. **Proporciona configuración centralizada** para todos los servicios

## 📊 Configuración por Servicio

### **Backend API**
- **Puerto**: 3001 (fijo)
- **Host**: 0.0.0.0 (escucha en todas las interfaces)
- **CORS**: Configurado para IPs locales y de red

### **Frontend Admin**
- **Puerto**: 3000 (fijo)
- **Conecta a**: localhost:3001 (backend)

### **Aplicación Móvil**
- **Puerto**: 8081 (Expo DevTools)
- **Conecta a**: [IP]:3001 (backend en red)
- **Detección**: Automática vía Expo

## 🚨 Problemas Comunes

### **1. Error "EADDRINUSE: address already in use"**
```bash
# Encontrar proceso usando el puerto
netstat -ano | findstr :3001

# Terminar proceso (Windows)
taskkill /PID [PID] /F

# Terminar proceso (Mac/Linux)
kill -9 [PID]
```

### **2. App móvil no conecta al backend**
- ✅ Verificar IP en `.env`
- ✅ Verificar CORS en backend
- ✅ Verificar que backend esté en puerto 3001

### **3. WebSocket timeout errors**
- ✅ Verificar configuración de puerto (debe ser 3001)
- ✅ Verificar conectividad de red
- ✅ Verificar configuración de firewall

## 🔍 Herramientas de Diagnóstico

### **Verificar Conectividad**
```bash
# Health check del backend
curl http://localhost:3001/health
curl http://[IP]:3001/health

# Verificar puertos activos
netstat -ano | findstr :3001
netstat -ano | findstr :3000
netstat -ano | findstr :8081
```

### **Logs de Red**
Los servicios ahora incluyen logs detallados:
```javascript
// API Service
🔧 API Service Network Configuration: {
  platform: "android",
  apiURL: "http://***********:3001/api", 
  currentIP: "***********",
  isDev: true
}

// WebSocket Service  
🔧 WebSocket Service Network Configuration: {
  platform: "android",
  wsURL: "http://***********:3001",
  isDev: true
}
```

## 🎯 Mejores Prácticas

### **1. Desarrollo Local**
- Usar `npm run start:all` para iniciar todos los servicios
- Verificar logs de configuración de red
- Probar conectividad antes de desarrollo

### **2. Cambio de Red**
- Actualizar `.env` con nueva IP
- Reiniciar servicios con `npm run start:all`
- Verificar logs de configuración

### **3. Debugging**
- Usar herramientas de diagnóstico incluidas
- Verificar configuración con `getNetworkConfig()`
- Validar configuración con `validateNetworkConfig()`

## 🔮 Futuras Mejoras

### **Automatización Completa**
- Script de detección automática de IP
- Actualización automática de archivos `.env`
- Reinicio automático de servicios

### **Configuración Dinámica**
- Hot-reload de configuración de red
- Detección automática de cambios de red
- Notificaciones de cambios de configuración

## 📞 Soporte

Si encuentras problemas con la configuración de red:

1. Verificar logs de configuración en la consola
2. Usar herramientas de diagnóstico incluidas
3. Verificar que todos los servicios estén en los puertos correctos
4. Consultar la sección de problemas comunes

---

**Última actualización**: 2025-06-02  
**Versión del sistema**: 1.0.0
