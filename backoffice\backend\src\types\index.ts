/**
 * 🌋 Volcano App Backend - Tipos TypeScript
 * Definiciones de tipos para toda la aplicación
 */

import { Request } from 'express';

// =====================================================
// ENUMS
// =====================================================

export enum AlertLevel {
  NORMAL = 'NORMAL',
  ADVISORY = 'ADVISORY',
  WATCH = 'WATCH',
  WARNING = 'WARNING',
  EMERGENCY = 'EMERGENCY'
}

export enum ZoneType {
  SAFE = 'SAFE',
  EMERGENCY = 'EMERGENCY',
  DANGER = 'DANGER',
  EVACUATION = 'EVACUATION',
  RESTRICTED = 'RESTRICTED'
}

export enum UserRole {
  ADMIN = 'ADMIN',
  OPERATOR = 'OPERATOR',
  VIEWER = 'VIEWER'
}

export enum AuditAction {
  CREATE = 'CREATE',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE',
  LOGIN = 'LOGIN',
  LOGOUT = 'LOGOUT'
}

// =====================================================
// INTERFACES DE BASE DE DATOS
// =====================================================

export interface AdminUser {
  id: string;
  email: string;
  password_hash: string;
  full_name: string;
  role: UserRole;
  is_active: boolean;
  last_login?: Date;
  created_at: Date;
  updated_at: Date;
}

export interface VolcanoAlert {
  id: string;
  title: string;
  message: string;
  alert_level: AlertLevel;
  volcano_name: string;
  volcano_lat: number;
  volcano_lng: number;
  is_active: boolean;
  is_scheduled: boolean;
  scheduled_for?: Date;
  expires_at?: Date;
  created_by?: string;
  created_at: Date;
  updated_at: Date;
  metadata?: Record<string, any>;
}

export interface SafetyZone {
  id: string;
  name: string;
  description?: string;
  zone_type: ZoneType;
  geometry: any; // PostGIS geometry
  capacity?: number;
  contact_info?: Record<string, any>;
  facilities?: Record<string, any>;
  is_active: boolean;
  version: number;
  created_by?: string;
  created_at: Date;
  updated_at: Date;
  metadata?: Record<string, any>;
}

export interface AuditLog {
  id: string;
  user_id?: string;
  action: AuditAction;
  table_name?: string;
  record_id?: string;
  old_values?: Record<string, any>;
  new_values?: Record<string, any>;
  ip_address?: string;
  user_agent?: string;
  created_at: Date;
}

export interface AppUserLocation {
  id: string;
  anonymous_id: string;
  location: any; // PostGIS point
  accuracy?: number;
  reported_at: Date;
  app_version?: string;
  device_type?: string;
  expires_at: Date;
}

export interface SystemConfig {
  key: string;
  value: Record<string, any>;
  description?: string;
  updated_by?: string;
  updated_at: Date;
}

// =====================================================
// DTOs (Data Transfer Objects)
// =====================================================

export interface CreateUserDto {
  email: string;
  password: string;
  full_name: string;
  role: UserRole;
}

export interface UpdateUserDto {
  email?: string;
  full_name?: string;
  role?: UserRole;
  is_active?: boolean;
}

export interface CreateAlertDto {
  title: string;
  message: string;
  alert_level: AlertLevel;
  volcano_name?: string;
  volcano_lat?: number;
  volcano_lng?: number;
  is_scheduled?: boolean;
  scheduled_for?: Date;
  expires_at?: Date;
  metadata?: Record<string, any>;
}

export interface UpdateAlertDto {
  title?: string;
  message?: string;
  alert_level?: AlertLevel;
  volcano_name?: string;
  volcano_lat?: number;
  volcano_lng?: number;
  is_active?: boolean;
  is_scheduled?: boolean;
  scheduled_for?: Date;
  expires_at?: Date;
  metadata?: Record<string, any>;
}

export interface CreateZoneDto {
  name: string;
  description?: string;
  zone_type: ZoneType;
  geometry: any;
  center_lat?: number;
  center_lng?: number;
  capacity?: number;
  contact_info?: Record<string, any>;
  facilities?: Record<string, any>;
  is_active?: boolean;
  metadata?: Record<string, any>;
}

export interface UpdateZoneDto {
  name?: string;
  description?: string;
  zone_type?: ZoneType;
  geometry?: any;
  center_lat?: number;
  center_lng?: number;
  capacity?: number;
  contact_info?: Record<string, any>;
  facilities?: Record<string, any>;
  is_active?: boolean;
  metadata?: Record<string, any>;
}

export interface ReportLocationDto {
  anonymous_id: string;
  latitude: number;
  longitude: number;
  accuracy?: number;
  app_version?: string;
  device_type?: string;
}

// =====================================================
// INTERFACES DE RESPUESTA API
// =====================================================

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  timestamp: Date;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface DashboardStats {
  active_alerts: number;
  active_zones: number;
  total_users: number;
  users_last_hour: number;
  alerts_last_24h: number;
  zones_by_type: Record<ZoneType, number>;
  alert_history: Array<{
    date: string;
    count: number;
    level: AlertLevel;
  }>;
}

// =====================================================
// INTERFACES DE AUTENTICACIÓN
// =====================================================

export interface LoginDto {
  email: string;
  password: string;
}

export interface AuthTokens {
  access_token: string;
  refresh_token: string;
  expires_in: number;
}

export interface JwtPayload {
  user_id: string;
  email: string;
  role: UserRole;
  iat: number;
  exp: number;
}

export interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    email: string;
    role: UserRole;
    full_name: string;
  };
}

// =====================================================
// INTERFACES DE CONFIGURACIÓN
// =====================================================

export interface DatabaseConfig {
  url: string;
  anon_key: string;
  service_role_key: string;
}

export interface JwtConfig {
  secret: string;
  expires_in: string;
  refresh_secret: string;
  refresh_expires_in: string;
}

export interface ServerConfig {
  port: number;
  host: string;
  cors_origin: string[];
  cors_credentials: boolean;
}

export interface SecurityConfig {
  bcrypt_rounds: number;
  rate_limit_window_ms: number;
  rate_limit_max_requests: number;
}

// =====================================================
// INTERFACES DE VALIDACIÓN
// =====================================================

export interface ValidationError {
  field: string;
  message: string;
  value?: any;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

// =====================================================
// INTERFACES DE LOGGING
// =====================================================

export interface LogContext {
  user_id?: string;
  request_id?: string;
  ip_address?: string;
  user_agent?: string;
  method?: string;
  url?: string;
  status_code?: number;
  response_time?: number;
}

// =====================================================
// INTERFACES DE NOTIFICACIONES
// =====================================================

export interface PushNotification {
  title: string;
  body: string;
  data?: Record<string, any>;
  target?: 'all' | 'zone' | 'user';
  target_criteria?: any;
}

export interface NotificationResult {
  success: boolean;
  sent_count: number;
  failed_count: number;
  errors?: string[];
}

// =====================================================
// TIPOS UTILITARIOS
// =====================================================

export type Coordinates = [number, number]; // [longitude, latitude]

export type GeoJSONGeometry = {
  type: 'Point' | 'Polygon' | 'LineString' | 'MultiPoint' | 'MultiPolygon' | 'MultiLineString';
  coordinates: any;
};

export type SortOrder = 'asc' | 'desc';

export type QueryFilters = {
  page?: number;
  limit?: number;
  sort_by?: string;
  sort_order?: SortOrder;
  search?: string;
  filters?: Record<string, any>;
};

// =====================================================
// CONSTANTES
// =====================================================

export const DEFAULT_PAGINATION = {
  page: 1,
  limit: 20,
  max_limit: 100
} as const;

export const ALERT_LEVEL_PRIORITY = {
  [AlertLevel.NORMAL]: 1,
  [AlertLevel.ADVISORY]: 2,
  [AlertLevel.WATCH]: 3,
  [AlertLevel.WARNING]: 4,
  [AlertLevel.EMERGENCY]: 5
} as const;

export const USER_ROLE_HIERARCHY = {
  [UserRole.VIEWER]: 1,
  [UserRole.OPERATOR]: 2,
  [UserRole.ADMIN]: 3
} as const;
