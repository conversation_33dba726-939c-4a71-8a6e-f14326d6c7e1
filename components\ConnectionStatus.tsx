/**
 * 🌋 Volcano App - Connection Status
 * Componente para mostrar el estado de conexión con la API y WebSocket
 */

import React from 'react';
import { View, StyleSheet } from 'react-native';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Colors } from '@/constants/Colors';
import { Layout, Spacing, BorderRadius } from '@/constants/Layout';
import { AccessibleText } from './ui/AccessibleText';
import { SecondaryButton } from './ui/AccessibleButton';
import { useHealthCheck } from '@/hooks/useApi';
import { useWebSocket } from '@/services/websocket';

export function ConnectionStatus() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  
  // API status
  const { data: healthData, isLoading, isError, refetch } = useHealthCheck();
  
  // WebSocket status
  const { isConnected: wsConnected, connectionError, connect: wsConnect } = useWebSocket();

  const getApiStatus = () => {
    if (isLoading) return { text: '⏳ Conectando...', color: 'secondary' as const };
    if (isError) return { text: '❌ Sin conexión', color: 'error' as const };
    if (healthData) return { text: '✅ Conectado', color: 'success' as const };
    return { text: '⚪ Desconocido', color: 'secondary' as const };
  };

  const getWebSocketStatus = () => {
    if (wsConnected) return { text: '✅ Conectado', color: 'success' as const };
    if (connectionError) return { text: `❌ Error: ${connectionError}`, color: 'error' as const };
    return { text: '⚪ Desconectado', color: 'secondary' as const };
  };

  const apiStatus = getApiStatus();
  const wsStatus = getWebSocketStatus();

  const styles = StyleSheet.create({
    container: {
      backgroundColor: colors.backgroundSecondary,
      borderRadius: BorderRadius.md,
      padding: Spacing.sm,
      margin: Spacing.sm,
    },
    
    row: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: Spacing.xs,
    },
    
    buttonRow: {
      flexDirection: 'row',
      gap: Spacing.sm,
      marginTop: Spacing.sm,
    },
    
    button: {
      flex: 1,
    },
  });

  return (
    <View style={styles.container}>
      <AccessibleText variant="button" style={{ marginBottom: Spacing.sm }}>
        🔗 Estado de Conexión
      </AccessibleText>
      
      <View style={styles.row}>
        <AccessibleText variant="body">API Backend:</AccessibleText>
        <AccessibleText variant="body" color={apiStatus.color}>
          {apiStatus.text}
        </AccessibleText>
      </View>
      
      <View style={styles.row}>
        <AccessibleText variant="body">WebSocket:</AccessibleText>
        <AccessibleText variant="body" color={wsStatus.color}>
          {wsStatus.text}
        </AccessibleText>
      </View>
      
      {healthData && (
        <View style={styles.row}>
          <AccessibleText variant="bodySmall" color="muted">
            Última actualización: {new Date(healthData.timestamp).toLocaleTimeString()}
          </AccessibleText>
        </View>
      )}
      
      <View style={styles.buttonRow}>
        <SecondaryButton 
          style={styles.button} 
          onPress={() => refetch()}
          disabled={isLoading}
        >
          Test API
        </SecondaryButton>
        
        <SecondaryButton 
          style={styles.button} 
          onPress={() => wsConnect()}
          disabled={wsConnected}
        >
          Connect WS
        </SecondaryButton>
      </View>
    </View>
  );
}
