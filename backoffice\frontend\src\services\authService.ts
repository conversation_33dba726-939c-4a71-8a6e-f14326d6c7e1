/**
 * 🌋 Volcano App Frontend - Servicio de Autenticación
 * Manejo de APIs de autenticación y tokens JWT
 */

import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { ApiResponse } from '../types/api';
import {
    ChangePasswordRequest,
    LoginRequest,
    LoginResponse,
    RefreshTokenRequest,
    User
} from '../types/auth';

// =====================================================
// CONFIGURACIÓN DE AXIOS
// =====================================================

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001';

class AuthService {
  private api: AxiosInstance;
  private authToken: string | null = null;

  constructor() {
    this.api = axios.create({
      baseURL: API_BASE_URL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Interceptor para agregar token de autorización
    this.api.interceptors.request.use(
      (config) => {
        if (this.authToken) {
          config.headers.Authorization = `Bearer ${this.authToken}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );
  }

  // =====================================================
  // GESTIÓN DE TOKENS
  // =====================================================

  setAuthToken(token: string | null) {
    this.authToken = token;
  }

  getAuthToken(): string | null {
    return this.authToken;
  }

  // =====================================================
  // INTERCEPTORES
  // =====================================================

  setupResponseInterceptor(onTokenExpired: () => Promise<boolean>) {
    return this.api.interceptors.response.use(
      (response) => response,
      async (error) => {
        const originalRequest = error.config;

        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;

          try {
            const refreshed = await onTokenExpired();
            if (refreshed) {
              // Reintentar la petición original con el nuevo token
              return this.api(originalRequest);
            }
          } catch (refreshError) {
            return Promise.reject(error);
          }
        }

        return Promise.reject(error);
      }
    );
  }

  removeResponseInterceptor(interceptorId: number) {
    this.api.interceptors.response.eject(interceptorId);
  }

  // =====================================================
  // MÉTODOS DE AUTENTICACIÓN
  // =====================================================

  /**
   * Iniciar sesión
   */
  async login(email: string, password: string): Promise<LoginResponse> {
    const response: AxiosResponse<ApiResponse<LoginResponse>> = await this.api.post(
      '/auth/login',
      { email, password } as LoginRequest
    );

    if (!response.data.success || !response.data.data) {
      throw new Error(response.data.error || 'Login failed');
    }

    return response.data.data;
  }

  /**
   * Cerrar sesión
   */
  async logout(): Promise<void> {
    await this.api.post('/auth/logout');
  }

  /**
   * Renovar token de acceso
   */
  async refreshToken(refreshToken: string): Promise<LoginResponse> {
    const response: AxiosResponse<ApiResponse<LoginResponse>> = await this.api.post(
      '/auth/refresh',
      { refresh_token: refreshToken } as RefreshTokenRequest
    );

    if (!response.data.success || !response.data.data) {
      throw new Error(response.data.error || 'Token refresh failed');
    }

    return response.data.data;
  }

  /**
   * Obtener perfil del usuario autenticado
   */
  async getProfile(): Promise<User> {
    const response: AxiosResponse<ApiResponse<User>> = await this.api.get('/auth/me');

    if (!response.data.success || !response.data.data) {
      throw new Error(response.data.error || 'Failed to get profile');
    }

    return response.data.data;
  }

  /**
   * Cambiar contraseña
   */
  async changePassword(
    currentPassword: string, 
    newPassword: string
  ): Promise<void> {
    const response: AxiosResponse<ApiResponse<void>> = await this.api.put(
      '/auth/change-password',
      {
        current_password: currentPassword,
        new_password: newPassword
      } as ChangePasswordRequest
    );

    if (!response.data.success) {
      throw new Error(response.data.error || 'Failed to change password');
    }
  }

  // =====================================================
  // UTILIDADES
  // =====================================================

  /**
   * Verifica si el usuario está autenticado
   */
  isAuthenticated(): boolean {
    return !!this.authToken;
  }

  /**
   * Obtiene información del token JWT (sin verificar)
   */
  getTokenInfo(token: string): any {
    try {
      const payload = token.split('.')[1];
      const decoded = atob(payload);
      return JSON.parse(decoded);
    } catch (error) {
      return null;
    }
  }

  /**
   * Verifica si el token está expirado
   */
  isTokenExpired(token: string): boolean {
    try {
      const tokenInfo = this.getTokenInfo(token);
      if (!tokenInfo || !tokenInfo.exp) {
        return true;
      }

      const currentTime = Math.floor(Date.now() / 1000);
      return tokenInfo.exp < currentTime;
    } catch (error) {
      return true;
    }
  }

  /**
   * Obtiene el tiempo restante del token en segundos
   */
  getTokenTimeRemaining(token: string): number {
    try {
      const tokenInfo = this.getTokenInfo(token);
      if (!tokenInfo || !tokenInfo.exp) {
        return 0;
      }

      const currentTime = Math.floor(Date.now() / 1000);
      return Math.max(0, tokenInfo.exp - currentTime);
    } catch (error) {
      return 0;
    }
  }

  // =====================================================
  // MANEJO DE ERRORES
  // =====================================================

  /**
   * Procesa errores de respuesta de la API
   */
  handleApiError(error: any): string {
    if (error.response) {
      // Error de respuesta del servidor
      const message = error.response.data?.error || error.response.data?.message;
      if (message) return message;

      switch (error.response.status) {
        case 400:
          return 'Datos inválidos';
        case 401:
          return 'Credenciales inválidas';
        case 403:
          return 'No tienes permisos para realizar esta acción';
        case 404:
          return 'Recurso no encontrado';
        case 429:
          return 'Demasiadas peticiones. Intenta más tarde';
        case 500:
          return 'Error interno del servidor';
        default:
          return `Error del servidor (${error.response.status})`;
      }
    } else if (error.request) {
      // Error de red
      return 'Error de conexión. Verifica tu conexión a internet';
    } else {
      // Error de configuración
      return error.message || 'Error inesperado';
    }
  }
}

// =====================================================
// INSTANCIA SINGLETON
// =====================================================

export const authService = new AuthService();
export default authService;
