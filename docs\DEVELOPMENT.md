# 🛠️ Guía de Desarrollo - Volcano App Backend

Esta guía proporciona información detallada para desarrolladores que trabajen en el backend de Volcano App.

## 🚀 Setup de Desarrollo

### Prerrequisitos
- Node.js 18+ (recomendado: usar nvm)
- npm 9+ o yarn 1.22+
- Git
- Editor con soporte TypeScript (VS Code recomendado)
- Cuenta de Supabase

### Configuración Inicial

```bash
# 1. Clonar repositorio
git clone <repository-url>
cd volcanoApp/backoffice/backend

# 2. Instalar dependencias
npm install

# 3. Configurar variables de entorno
cp .env.example .env
# Editar .env con configuraciones de desarrollo

# 4. Verificar configuración
npm run build
npm run dev
```

### Variables de Entorno de Desarrollo

```bash
# .env para desarrollo
NODE_ENV=development
PORT=3001
HOST=localhost

# Supabase (usar proyecto de desarrollo)
SUPABASE_URL=https://your-dev-project.supabase.co
SUPABASE_ANON_KEY=your-dev-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-dev-service-key

# JWT (usar secretos de desarrollo)
JWT_SECRET=dev-jwt-secret-change-in-production
JWT_REFRESH_SECRET=dev-refresh-secret-change-in-production
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Seguridad (valores relajados para desarrollo)
BCRYPT_ROUNDS=4
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000

# CORS (permitir localhost)
CORS_ORIGIN=http://localhost:3000,http://localhost:5173,http://localhost:8080
CORS_CREDENTIALS=true

# Logging (más verbose en desarrollo)
LOG_LEVEL=debug
LOG_FILE_PATH=./logs/dev.log
LOG_MAX_SIZE=5m
LOG_MAX_FILES=3
```

## 🏗️ Arquitectura del Código

### Principios de Diseño

1. **Separación de Responsabilidades**: Cada módulo tiene una responsabilidad específica
2. **Inversión de Dependencias**: Los módulos dependen de abstracciones, no de implementaciones
3. **Single Responsibility**: Cada función/clase tiene una sola razón para cambiar
4. **DRY (Don't Repeat Yourself)**: Evitar duplicación de código
5. **SOLID Principles**: Aplicar principios SOLID donde sea apropiado

### Estructura de Archivos

```
src/
├── config/
│   └── env.ts              # Configuración centralizada
├── controllers/            # Lógica de negocio
│   ├── alerts.ts          # Gestión de alertas
│   ├── audit.ts           # Sistema de auditoría
│   ├── auth.ts            # Autenticación
│   ├── config.ts          # Configuración del sistema
│   ├── mobile.ts          # API móvil
│   └── zones.ts           # Zonas de seguridad
├── middleware/             # Middleware personalizado
│   ├── auth.ts            # Autenticación y autorización
│   ├── errorHandler.ts    # Manejo de errores
│   └── validation.ts      # Validaciones
├── routes/                 # Definición de rutas
│   ├── alerts.ts          # Rutas de alertas
│   ├── audit.ts           # Rutas de auditoría
│   ├── auth.ts            # Rutas de autenticación
│   ├── config.ts          # Rutas de configuración
│   ├── index.ts           # Router principal
│   ├── mobile.ts          # Rutas móviles
│   └── zones.ts           # Rutas de zonas
├── services/               # Servicios externos
│   └── supabase.ts        # Cliente de Supabase
├── types/                  # Definiciones de tipos
│   └── index.ts           # Tipos principales
├── utils/                  # Utilidades
│   └── logger.ts          # Sistema de logging
└── index.ts               # Punto de entrada
```

### Convenciones de Código

#### Naming Conventions
```typescript
// Archivos: kebab-case
auth-controller.ts
error-handler.ts

// Funciones: camelCase
getUserById()
validateCreateAlert()

// Clases: PascalCase
class AlertController
class ValidationError

// Constantes: UPPER_SNAKE_CASE
const MAX_RETRY_ATTEMPTS = 3
const DEFAULT_PAGE_SIZE = 20

// Interfaces: PascalCase con 'I' prefix opcional
interface User
interface IUserRepository

// Enums: PascalCase
enum UserRole
enum AlertLevel
```

#### Estructura de Funciones
```typescript
/**
 * Descripción de la función
 * @param param1 - Descripción del parámetro
 * @param param2 - Descripción del parámetro
 * @returns Descripción del valor de retorno
 */
export async function functionName(
  param1: Type1,
  param2: Type2
): Promise<ReturnType> {
  try {
    // Validaciones
    if (!param1) {
      throw new ValidationError('param1 is required');
    }

    // Lógica principal
    const result = await someOperation(param1, param2);

    // Logging
    logger.info('Operation completed', { param1, param2, result });

    return result;
  } catch (error) {
    logger.error('Operation failed', { error, param1, param2 });
    throw error;
  }
}
```

## 🧪 Testing

### Configuración de Testing

```bash
# Instalar dependencias de testing
npm install --save-dev jest @types/jest ts-jest supertest @types/supertest

# Ejecutar tests
npm test

# Tests en modo watch
npm run test:watch

# Coverage
npm run test:coverage
```

### Estructura de Tests

```
tests/
├── unit/                   # Tests unitarios
│   ├── controllers/
│   ├── middleware/
│   ├── services/
│   └── utils/
├── integration/            # Tests de integración
│   ├── routes/
│   └── database/
├── fixtures/               # Datos de prueba
│   ├── users.json
│   ├── alerts.json
│   └── zones.json
├── helpers/                # Utilidades de testing
│   ├── setup.ts
│   ├── teardown.ts
│   └── mocks.ts
└── config/
    └── jest.config.js
```

### Ejemplo de Test

```typescript
// tests/unit/controllers/alerts.test.ts
import { Request, Response } from 'express';
import { getAlerts } from '@/controllers/alerts';
import { supabaseAdmin } from '@/services/supabase';

// Mock Supabase
jest.mock('@/services/supabase');
const mockSupabase = supabaseAdmin as jest.Mocked<typeof supabaseAdmin>;

describe('AlertsController', () => {
  describe('getAlerts', () => {
    it('should return paginated alerts', async () => {
      // Arrange
      const mockAlerts = [
        { id: '1', title: 'Test Alert', alert_level: 'WARNING' }
      ];
      
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          order: jest.fn().mockReturnValue({
            range: jest.fn().mockResolvedValue({
              data: mockAlerts,
              error: null,
              count: 1
            })
          })
        })
      } as any);

      const req = {
        query: { page: '1', limit: '20' }
      } as unknown as Request;
      
      const res = {
        json: jest.fn(),
        status: jest.fn().mockReturnThis()
      } as unknown as Response;

      // Act
      await getAlerts(req as any, res);

      // Assert
      expect(res.json).toHaveBeenCalledWith({
        success: true,
        data: mockAlerts,
        pagination: expect.any(Object),
        timestamp: expect.any(Date)
      });
    });
  });
});
```

## 🔍 Debugging

### VS Code Configuration

```json
// .vscode/launch.json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug Backend",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/src/index.ts",
      "outFiles": ["${workspaceFolder}/dist/**/*.js"],
      "runtimeArgs": ["-r", "ts-node/register"],
      "env": {
        "NODE_ENV": "development"
      },
      "console": "integratedTerminal",
      "restart": true,
      "protocol": "inspector"
    }
  ]
}
```

### Logging para Debug

```typescript
// Usar diferentes niveles de log
logger.debug('Detailed debug information', { data });
logger.info('General information', { userId });
logger.warn('Warning condition', { warning });
logger.error('Error occurred', { error });

// Logging estructurado
logger.info('User action', {
  userId: user.id,
  action: 'CREATE_ALERT',
  resource: 'alerts',
  resourceId: alert.id,
  timestamp: new Date(),
  metadata: { alertLevel: alert.level }
});
```

## 📊 Monitoreo y Métricas

### Health Checks

```typescript
// Implementar health checks personalizados
export async function customHealthCheck(): Promise<HealthStatus> {
  const checks = {
    database: await checkDatabaseConnection(),
    externalAPI: await checkExternalAPIConnection(),
    cache: await checkCacheConnection()
  };

  return {
    status: Object.values(checks).every(Boolean) ? 'healthy' : 'unhealthy',
    checks,
    timestamp: new Date()
  };
}
```

### Métricas Personalizadas

```typescript
// Ejemplo de métricas
export const metrics = {
  alertsCreated: 0,
  zonesUpdated: 0,
  authenticationAttempts: 0,
  
  incrementAlertsCreated() {
    this.alertsCreated++;
    logger.info('Metric: Alert created', { total: this.alertsCreated });
  }
};
```

## 🔒 Seguridad en Desarrollo

### Mejores Prácticas

1. **Nunca commitear secretos**: Usar .env y .gitignore
2. **Validar toda entrada**: Usar middleware de validación
3. **Sanitizar datos**: Prevenir inyecciones
4. **Logging seguro**: No loggear información sensible
5. **Dependencias actualizadas**: Usar `npm audit`

### Checklist de Seguridad

```bash
# Verificar vulnerabilidades
npm audit

# Verificar dependencias desactualizadas
npm outdated

# Linting de seguridad
npm run lint:security
```

## 🚀 Deployment

### Build para Producción

```bash
# Limpiar build anterior
rm -rf dist/

# Compilar TypeScript
npm run build

# Verificar build
node dist/index.js
```

### Variables de Entorno de Producción

```bash
# Usar valores seguros en producción
NODE_ENV=production
JWT_SECRET=<strong-random-secret>
JWT_REFRESH_SECRET=<strong-random-refresh-secret>
BCRYPT_ROUNDS=12
LOG_LEVEL=info
```

## 📚 Recursos Adicionales

### Documentación
- [Express.js Documentation](https://expressjs.com/)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [Supabase Documentation](https://supabase.com/docs)
- [Winston Logging](https://github.com/winstonjs/winston)

### Tools Recomendadas
- **VS Code Extensions**:
  - TypeScript Importer
  - ESLint
  - Prettier
  - REST Client
  - GitLens

### Comandos Útiles

```bash
# Desarrollo
npm run dev                 # Servidor con hot reload
npm run build              # Compilar TypeScript
npm run lint               # Linting
npm run lint:fix           # Fix automático de linting
npm test                   # Ejecutar tests
npm run test:watch         # Tests en modo watch

# Utilidades
npm run clean              # Limpiar build
npm run type-check         # Solo verificar tipos
npm audit                  # Verificar vulnerabilidades
npm outdated               # Verificar dependencias
```

## 🤝 Contribución

### Workflow de Desarrollo

1. **Crear branch**: `git checkout -b feature/nueva-funcionalidad`
2. **Desarrollar**: Implementar funcionalidad con tests
3. **Testing**: Verificar que todos los tests pasen
4. **Linting**: Verificar que el código pase linting
5. **Commit**: Usar conventional commits
6. **Push**: `git push origin feature/nueva-funcionalidad`
7. **Pull Request**: Crear PR con descripción detallada

### Conventional Commits

```bash
# Tipos de commit
feat: nueva funcionalidad
fix: corrección de bug
docs: cambios en documentación
style: formateo, punto y coma faltante, etc.
refactor: refactoring de código
test: agregar tests
chore: cambios en build, dependencias, etc.

# Ejemplos
git commit -m "feat: add user authentication endpoint"
git commit -m "fix: resolve memory leak in logger"
git commit -m "docs: update API documentation"
```

---

## 📞 Soporte

Para preguntas sobre desarrollo:
- **Slack**: #volcano-app-dev
- **Email**: <EMAIL>
- **Issues**: GitHub Issues para bugs y features

---

**Última actualización**: Junio 2025  
**Versión**: 1.0.0
