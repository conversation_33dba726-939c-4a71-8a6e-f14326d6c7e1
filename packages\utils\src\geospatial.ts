/**
 * 🌋 Volcano App - Utilidades Geoespaciales
 * Funciones para cálculos geográficos y manejo de coordenadas
 */

import { Coordinates, LocationPoint } from '@volcano-app/shared-types';

/**
 * Calcula la distancia entre dos puntos usando la fórmula de Haversine
 * @param lat1 Latitud del primer punto
 * @param lng1 Longitud del primer punto
 * @param lat2 Latitud del segundo punto
 * @param lng2 Longitud del segundo punto
 * @returns Distancia en kilómetros
 */
export function calculateDistance(
  lat1: number,
  lng1: number,
  lat2: number,
  lng2: number
): number {
  const R = 6371; // Radio de la Tierra en km
  const dLat = toRadians(lat2 - lat1);
  const dLng = toRadians(lng2 - lng1);
  
  const a = 
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(toRadians(lat1)) * Math.cos(toRadians(lat2)) * 
    Math.sin(dLng / 2) * Math.sin(dLng / 2);
  
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
}

/**
 * Convierte grados a radianes
 */
export function toRadians(degrees: number): number {
  return degrees * (Math.PI / 180);
}

/**
 * Convierte radianes a grados
 */
export function toDegrees(radians: number): number {
  return radians * (180 / Math.PI);
}

/**
 * Valida si las coordenadas son válidas
 */
export function isValidCoordinates(lat: number, lng: number): boolean {
  return lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180;
}

/**
 * Formatea coordenadas para mostrar
 */
export function formatCoordinates(lat: number, lng: number, precision = 6): string {
  return `${lat.toFixed(precision)}, ${lng.toFixed(precision)}`;
}

/**
 * Convierte coordenadas a formato DMS (Degrees, Minutes, Seconds)
 */
export function toDMS(coordinate: number, isLatitude: boolean): string {
  const absolute = Math.abs(coordinate);
  const degrees = Math.floor(absolute);
  const minutes = Math.floor((absolute - degrees) * 60);
  const seconds = ((absolute - degrees - minutes / 60) * 3600).toFixed(2);
  
  const direction = isLatitude 
    ? (coordinate >= 0 ? 'N' : 'S')
    : (coordinate >= 0 ? 'E' : 'W');
  
  return `${degrees}°${minutes}'${seconds}"${direction}`;
}

/**
 * Calcula el punto medio entre dos coordenadas
 */
export function getMidpoint(
  lat1: number, lng1: number,
  lat2: number, lng2: number
): Coordinates {
  const dLng = toRadians(lng2 - lng1);
  const lat1Rad = toRadians(lat1);
  const lat2Rad = toRadians(lat2);
  const lng1Rad = toRadians(lng1);

  const bx = Math.cos(lat2Rad) * Math.cos(dLng);
  const by = Math.cos(lat2Rad) * Math.sin(dLng);
  
  const lat3 = Math.atan2(
    Math.sin(lat1Rad) + Math.sin(lat2Rad),
    Math.sqrt((Math.cos(lat1Rad) + bx) * (Math.cos(lat1Rad) + bx) + by * by)
  );
  
  const lng3 = lng1Rad + Math.atan2(by, Math.cos(lat1Rad) + bx);
  
  return [toDegrees(lng3), toDegrees(lat3)];
}

/**
 * Calcula el bearing (rumbo) entre dos puntos
 */
export function getBearing(
  lat1: number, lng1: number,
  lat2: number, lng2: number
): number {
  const dLng = toRadians(lng2 - lng1);
  const lat1Rad = toRadians(lat1);
  const lat2Rad = toRadians(lat2);

  const y = Math.sin(dLng) * Math.cos(lat2Rad);
  const x = Math.cos(lat1Rad) * Math.sin(lat2Rad) - 
            Math.sin(lat1Rad) * Math.cos(lat2Rad) * Math.cos(dLng);

  const bearing = toDegrees(Math.atan2(y, x));
  return (bearing + 360) % 360;
}

/**
 * Verifica si un punto está dentro de un radio específico
 */
export function isWithinRadius(
  centerLat: number, centerLng: number,
  pointLat: number, pointLng: number,
  radiusKm: number
): boolean {
  const distance = calculateDistance(centerLat, centerLng, pointLat, pointLng);
  return distance <= radiusKm;
}

/**
 * Genera un círculo de puntos alrededor de un centro
 */
export function generateCirclePoints(
  centerLat: number, centerLng: number,
  radiusKm: number, numPoints = 32
): Coordinates[] {
  const points: Coordinates[] = [];
  const radiusInDegrees = radiusKm / 111.32; // Aproximación: 1 grado ≈ 111.32 km
  
  for (let i = 0; i < numPoints; i++) {
    const angle = (i * 360) / numPoints;
    const angleRad = toRadians(angle);
    
    const lat = centerLat + radiusInDegrees * Math.cos(angleRad);
    const lng = centerLng + radiusInDegrees * Math.sin(angleRad) / Math.cos(toRadians(centerLat));
    
    points.push([lng, lat]);
  }
  
  return points;
}

/**
 * Convierte coordenadas a formato PostGIS Point
 */
export function toPostGISPoint(lat: number, lng: number): string {
  return `POINT(${lng} ${lat})`;
}

/**
 * Convierte GeoJSON a formato PostGIS
 */
export function geoJSONToPostGIS(geoJSON: any): string {
  return `ST_GeomFromGeoJSON('${JSON.stringify(geoJSON)}')`;
}

/**
 * Valida si una geometría GeoJSON es válida
 */
export function isValidGeoJSON(geometry: any): boolean {
  if (!geometry || typeof geometry !== 'object') return false;
  
  const validTypes = ['Point', 'LineString', 'Polygon', 'MultiPoint', 'MultiLineString', 'MultiPolygon'];
  if (!validTypes.includes(geometry.type)) return false;
  
  if (!Array.isArray(geometry.coordinates)) return false;
  
  // Validaciones específicas por tipo
  switch (geometry.type) {
    case 'Point':
      return geometry.coordinates.length === 2 && 
             isValidCoordinates(geometry.coordinates[1], geometry.coordinates[0]);
    
    case 'Polygon':
      return geometry.coordinates.length > 0 &&
             Array.isArray(geometry.coordinates[0]) &&
             geometry.coordinates[0].length >= 4;
    
    default:
      return true; // Validación básica para otros tipos
  }
}
