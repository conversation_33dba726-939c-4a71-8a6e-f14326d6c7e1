/**
 * 🌋 Volcano App Backend - Rutas de Autenticación
 * Endpoints para login, logout, refresh token y gestión de perfil
 */

import { Router } from 'express';
import { authenticateToken } from '@/middleware/auth';
import { 
  validateLogin, 
  validateRefreshToken, 
  validateChangePassword 
} from '@/middleware/validation';
import { asyncHandler } from '@/middleware/errorHandler';

// Importar controladores
import {
  login,
  logout,
  refreshToken,
  getProfile,
  changePassword
} from '@/controllers/auth';

// =====================================================
// ROUTER DE AUTENTICACIÓN
// =====================================================

const router = Router();

// =====================================================
// RUTAS PÚBLICAS (SIN AUTENTICACIÓN)
// =====================================================

/**
 * Iniciar sesión
 * POST /auth/login
 */
router.post('/login', 
  validateLogin,
  asyncHandler(login)
);

/**
 * Renovar token de acceso
 * POST /auth/refresh
 */
router.post('/refresh', 
  validateRefreshToken,
  asyncHandler(refreshToken)
);

// =====================================================
// RUTAS PROTEGIDAS (CON AUTENTICACIÓN)
// =====================================================

/**
 * Cerrar sesión
 * POST /auth/logout
 */
router.post('/logout', 
  authenticateToken(),
  asyncHandler(logout)
);

/**
 * Obtener perfil del usuario autenticado
 * GET /auth/me
 */
router.get('/me', 
  authenticateToken(),
  asyncHandler(getProfile)
);

/**
 * Cambiar contraseña del usuario autenticado
 * PUT /auth/change-password
 */
router.put('/change-password', 
  authenticateToken(),
  validateChangePassword,
  asyncHandler(changePassword)
);

// =====================================================
// RUTAS DE INFORMACIÓN
// =====================================================

/**
 * Información sobre autenticación
 * GET /auth/info
 */
router.get('/info', (req, res) => {
  res.json({
    success: true,
    data: {
      endpoints: {
        login: 'POST /auth/login',
        logout: 'POST /auth/logout',
        refresh: 'POST /auth/refresh',
        profile: 'GET /auth/me',
        changePassword: 'PUT /auth/change-password'
      },
      token: {
        type: 'Bearer',
        header: 'Authorization',
        expires_in: process.env.JWT_EXPIRES_IN || '24h',
        refresh_expires_in: process.env.JWT_REFRESH_EXPIRES_IN || '7d'
      },
      security: {
        bcrypt_rounds: parseInt(process.env.BCRYPT_ROUNDS || '12'),
        password_requirements: {
          min_length: 8,
          requires_uppercase: true,
          requires_lowercase: true,
          requires_number: true
        }
      }
    },
    timestamp: new Date()
  });
});

// =====================================================
// EXPORTACIÓN
// =====================================================

export default router;
