/**
 * 🌋 Volcano App Backend - Middleware de Manejo de Errores
 * Manejo centralizado de errores HTTP y de aplicación
 */

import { Request, Response, NextFunction } from 'express';
import { logger } from '@/utils/logger';
import { AuthenticatedRequest } from '@/types';

// =====================================================
// TIPOS DE ERROR
// =====================================================

export class AppError extends Error {
  public statusCode: number;
  public isOperational: boolean;

  constructor(message: string, statusCode: number = 500, isOperational: boolean = true) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    
    Error.captureStackTrace(this, this.constructor);
  }
}

export class ValidationError extends AppError {
  constructor(message: string) {
    super(message, 400);
  }
}

export class NotFoundError extends AppError {
  constructor(resource: string = 'Resource') {
    super(`${resource} not found`, 404);
  }
}

export class UnauthorizedError extends AppError {
  constructor(message: string = 'Unauthorized') {
    super(message, 401);
  }
}

export class ForbiddenError extends AppError {
  constructor(message: string = 'Forbidden') {
    super(message, 403);
  }
}

export class ConflictError extends AppError {
  constructor(message: string) {
    super(message, 409);
  }
}

export class TooManyRequestsError extends AppError {
  constructor(message: string = 'Too many requests') {
    super(message, 429);
  }
}

// =====================================================
// MIDDLEWARE DE MANEJO DE ERRORES
// =====================================================

/**
 * Middleware principal para manejo de errores
 */
export function errorHandler() {
  return (error: any, req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    // Si la respuesta ya fue enviada, delegar al handler por defecto de Express
    if (res.headersSent) {
      return next(error);
    }

    // Determinar código de estado
    let statusCode = 500;
    let message = 'Internal server error';
    let details: any = undefined;

    if (error instanceof AppError) {
      statusCode = error.statusCode;
      message = error.message;
    } else if (error.name === 'ValidationError') {
      statusCode = 400;
      message = 'Validation failed';
      details = error.details || error.message;
    } else if (error.name === 'JsonWebTokenError') {
      statusCode = 401;
      message = 'Invalid token';
    } else if (error.name === 'TokenExpiredError') {
      statusCode = 401;
      message = 'Token expired';
    } else if (error.code === 'PGRST116') {
      // Error específico de Supabase para "not found"
      statusCode = 404;
      message = 'Resource not found';
    } else if (error.code === '23505') {
      // Error de PostgreSQL para violación de constraint único
      statusCode = 409;
      message = 'Resource already exists';
    } else if (error.code === '23503') {
      // Error de PostgreSQL para violación de foreign key
      statusCode = 400;
      message = 'Invalid reference';
    } else if (error.code === '23502') {
      // Error de PostgreSQL para violación de NOT NULL
      statusCode = 400;
      message = 'Missing required field';
    }

    // Log del error
    const errorInfo = {
      error: {
        message: error.message,
        stack: error.stack,
        name: error.name,
        code: error.code
      },
      request: {
        method: req.method,
        url: req.url,
        headers: req.headers,
        body: req.body,
        params: req.params,
        query: req.query,
        userId: req.user?.id,
        userRole: req.user?.role,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      },
      response: {
        statusCode
      }
    };

    if (statusCode >= 500) {
      logger.error('Server Error:', errorInfo);
    } else if (statusCode >= 400) {
      logger.warn('Client Error:', errorInfo);
    }

    // Respuesta de error
    const errorResponse: any = {
      success: false,
      error: message,
      timestamp: new Date()
    };

    // En desarrollo, incluir más detalles
    if (process.env.NODE_ENV === 'development') {
      errorResponse.details = details;
      errorResponse.stack = error.stack;
    } else if (details) {
      errorResponse.details = details;
    }

    res.status(statusCode).json(errorResponse);
  };
}

/**
 * Middleware para capturar rutas no encontradas
 */
export function notFoundHandler() {
  return (req: Request, res: Response, next: NextFunction) => {
    const error = new NotFoundError(`Route ${req.method} ${req.originalUrl} not found`);
    next(error);
  };
}

/**
 * Middleware para capturar errores asíncronos
 */
export function asyncHandler(fn: Function) {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
}

// =====================================================
// UTILIDADES DE ERROR
// =====================================================

/**
 * Crea un error de validación con detalles
 */
export function createValidationError(field: string, message: string, value?: any): ValidationError {
  const error = new ValidationError(`Validation failed for field '${field}': ${message}`);
  (error as any).details = { field, message, value };
  return error;
}

/**
 * Verifica si un error es operacional (esperado)
 */
export function isOperationalError(error: any): boolean {
  if (error instanceof AppError) {
    return error.isOperational;
  }
  return false;
}

/**
 * Maneja errores de Supabase y los convierte a errores de aplicación
 */
export function handleSupabaseError(error: any): AppError {
  if (error.code === 'PGRST116') {
    return new NotFoundError();
  }
  
  if (error.code === '23505') {
    return new ConflictError('Resource already exists');
  }
  
  if (error.code === '23503') {
    return new ValidationError('Invalid reference');
  }
  
  if (error.code === '23502') {
    return new ValidationError('Missing required field');
  }
  
  // Error genérico de base de datos
  return new AppError('Database operation failed', 500);
}

// =====================================================
// EXPORTACIONES
// =====================================================

export default {
  AppError,
  ValidationError,
  NotFoundError,
  UnauthorizedError,
  ForbiddenError,
  ConflictError,
  TooManyRequestsError,
  errorHandler,
  notFoundHandler,
  asyncHandler,
  createValidationError,
  isOperationalError,
  handleSupabaseError
};
