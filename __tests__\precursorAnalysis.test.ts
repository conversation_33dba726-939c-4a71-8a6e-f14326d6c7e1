/**
 * 🌋 Volcano App - Tests para Análisis de Precursores
 * Tests unitarios para validar funciones matemáticas y lógica de alertas
 */

import {
  validarDatos,
  filtrarValoresInvalidos,
  calcularPrimeraDerivada,
  calcularSegundaDerivada,
  analizarPrecursor,
  determinarNivelAlerta,
  analisisCompleto,
  obtenerUltimoValorValido,
  formatearValor,
  obtenerEstadisticas,
} from '../utils/precursorAnalysis';

import {
  UMBRALES_DEFAULT,
  DATOS_PRUEBA,
  UmbralesAlerta,
} from '../types/precursor';

describe('Análisis de Precursores Volcánicos', () => {
  
  describe('validarDatos', () => {
    it('debe validar datos correctos', () => {
      expect(validarDatos([1, 2, 3, 4])).toBe(true);
      expect(validarDatos([0, -1, 5.5, 10])).toBe(true);
    });

    it('debe rechazar datos inválidos', () => {
      expect(validarDatos([])).toBe(false);
      expect(validarDatos([1])).toBe(false);
      expect(validarDatos([1, NaN, 3])).toBe(false);
      expect(validarDatos([1, Infinity, 3])).toBe(false);
      expect(validarDatos('no es array' as any)).toBe(false);
    });
  });

  describe('filtrarValoresInvalidos', () => {
    it('debe filtrar valores inválidos', () => {
      const datos = [1, NaN, 3, Infinity, 5, -Infinity, 7];
      const resultado = filtrarValoresInvalidos(datos);
      expect(resultado).toEqual([1, 3, 5, 7]);
    });

    it('debe mantener valores válidos', () => {
      const datos = [1, 2, 3, 4, 5];
      const resultado = filtrarValoresInvalidos(datos);
      expect(resultado).toEqual(datos);
    });
  });

  describe('calcularPrimeraDerivada', () => {
    it('debe calcular primera derivada correctamente', () => {
      const datos = [2, 3, 3, 4, 6];
      const resultado = calcularPrimeraDerivada(datos);
      expect(resultado).toEqual([null, 1, 0, 1, 2]);
    });

    it('debe manejar datos de prueba', () => {
      const resultado = calcularPrimeraDerivada(DATOS_PRUEBA.valores);
      expect(resultado[0]).toBe(null);
      expect(resultado[1]).toBe(1); // 3 - 2 = 1
      expect(resultado[2]).toBe(0); // 3 - 3 = 0
      expect(resultado[3]).toBe(1); // 4 - 3 = 1
    });

    it('debe lanzar error con datos inválidos', () => {
      expect(() => calcularPrimeraDerivada([])).toThrow();
      expect(() => calcularPrimeraDerivada([1])).toThrow();
    });
  });

  describe('calcularSegundaDerivada', () => {
    it('debe calcular segunda derivada correctamente', () => {
      const primeraderivada = [null, 1, 0, 1, 2];
      const resultado = calcularSegundaDerivada(primeraderivada);
      expect(resultado).toEqual([null, null, -1, 1, 1]);
    });

    it('debe manejar valores null en primera derivada', () => {
      const primeraderivada = [null, 1, null, 2, 3];
      const resultado = calcularSegundaDerivada(primeraderivada);
      expect(resultado[0]).toBe(null);
      expect(resultado[1]).toBe(null);
      expect(resultado[2]).toBe(null); // null - 1 = null
      expect(resultado[3]).toBe(null); // 2 - null = null
      expect(resultado[4]).toBe(1); // 3 - 2 = 1
    });

    it('debe lanzar error con datos insuficientes', () => {
      expect(() => calcularSegundaDerivada([])).toThrow();
      expect(() => calcularSegundaDerivada([null])).toThrow();
    });
  });

  describe('analizarPrecursor', () => {
    it('debe realizar análisis completo con datos de prueba', () => {
      const resultado = analizarPrecursor(DATOS_PRUEBA.valores);
      
      expect(resultado.datosOriginales).toEqual(DATOS_PRUEBA.valores);
      expect(resultado.primeraderivada).toHaveLength(DATOS_PRUEBA.valores.length);
      expect(resultado.segundaDerivada).toHaveLength(DATOS_PRUEBA.valores.length);
      expect(resultado.primeraderivada[0]).toBe(null);
      expect(resultado.segundaDerivada[0]).toBe(null);
      expect(resultado.segundaDerivada[1]).toBe(null);
    });

    it('debe incluir timestamps cuando se solicite', () => {
      const resultado = analizarPrecursor(DATOS_PRUEBA.valores, {
        incluirTimestamps: true,
      });
      
      expect(resultado.timestamps).toBeDefined();
      expect(resultado.timestamps).toHaveLength(DATOS_PRUEBA.valores.length);
    });

    it('debe filtrar valores inválidos cuando se solicite', () => {
      const datosConInvalidos = [1, NaN, 3, 4, Infinity, 6];
      const resultado = analizarPrecursor(datosConInvalidos, {
        filtrarInvalidos: true,
      });
      
      expect(resultado.datosOriginales).toEqual([1, 3, 4, 6]);
    });
  });

  describe('obtenerUltimoValorValido', () => {
    it('debe obtener último valor válido', () => {
      expect(obtenerUltimoValorValido([1, 2, null, 4, null])).toBe(4);
      expect(obtenerUltimoValorValido([null, null, 3])).toBe(3);
      expect(obtenerUltimoValorValido([1, 2, 3])).toBe(3);
    });

    it('debe retornar null si no hay valores válidos', () => {
      expect(obtenerUltimoValorValido([null, null, null])).toBe(null);
      expect(obtenerUltimoValorValido([])).toBe(null);
    });
  });

  describe('determinarNivelAlerta', () => {
    const umbrales: UmbralesAlerta = { verde: 1, amarillo: 5, rojo: 5 };

    it('debe determinar nivel Verde', () => {
      const segundaDerivada = [null, null, 0.5, 1, 0.8];
      const resultado = determinarNivelAlerta(segundaDerivada, umbrales);
      
      expect(resultado.nivel).toBe('Verde');
      expect(resultado.valor).toBe(0.8);
      expect(resultado.mensaje).toContain('estable');
    });

    it('debe determinar nivel Amarillo', () => {
      const segundaDerivada = [null, null, 1.5, 3, 4];
      const resultado = determinarNivelAlerta(segundaDerivada, umbrales);
      
      expect(resultado.nivel).toBe('Amarillo');
      expect(resultado.valor).toBe(4);
      expect(resultado.mensaje).toContain('acelerando');
    });

    it('debe determinar nivel Rojo', () => {
      const segundaDerivada = [null, null, 2, 6, 8];
      const resultado = determinarNivelAlerta(segundaDerivada, umbrales);
      
      expect(resultado.nivel).toBe('Rojo');
      expect(resultado.valor).toBe(8);
      expect(resultado.mensaje).toContain('peligrosa');
    });

    it('debe manejar datos sin valores válidos', () => {
      const segundaDerivada = [null, null, null];
      const resultado = determinarNivelAlerta(segundaDerivada, umbrales);
      
      expect(resultado.nivel).toBe('Verde');
      expect(resultado.valor).toBe(null);
      expect(resultado.mensaje).toContain('datos suficientes');
    });

    it('debe usar umbrales por defecto', () => {
      const segundaDerivada = [null, null, 3];
      const resultado = determinarNivelAlerta(segundaDerivada);
      
      expect(resultado.nivel).toBe('Amarillo');
      expect(resultado.valor).toBe(3);
    });
  });

  describe('analisisCompleto', () => {
    it('debe realizar análisis completo con datos de prueba', () => {
      const { analisis, alerta } = analisisCompleto(DATOS_PRUEBA.valores);
      
      expect(analisis.datosOriginales).toEqual(DATOS_PRUEBA.valores);
      expect(alerta.nivel).toBeDefined();
      expect(alerta.mensaje).toBeDefined();
      expect(alerta.timestamp).toBeInstanceOf(Date);
    });

    it('debe detectar escalada peligrosa en datos de prueba', () => {
      // Los datos de prueba [2, 3, 3, 4, 6, 9, 14, 22, 35, 50, 68] 
      // deberían generar una alerta Roja debido a la aceleración
      const { alerta } = analisisCompleto(DATOS_PRUEBA.valores);
      
      expect(alerta.nivel).toBe('Rojo');
      expect(alerta.valor).toBeGreaterThan(5);
    });
  });

  describe('formatearValor', () => {
    it('debe formatear valores numéricos', () => {
      expect(formatearValor(3.14159, 2)).toBe('3.14');
      expect(formatearValor(10, 0)).toBe('10');
      expect(formatearValor(1.5, 1)).toBe('1.5');
    });

    it('debe manejar valores null', () => {
      expect(formatearValor(null)).toBe('N/A');
      expect(formatearValor(null, 3)).toBe('N/A');
    });
  });

  describe('obtenerEstadisticas', () => {
    it('debe calcular estadísticas correctas', () => {
      const datos = [1, 2, null, 4, 5];
      const stats = obtenerEstadisticas(datos);
      
      expect(stats.min).toBe(1);
      expect(stats.max).toBe(5);
      expect(stats.promedio).toBe(3); // (1+2+4+5)/4 = 3
      expect(stats.count).toBe(4);
    });

    it('debe manejar arrays sin valores válidos', () => {
      const datos = [null, null, null];
      const stats = obtenerEstadisticas(datos);
      
      expect(stats.min).toBe(null);
      expect(stats.max).toBe(null);
      expect(stats.promedio).toBe(null);
      expect(stats.count).toBe(0);
    });

    it('debe manejar arrays vacíos', () => {
      const stats = obtenerEstadisticas([]);
      
      expect(stats.min).toBe(null);
      expect(stats.max).toBe(null);
      expect(stats.promedio).toBe(null);
      expect(stats.count).toBe(0);
    });
  });

  describe('Casos Edge', () => {
    it('debe manejar datos con un solo valor válido', () => {
      expect(() => analizarPrecursor([5])).toThrow();
    });

    it('debe manejar datos con valores negativos', () => {
      const datos = [-5, -3, -1, 2, 5];
      const resultado = analizarPrecursor(datos);
      
      expect(resultado.datosOriginales).toEqual(datos);
      expect(resultado.primeraderivada[1]).toBe(2); // -3 - (-5) = 2
    });

    it('debe manejar datos con valores muy grandes', () => {
      const datos = [1000000, 2000000, 4000000];
      const resultado = analizarPrecursor(datos);
      
      expect(resultado.primeraderivada[1]).toBe(1000000);
      expect(resultado.segundaDerivada[2]).toBe(1000000);
    });
  });
});
