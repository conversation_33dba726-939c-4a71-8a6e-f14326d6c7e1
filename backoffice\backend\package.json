{"name": "volcano-app-backend", "version": "1.0.0", "description": "Backend API para el sistema de administración de Volcano App", "main": "dist/index.js", "scripts": {"dev": "nodemon --exec ts-node -r tsconfig-paths/register src/index.ts", "build": "tsc", "start": "node -r module-alias/register dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false --passWithNoTests", "test:unit": "jest --testPathPattern=unit", "test:integration": "jest --testPathPattern=integration", "test:controllers": "jest --testPathPattern=controllers", "test:services": "jest --testPathPattern=services", "test:middleware": "jest --testPathPattern=middleware", "test:verbose": "jest --verbose", "test:silent": "jest --silent", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "type-check": "tsc --noEmit", "db:migrate": "node dist/scripts/migrate.js", "db:seed": "node dist/scripts/seed.js"}, "keywords": ["volcano", "emergency", "api", "typescript", "express", "supabase"], "author": "Volcano App Team", "license": "MIT", "dependencies": {"@hookform/resolvers": "^5.0.1", "@react-native-async-storage/async-storage": "^2.1.2", "@supabase/supabase-js": "^2.39.0", "@types/leaflet": "^1.9.18", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "ioredis": "^5.3.2", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "leaflet": "^1.9.4", "lucide-react": "^0.511.0", "module-alias": "^2.2.3", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "react-hook-form": "^7.56.4", "react-hot-toast": "^2.5.2", "react-leaflet": "^5.0.0", "socket.io": "^4.7.5", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "tsconfig-paths": "^4.2.0", "uuid": "^9.0.1", "winston": "^3.11.0", "ws": "^8.16.0", "yup": "^1.6.1"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/node": "^20.10.0", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.6", "@types/uuid": "^9.0.7", "@types/ws": "^8.5.10", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0", "jest": "^29.7.0", "nodemon": "^3.0.1", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.3.2"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "_moduleAliases": {"@": "./dist"}}