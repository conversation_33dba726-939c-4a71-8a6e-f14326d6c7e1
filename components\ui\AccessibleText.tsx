/**
 * AccessibleText Component
 * Provides consistent, accessible text rendering with scaling support
 */

import React from 'react';
import { Text, TextProps } from 'react-native';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Colors } from '@/constants/Colors';
import { Typography, scaleTypography, AccessibilityScales } from '@/constants/Typography';

export interface AccessibleTextProps extends TextProps {
  variant?: keyof typeof Typography;
  color?: 'primary' | 'secondary' | 'muted' | 'inverse' | 'success' | 'warning' | 'error' | 'info';
  scale?: keyof typeof AccessibilityScales;
  emergency?: boolean;
  children: React.ReactNode;
}

export function AccessibleText({
  variant = 'body',
  color = 'primary',
  scale = 'normal',
  emergency = false,
  style,
  children,
  ...props
}: AccessibleTextProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];

  // Get text color
  const getTextColor = () => {
    switch (color) {
      case 'primary':
        return colors.text;
      case 'secondary':
        return colors.textSecondary;
      case 'muted':
        return colors.textMuted;
      case 'inverse':
        return colors.textInverse;
      case 'success':
        return colors.success;
      case 'warning':
        return colors.warning;
      case 'error':
        return colors.error;
      case 'info':
        return colors.info;
      default:
        return colors.text;
    }
  };

  // Simple base styles based on variant
  const getBaseStyle = () => {
    switch (variant) {
      case 'h1':
        return { fontSize: 36, fontWeight: 'bold' as const };
      case 'h2':
        return { fontSize: 30, fontWeight: 'bold' as const };
      case 'h3':
        return { fontSize: 24, fontWeight: '600' as const };
      case 'h4':
        return { fontSize: 20, fontWeight: '600' as const };
      case 'bodyLarge':
        return { fontSize: 18, fontWeight: 'normal' as const };
      case 'body':
        return { fontSize: 16, fontWeight: 'normal' as const };
      case 'bodySmall':
        return { fontSize: 14, fontWeight: 'normal' as const };
      case 'button':
        return { fontSize: 16, fontWeight: '600' as const };
      case 'buttonLarge':
        return { fontSize: 18, fontWeight: '600' as const };
      case 'alert':
        return { fontSize: 18, fontWeight: 'bold' as const };
      case 'alertLarge':
        return { fontSize: 24, fontWeight: 'bold' as const };
      case 'caption':
        return { fontSize: 12, fontWeight: 'normal' as const };
      default:
        return { fontSize: 16, fontWeight: 'normal' as const };
    }
  };

  // Emergency mode overrides
  const emergencyStyle = emergency ? {
    fontWeight: 'bold' as const,
    textTransform: 'uppercase' as const,
  } : {};

  const finalStyle = [
    getBaseStyle(),
    { color: getTextColor() },
    emergencyStyle,
    style,
  ];

  return (
    <Text
      style={finalStyle}
      accessible={true}
      {...props}
    >
      {children}
    </Text>
  );
}

// Convenience components for common text types
export function Heading1(props: Omit<AccessibleTextProps, 'variant'>) {
  return <AccessibleText variant="h1" {...props} />;
}

export function Heading2(props: Omit<AccessibleTextProps, 'variant'>) {
  return <AccessibleText variant="h2" {...props} />;
}

export function Heading3(props: Omit<AccessibleTextProps, 'variant'>) {
  return <AccessibleText variant="h3" {...props} />;
}

export function Heading4(props: Omit<AccessibleTextProps, 'variant'>) {
  return <AccessibleText variant="h4" {...props} />;
}

export function BodyText(props: Omit<AccessibleTextProps, 'variant'>) {
  return <AccessibleText variant="body" {...props} />;
}

export function BodyLarge(props: Omit<AccessibleTextProps, 'variant'>) {
  return <AccessibleText variant="bodyLarge" {...props} />;
}

export function BodySmall(props: Omit<AccessibleTextProps, 'variant'>) {
  return <AccessibleText variant="bodySmall" {...props} />;
}

export function Caption(props: Omit<AccessibleTextProps, 'variant'>) {
  return <AccessibleText variant="caption" {...props} />;
}

export function AlertText(props: Omit<AccessibleTextProps, 'variant'>) {
  return <AccessibleText variant="alert" emergency {...props} />;
}

export function AlertTextLarge(props: Omit<AccessibleTextProps, 'variant'>) {
  return <AccessibleText variant="alertLarge" emergency {...props} />;
}
