/**
 * Volcano App Layout System
 * Consistent spacing, sizing, and layout patterns
 * Optimized for touch accessibility and various screen sizes
 */

import { Dimensions } from 'react-native';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

// Spacing scale (8pt grid system)
export const Spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  '2xl': 48,
  '3xl': 64,
  '4xl': 96,
};

// Border radius for consistent rounded corners
export const BorderRadius = {
  none: 0,
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  '2xl': 24,
  full: 9999,
};

// Touch target sizes (accessibility compliant)
export const TouchTargets = {
  minimum: 44, // iOS/Android minimum
  comfortable: 48,
  large: 56,
  extraLarge: 64,
};

// Icon sizes
export const IconSizes = {
  xs: 16,
  sm: 20,
  md: 24,
  lg: 32,
  xl: 40,
  '2xl': 48,
  '3xl': 64,
};

// Container widths
export const ContainerWidths = {
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
};

// Screen breakpoints
export const Breakpoints = {
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
};

// Z-index layers
export const ZIndex = {
  base: 0,
  overlay: 10,
  modal: 20,
  alert: 30,
  emergency: 40,
  tooltip: 50,
};

// Shadow presets
export const Shadows = {
  sm: {
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  md: {
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  lg: {
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 5,
  },
  xl: {
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.2,
    shadowRadius: 16,
    elevation: 8,
  },
};

// Layout patterns
export const Layout = {
  // Screen dimensions
  screen: {
    width: screenWidth,
    height: screenHeight,
  },
  
  // Safe areas and padding
  safeArea: {
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.md,
  },
  
  // Container patterns
  container: {
    flex: 1,
    paddingHorizontal: Spacing.md,
  },
  
  containerCentered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: Spacing.md,
  },
  
  // Card patterns
  card: {
    backgroundColor: 'white',
    borderRadius: BorderRadius.lg,
    padding: Spacing.md,
    ...Shadows.md,
  },
  
  cardCompact: {
    backgroundColor: 'white',
    borderRadius: BorderRadius.md,
    padding: Spacing.sm,
    ...Shadows.sm,
  },
  
  // Button patterns
  button: {
    minHeight: TouchTargets.comfortable,
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.md,
    justifyContent: 'center',
    alignItems: 'center',
  },
  
  buttonLarge: {
    minHeight: TouchTargets.large,
    paddingHorizontal: Spacing.xl,
    paddingVertical: Spacing.md,
    borderRadius: BorderRadius.lg,
    justifyContent: 'center',
    alignItems: 'center',
  },
  
  // Emergency button (extra large touch target)
  emergencyButton: {
    minHeight: TouchTargets.extraLarge,
    paddingHorizontal: Spacing['2xl'],
    paddingVertical: Spacing.lg,
    borderRadius: BorderRadius.xl,
    justifyContent: 'center',
    alignItems: 'center',
  },
  
  // List patterns
  listItem: {
    minHeight: TouchTargets.comfortable,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    justifyContent: 'center',
  },
  
  listItemLarge: {
    minHeight: TouchTargets.large,
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    justifyContent: 'center',
  },
  
  // Header patterns
  header: {
    height: 60,
    paddingHorizontal: Spacing.md,
    justifyContent: 'center',
    alignItems: 'center',
  },
  
  headerLarge: {
    height: 80,
    paddingHorizontal: Spacing.lg,
    justifyContent: 'center',
    alignItems: 'center',
  },
  
  // Tab bar
  tabBar: {
    height: 60,
    paddingBottom: 8,
  },
  
  // Alert patterns
  alert: {
    padding: Spacing.md,
    borderRadius: BorderRadius.md,
    marginVertical: Spacing.sm,
  },
  
  alertLarge: {
    padding: Spacing.lg,
    borderRadius: BorderRadius.lg,
    marginVertical: Spacing.md,
  },
  
  // Emergency overlay
  emergencyOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: ZIndex.emergency,
  },
};

// Responsive helpers
export const isSmallScreen = screenWidth < Breakpoints.sm;
export const isMediumScreen = screenWidth >= Breakpoints.sm && screenWidth < Breakpoints.md;
export const isLargeScreen = screenWidth >= Breakpoints.md;

// Accessibility helpers
export const getMinimumTouchTarget = (size: number) => 
  Math.max(size, TouchTargets.minimum);

export const getAccessibleSpacing = (base: number, scale: number = 1) => 
  Math.round(base * scale);

// Emergency mode layout (simplified, larger targets)
export const EmergencyLayout = {
  button: {
    minHeight: 80,
    paddingHorizontal: Spacing['3xl'],
    paddingVertical: Spacing.xl,
    borderRadius: BorderRadius['2xl'],
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: Spacing.lg,
  },
  
  container: {
    flex: 1,
    paddingHorizontal: Spacing.xl,
    paddingVertical: Spacing.xl,
    justifyContent: 'center',
    alignItems: 'center',
  },
  
  text: {
    textAlign: 'center',
    marginVertical: Spacing.lg,
  },
};
