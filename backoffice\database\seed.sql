-- 🌋 Volcano App - Datos Iniciales
-- Ejecutar después de schema.sql

-- =====================================================
-- USUARIO ADMINISTRADOR INICIAL
-- =====================================================
-- Password: admin123 (cambiar en producción)
-- Hash generado con bcrypt, rounds=12
INSERT INTO admin_users (email, password_hash, full_name, role) VALUES
('<EMAIL>', '$2b$12$LQv3c1yqBwlVHpPjrU3HUOeCDk027zKgqukZTfauQyIiSdmxM9OyO', 'Administrador Principal', 'ADMIN'),
('<EMAIL>', '$2b$12$LQv3c1yqBwlVHpPjrU3HUOeCDk027zKgqukZTfauQyIiSdmxM9OyO', 'Operador SERNAGEOMIN', 'OPERATOR'),
('<EMAIL>', '$2b$12$LQv3c1yqBwlVHpPjrU3HUOeCDk027zKgqukZTfauQyIiSdmxM9OyO', 'Visualizador ONEMI', 'VIEWER');

-- =====================================================
-- ALERTAS VOLCÁNICAS INICIALES
-- =====================================================
INSERT INTO volcano_alerts (title, message, alert_level, created_by) VALUES
(
    'Estado Normal del Volcán Villarrica',
    'El volcán Villarrica presenta actividad normal. Parámetros de monitoreo dentro de rangos habituales. Se mantiene vigilancia continua.',
    'NORMAL',
    (SELECT id FROM admin_users WHERE email = '<EMAIL>')
),
(
    'Aviso Técnico - Incremento Sísmico Menor',
    'Se registra un leve incremento en la actividad sísmica del volcán. Los parámetros se mantienen dentro de rangos normales pero se recomienda mantenerse informado.',
    'ADVISORY',
    (SELECT id FROM admin_users WHERE email = '<EMAIL>')
);

-- =====================================================
-- ZONAS DE SEGURIDAD INICIALES
-- =====================================================

-- Centro de Pucón (Zona Segura)
INSERT INTO safety_zones (
    name, 
    description, 
    zone_type, 
    geometry,
    capacity,
    contact_info,
    facilities,
    created_by
) VALUES (
    'Centro de Pucón',
    'Zona urbana central de Pucón con infraestructura de emergencia y fácil acceso a rutas de evacuación.',
    'SAFE',
    ST_GeomFromText('POLYGON((-71.9520 -39.2680, -71.9480 -39.2680, -71.9480 -39.2720, -71.9520 -39.2720, -71.9520 -39.2680))', 4326),
    5000,
    '{"emergency_phone": "133", "municipality": "+56-45-2441675", "police": "+56-45-2441777"}',
    '{"hospitals": 1, "schools": 3, "emergency_centers": 2, "water": true, "electricity": true}',
    (SELECT id FROM admin_users WHERE email = '<EMAIL>')
);

-- Hospital de Pucón (Centro de Emergencia)
INSERT INTO safety_zones (
    name, 
    description, 
    zone_type, 
    geometry,
    capacity,
    contact_info,
    facilities,
    created_by
) VALUES (
    'Hospital de Pucón',
    'Centro hospitalario principal con capacidad de atención de emergencias y helipuerto.',
    'EMERGENCY',
    ST_GeomFromText('POLYGON((-71.9460 -39.2740, -71.9440 -39.2740, -71.9440 -39.2760, -71.9460 -39.2760, -71.9460 -39.2740))', 4326),
    200,
    '{"hospital": "+56-45-2441500", "emergency": "131", "ambulance": "131"}',
    '{"beds": 50, "emergency_room": true, "helipad": true, "pharmacy": true, "blood_bank": true}',
    (SELECT id FROM admin_users WHERE email = '<EMAIL>')
);

-- Escuela Básica Pucón (Refugio)
INSERT INTO safety_zones (
    name, 
    description, 
    zone_type, 
    geometry,
    capacity,
    contact_info,
    facilities,
    created_by
) VALUES (
    'Escuela Básica Pucón',
    'Establecimiento educacional habilitado como centro de refugio temporal en emergencias.',
    'SAFE',
    ST_GeomFromText('POLYGON((-71.9500 -39.2700, -71.9480 -39.2700, -71.9480 -39.2720, -71.9500 -39.2720, -71.9500 -39.2700))', 4326),
    800,
    '{"school": "+56-45-2441234", "director": "+56-9-8765-4321"}',
    '{"classrooms": 12, "gym": true, "kitchen": true, "bathrooms": 8, "playground": true}',
    (SELECT id FROM admin_users WHERE email = '<EMAIL>')
);

-- Zona de Peligro Volcánico (Radio 10km del volcán)
INSERT INTO safety_zones (
    name, 
    description, 
    zone_type, 
    geometry,
    capacity,
    contact_info,
    facilities,
    created_by
) VALUES (
    'Zona de Peligro Volcánico - Radio 10km',
    'Área de alto riesgo volcánico. Evacuación inmediata requerida en caso de alerta roja.',
    'DANGER',
    ST_Buffer(ST_GeomFromText('POINT(-71.939167 -39.420000)', 4326)::geography, 10000)::geometry,
    0,
    '{"emergency": "133", "sernageomin": "+56-2-2482-8000"}',
    '{}',
    (SELECT id FROM admin_users WHERE email = '<EMAIL>')
);

-- Ruta de Evacuación Principal (Pucón - Temuco)
INSERT INTO safety_zones (
    name, 
    description, 
    zone_type, 
    geometry,
    capacity,
    contact_info,
    facilities,
    created_by
) VALUES (
    'Ruta de Evacuación Principal',
    'Corredor de evacuación principal desde Pucón hacia Temuco por Ruta 199.',
    'EVACUATION',
    ST_GeomFromText('POLYGON((-71.9600 -39.2600, -71.8000 -38.7000, -71.7900 -38.7100, -71.9500 -39.2700, -71.9600 -39.2600))', 4326),
    NULL,
    '{"road_info": "+56-600-4000000", "carabineros": "133"}',
    '{"gas_stations": 3, "rest_areas": 2, "emergency_phones": 5}',
    (SELECT id FROM admin_users WHERE email = '<EMAIL>')
);

-- =====================================================
-- CONFIGURACIÓN ADICIONAL DEL SISTEMA
-- =====================================================
INSERT INTO system_config (key, value, description) VALUES
('app_version_min', '"1.0.0"', 'Versión mínima requerida de la app móvil'),
('maintenance_mode', 'false', 'Modo de mantenimiento del sistema'),
('alert_notification_delay_minutes', '5', 'Retraso en minutos para notificaciones de alerta'),
('zone_sync_interval_minutes', '15', 'Intervalo de sincronización de zonas con la app móvil'),
('max_concurrent_users', '1000', 'Máximo número de usuarios concurrentes'),
('backup_retention_days', '30', 'Días de retención de backups'),
('log_retention_days', '90', 'Días de retención de logs de auditoría');

-- =====================================================
-- LOGS DE AUDITORÍA INICIALES
-- =====================================================
INSERT INTO audit_logs (user_id, action, table_name, record_id, new_values) VALUES
(
    (SELECT id FROM admin_users WHERE email = '<EMAIL>'),
    'CREATE',
    'admin_users',
    (SELECT id FROM admin_users WHERE email = '<EMAIL>'),
    '{"action": "Initial admin user created"}'
),
(
    (SELECT id FROM admin_users WHERE email = '<EMAIL>'),
    'CREATE',
    'volcano_alerts',
    (SELECT id FROM volcano_alerts WHERE title = 'Estado Normal del Volcán Villarrica'),
    '{"action": "Initial normal alert created"}'
);

-- =====================================================
-- DATOS DE PRUEBA PARA DESARROLLO
-- =====================================================

-- Ubicaciones simuladas de usuarios (para testing)
INSERT INTO app_users_locations (anonymous_id, location, accuracy, app_version, device_type) VALUES
('test-user-001', ST_GeomFromText('POINT(-71.9500 -39.2700)', 4326), 10.5, '1.0.0', 'iOS'),
('test-user-002', ST_GeomFromText('POINT(-71.9480 -39.2680)', 4326), 15.2, '1.0.0', 'Android'),
('test-user-003', ST_GeomFromText('POINT(-71.9520 -39.2720)', 4326), 8.7, '1.0.0', 'iOS'),
('test-user-004', ST_GeomFromText('POINT(-71.9460 -39.2740)', 4326), 12.3, '1.0.0', 'Android'),
('test-user-005', ST_GeomFromText('POINT(-71.9440 -39.2760)', 4326), 20.1, '1.0.0', 'iOS');

-- =====================================================
-- FUNCIONES ÚTILES PARA ADMINISTRACIÓN
-- =====================================================

-- Función para obtener estadísticas rápidas
CREATE OR REPLACE FUNCTION get_system_stats()
RETURNS TABLE (
    total_alerts INTEGER,
    active_alerts INTEGER,
    total_zones INTEGER,
    active_zones INTEGER,
    total_users INTEGER,
    active_users_last_hour INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        (SELECT COUNT(*)::INTEGER FROM volcano_alerts),
        (SELECT COUNT(*)::INTEGER FROM volcano_alerts WHERE is_active = true),
        (SELECT COUNT(*)::INTEGER FROM safety_zones),
        (SELECT COUNT(*)::INTEGER FROM safety_zones WHERE is_active = true),
        (SELECT COUNT(*)::INTEGER FROM admin_users WHERE is_active = true),
        (SELECT COUNT(*)::INTEGER FROM app_users_locations WHERE reported_at > NOW() - INTERVAL '1 hour');
END;
$$ LANGUAGE plpgsql;

-- Función para limpiar datos antiguos
CREATE OR REPLACE FUNCTION cleanup_old_data()
RETURNS void AS $$
BEGIN
    -- Limpiar ubicaciones expiradas
    DELETE FROM app_users_locations WHERE expires_at < NOW();
    
    -- Limpiar logs de auditoría antiguos (más de 90 días)
    DELETE FROM audit_logs WHERE created_at < NOW() - INTERVAL '90 days';
    
    -- Limpiar alertas expiradas inactivas (más de 30 días)
    UPDATE volcano_alerts 
    SET is_active = false 
    WHERE is_active = true 
      AND expires_at < NOW() - INTERVAL '30 days';
      
    RAISE NOTICE 'Cleanup completed successfully';
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- VERIFICACIÓN DE DATOS
-- =====================================================

-- Verificar que los datos se insertaron correctamente
DO $$
DECLARE
    user_count INTEGER;
    alert_count INTEGER;
    zone_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO user_count FROM admin_users;
    SELECT COUNT(*) INTO alert_count FROM volcano_alerts;
    SELECT COUNT(*) INTO zone_count FROM safety_zones;
    
    RAISE NOTICE 'Datos iniciales insertados:';
    RAISE NOTICE '- Usuarios administradores: %', user_count;
    RAISE NOTICE '- Alertas volcánicas: %', alert_count;
    RAISE NOTICE '- Zonas de seguridad: %', zone_count;
    
    IF user_count = 0 OR alert_count = 0 OR zone_count = 0 THEN
        RAISE EXCEPTION 'Error: No se pudieron insertar todos los datos iniciales';
    END IF;
END $$;
