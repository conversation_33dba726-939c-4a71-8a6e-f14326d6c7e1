/**
 * 🌋 Volcano App - API Service
 * Configuración y servicios para conectar con el backend
 */

import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { getAPIBaseURL, logNetworkConfig } from './network-config';

// Configuración de la API
const API_CONFIG = {
  baseURL: getAPIBaseURL(),
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
};

// Log de la configuración para debugging
try {
  logNetworkConfig('API Service');
} catch (error) {
  console.log('🔧 API Service Network Configuration:', {
    platform: 'unknown',
    apiURL: API_CONFIG.baseURL,
    isDev: __DEV__
  });
}

// Crear instancia de axios
const apiClient: AxiosInstance = axios.create(API_CONFIG);

// Interceptor para requests
apiClient.interceptors.request.use(
  (config) => {
    // Agregar token de autenticación si existe
    // const token = getAuthToken();
    // if (token) {
    //   config.headers.Authorization = `Bearer ${token}`;
    // }
    
    console.log(`🌐 API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('❌ API Request Error:', error);
    return Promise.reject(error);
  }
);

// Interceptor para responses
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    console.log(`✅ API Response: ${response.status} ${response.config.url}`);
    return response;
  },
  (error) => {
    console.error('❌ API Response Error:', error.response?.status, error.response?.data);
    
    // Manejar errores específicos
    if (error.response?.status === 401) {
      // Token expirado o no válido
      console.log('🔐 Authentication required');
    } else if (error.response?.status >= 500) {
      // Error del servidor
      console.log('🚨 Server error');
    }
    
    return Promise.reject(error);
  }
);

// Tipos de datos de la API
export interface VolcanoAlert {
  id: string;
  title: string;
  message: string;
  alert_level: 'NORMAL' | 'ADVISORY' | 'WATCH' | 'WARNING' | 'EMERGENCY';
  volcano_name: string;
  volcano_lat: number;
  volcano_lng: number;
  created_at: string;
  expires_at?: string;
  metadata?: any;
}

export interface SafetyZone {
  id: string;
  name: string;
  description?: string;
  zone_type: 'SAFE' | 'EMERGENCY' | 'DANGER' | 'EVACUATION' | 'RESTRICTED';
  geometry: {
    type: string;
    coordinates: number[][][];
  };
  capacity?: number;
  contact_info?: any;
  facilities?: any;
  is_active: boolean;
  version: number;
}

export interface MobileConfig {
  mobile_app_version: string;
  volcano_coordinates: {
    lat: number;
    lng: number;
    name: string;
  };
  emergency_contacts: {
    emergency: string;
    police: string;
    fire: string;
    medical: string;
  };
  update_intervals: {
    alerts: number;
    zones: number;
    location: number;
  };
  notification_settings: {
    push_enabled: boolean;
    email_enabled: boolean;
  };
}

export interface LocationReport {
  anonymous_id: string;
  latitude: number;
  longitude: number;
  accuracy?: number;
  timestamp?: string;
  app_version?: string;
  device_type?: string;
}

export interface LocationCheckResult {
  is_in_safe_zone: boolean;
  current_zone?: SafetyZone;
  distance_to_volcano: number;
  nearest_safe_zone?: SafetyZone;
  recommendations: string[];
}

// Servicios de la API
export const apiService = {
  // Health check
  async healthCheck(): Promise<any> {
    const response = await apiClient.get('/mobile/health');
    return response.data;
  },

  // Configuración móvil
  async getMobileConfig(): Promise<MobileConfig> {
    const response = await apiClient.get('/mobile/config');
    return response.data.data;
  },

  // Alertas volcánicas
  async getCurrentAlert(): Promise<VolcanoAlert | null> {
    try {
      const response = await apiClient.get('/mobile/alerts/current');
      return response.data.data;
    } catch (error: any) {
      if (error.response?.status === 404) {
        return null; // No hay alerta activa
      }
      throw error;
    }
  },

  // Zonas de seguridad
  async getAllZones(): Promise<SafetyZone[]> {
    const response = await apiClient.get('/mobile/zones/all');
    return response.data.data;
  },

  // Reportar ubicación
  async reportLocation(location: LocationReport): Promise<void> {
    await apiClient.post('/mobile/location/report', location);
  },

  // Verificar ubicación en zonas
  async checkLocation(lat: number, lng: number): Promise<LocationCheckResult> {
    const response = await apiClient.post('/mobile/location/check', { lat, lng });
    return response.data.data;
  },

  // Sincronización masiva (para offline)
  async bulkSync(lastSync?: string): Promise<{
    alerts: VolcanoAlert[];
    zones: SafetyZone[];
    config: MobileConfig;
    timestamp: string;
  }> {
    const response = await apiClient.post('/mobile/sync', {
      last_sync: lastSync,
      include: ['alerts', 'zones', 'config']
    });
    return response.data.data;
  },

  // Verificar versión de la app
  async checkAppVersion(currentVersion: string): Promise<{
    is_compatible: boolean;
    latest_version: string;
    update_required: boolean;
    update_url?: string;
  }> {
    const response = await apiClient.get(`/mobile/version/check?version=${currentVersion}`);
    return response.data.data;
  },

  // Reportar ubicaciones en lote (para offline)
  async batchLocationReport(locations: LocationReport[]): Promise<void> {
    await apiClient.post('/mobile/location/batch', { locations });
  },
};

export default apiService;
