[2025-06-02T19:08:35.514Z] [ERROR] Failed to start Vite: spawn npm ENOENT
[2025-06-02T19:08:35.517Z] [ERROR] Vite process exited with code -4058
{
  "timestamp": "2025-06-02T19:09:22.737Z",
  "type": "javascript_error",
  "severity": "high",
  "message": "2025-06-02T19:09:22.727Z vite:config using resolved config: {\n  plugins: [\n    'vite:optimized-deps',\n    'vite:watch-package-data',\n    'vite:pre-alias',\n    'alias',\n    'vite:react-babel',\n    'vite:react-refresh',\n    'vite:modulepreload-polyfill',\n    'vite:resolve',\n    'vite:html-inline-proxy',\n    'vite:css',\n    'vite:esbuild',\n    'vite:json',\n    'vite:wasm-helper',\n    'vite:worker',\n    'vite:asset',\n    'vite:wasm-fallback',\n    'vite:define',\n    'vite:css-post',\n    'vite:worker-import-meta-url',\n    'vite:asset-import-meta-url',\n    'vite:dynamic-import-vars',\n    'vite:import-glob',\n    'vite:client-inject',\n    'vite:css-analysis',\n    'vite:import-analysis'\n  ],\n  resolve: {\n    mainFields: [ 'browser', 'module', 'jsnext:main', 'jsnext' ],\n    conditions: [],\n    extensions: [\n      '.mjs',  '.js',\n      '.mts',  '.ts',\n      '.jsx',  '.tsx',\n      '.json'\n    ],\n    dedupe: [ 'react', 'react-dom' ],\n    preserveSymlinks: false,\n    alias: [ [Object], [Object], [Object] ]\n  },\n  optimizeDeps: {\n    holdUntilCrawlEnd: true,\n    include: [\n      'react',\n      'react-dom',\n      'react',\n      'react-dom',\n      'react/jsx-dev-runtime',\n      'react/jsx-runtime'\n    ],\n    esbuildOptions: { preserveSymlinks: false, jsx: 'automatic' }\n  },\n  server: {\n    preTransformRequests: true,\n    port: 3000,\n    host: true,\n    proxy: { '/api': [Object] },\n    sourcemapIgnoreList: [Function: isInNodeModules$1],\n    middlewareMode: false,\n    fs: {\n      strict: true,\n      allow: [Array],\n      deny: [Array],\n      cachedChecks: undefined\n    }\n  },\n  build: {\n    target: [ 'es2020', 'edge88', 'firefox78', 'chrome87', 'safari14' ],\n    cssTarget: [ 'es2020', 'edge88', 'firefox78', 'chrome87', 'safari14' ],\n    outDir: 'dist',\n    assetsDir: 'assets',\n    assetsInlineLimit: 4096,\n    cssCodeSplit: true,\n    sourcemap: true,\n    rollupOptions: { output: [Object], onwarn: [Function: onwarn] },\n    minify: 'esbuild',\n    terserOptions: {},\n    write: true,\n    emptyOutDir: null,\n    copyPublicDir: true,\n    manifest: false,\n    lib: false,\n    ssr: false,\n    ssrManifest: false,\n    ssrEmitAssets: false,\n    reportCompressedSize: true,\n    chunkSizeWarningLimit: 500,\n    watch: null,\n    commonjsOptions: { include: [Array], extensions: [Array] },\n    dynamicImportVarsOptions: { warnOnError: true, exclude: [Array] },\n    modulePreload: { polyfill: true },\n    cssMinify: true\n  },\n  define: { __APP_VERSION__: '\"1.0.0\"', __DEV__: 'true' },\n  esbuild: { jsxDev: true, drop: [], jsx: 'automatic' },\n  configFile: 'C:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/vite.config.ts',\n  configFileDependencies: [\n    'C:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/vite.config.ts'\n  ],\n  inlineConfig: {\n    root: undefined,\n    base: undefined,\n    mode: undefined,\n    configFile: undefined,\n    logLevel: undefined,\n    clearScreen: undefined,\n    optimizeDeps: { force: undefined },\n    server: {}\n  },\n  root: 'C:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend',\n  base: '/',\n  decodedBase: '/',\n  rawBase: '/',\n  publicDir: 'C:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/public',\n  cacheDir: 'C:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite',\n  command: 'serve',\n  mode: 'development',\n  ssr: {\n    target: 'node',\n    optimizeDeps: { noDiscovery: true, esbuildOptions: [Object] }\n  },\n  isWorker: false,\n  mainConfig: null,\n  bundleChain: [],\n  isProduction: false,\n  css: { lightningcss: undefined },\n  preview: {\n    port: undefined,\n    strictPort: undefined,\n    host: true,\n    allowedHosts: undefined,\n    https: undefined,\n    open: undefined,\n    proxy: { '/api': [Object] },\n    cors: undefined,\n    headers: undefined\n  },\n  envDir: 'C:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend',\n  env: {\n    VITE_API_BASE_URL: 'http://localhost:3001',\n    VITE_API_TIMEOUT: '10000',\n    VITE_APP_NAME: 'Volcano App Admin',\n    VITE_APP_VERSION: '1.0.0',\n    VITE_APP_DESCRIPTION: 'Panel administrativo para gestión de alertas volcánicas',\n    VITE_DEFAULT_LAT: '-39.420000',\n    VITE_DEFAULT_LNG: '-71.939167',\n    VITE_DEFAULT_ZOOM: '10',\n    VITE_ENABLE_DEVTOOLS: 'true',\n    VITE_LOG_LEVEL: 'debug',\n    VITE_DEBUG: 'true',\n    BASE_URL: '/',\n    MODE: 'development',\n    DEV: true,\n    PROD: false\n  },\n  assetsInclude: [Function: assetsInclude],\n  logger: {\n    hasWarned: false,\n    info: [Function: info],\n    warn: [Function: warn],\n    warnOnce: [Function: warnOnce],\n    error: [Function: error],\n    clearScreen: [Function: clearScreen],\n    hasErrorLogged: [Function: hasErrorLogged]\n  },\n  packageCache: Map(1) {\n    'fnpd_C:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend' => {\n      dir: 'C:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend',\n      data: [Object],\n      hasSideEffects: [Function: hasSideEffects],\n      webResolvedImports: {},\n      nodeResolvedImports: {},\n      setResolvedCache: [Function: setResolvedCache],\n      getResolvedCache: [Function: getResolvedCache]\n    },\n    set: [Function (anonymous)]\n  },\n  createResolver: [Function: createResolver],\n  worker: { format: 'iife', plugins: '() => plugins', rollupOptions: {} },\n  appType: 'spa',\n  experimental: { importGlobRestoreExtension: false, hmrPartialAccept: false },\n  webSocketToken: 's0gwEWQd0rfb',\n  additionalAllowedHosts: [],\n  getSortedPlugins: [Function: getSortedPlugins],\n  getSortedPluginHooks: [Function: getSortedPluginHooks]\n}",
  "raw": "2025-06-02T19:09:22.727Z vite:config using resolved config: {\n  plugins: [\n    'vite:optimized-deps',\n    'vite:watch-package-data',\n    'vite:pre-alias',\n    'alias',\n    'vite:react-babel',\n    'vite:react-refresh',\n    'vite:modulepreload-polyfill',\n    'vite:resolve',\n    'vite:html-inline-proxy',\n    'vite:css',\n    'vite:esbuild',\n    'vite:json',\n    'vite:wasm-helper',\n    'vite:worker',\n    'vite:asset',\n    'vite:wasm-fallback',\n    'vite:define',\n    'vite:css-post',\n    'vite:worker-import-meta-url',\n    'vite:asset-import-meta-url',\n    'vite:dynamic-import-vars',\n    'vite:import-glob',\n    'vite:client-inject',\n    'vite:css-analysis',\n    'vite:import-analysis'\n  ],\n  resolve: {\n    mainFields: [ 'browser', 'module', 'jsnext:main', 'jsnext' ],\n    conditions: [],\n    extensions: [\n      '.mjs',  '.js',\n      '.mts',  '.ts',\n      '.jsx',  '.tsx',\n      '.json'\n    ],\n    dedupe: [ 'react', 'react-dom' ],\n    preserveSymlinks: false,\n    alias: [ [Object], [Object], [Object] ]\n  },\n  optimizeDeps: {\n    holdUntilCrawlEnd: true,\n    include: [\n      'react',\n      'react-dom',\n      'react',\n      'react-dom',\n      'react/jsx-dev-runtime',\n      'react/jsx-runtime'\n    ],\n    esbuildOptions: { preserveSymlinks: false, jsx: 'automatic' }\n  },\n  server: {\n    preTransformRequests: true,\n    port: 3000,\n    host: true,\n    proxy: { '/api': [Object] },\n    sourcemapIgnoreList: [Function: isInNodeModules$1],\n    middlewareMode: false,\n    fs: {\n      strict: true,\n      allow: [Array],\n      deny: [Array],\n      cachedChecks: undefined\n    }\n  },\n  build: {\n    target: [ 'es2020', 'edge88', 'firefox78', 'chrome87', 'safari14' ],\n    cssTarget: [ 'es2020', 'edge88', 'firefox78', 'chrome87', 'safari14' ],\n    outDir: 'dist',\n    assetsDir: 'assets',\n    assetsInlineLimit: 4096,\n    cssCodeSplit: true,\n    sourcemap: true,\n    rollupOptions: { output: [Object], onwarn: [Function: onwarn] },\n    minify: 'esbuild',\n    terserOptions: {},\n    write: true,\n    emptyOutDir: null,\n    copyPublicDir: true,\n    manifest: false,\n    lib: false,\n    ssr: false,\n    ssrManifest: false,\n    ssrEmitAssets: false,\n    reportCompressedSize: true,\n    chunkSizeWarningLimit: 500,\n    watch: null,\n    commonjsOptions: { include: [Array], extensions: [Array] },\n    dynamicImportVarsOptions: { warnOnError: true, exclude: [Array] },\n    modulePreload: { polyfill: true },\n    cssMinify: true\n  },\n  define: { __APP_VERSION__: '\"1.0.0\"', __DEV__: 'true' },\n  esbuild: { jsxDev: true, drop: [], jsx: 'automatic' },\n  configFile: 'C:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/vite.config.ts',\n  configFileDependencies: [\n    'C:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/vite.config.ts'\n  ],\n  inlineConfig: {\n    root: undefined,\n    base: undefined,\n    mode: undefined,\n    configFile: undefined,\n    logLevel: undefined,\n    clearScreen: undefined,\n    optimizeDeps: { force: undefined },\n    server: {}\n  },\n  root: 'C:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend',\n  base: '/',\n  decodedBase: '/',\n  rawBase: '/',\n  publicDir: 'C:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/public',\n  cacheDir: 'C:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite',\n  command: 'serve',\n  mode: 'development',\n  ssr: {\n    target: 'node',\n    optimizeDeps: { noDiscovery: true, esbuildOptions: [Object] }\n  },\n  isWorker: false,\n  mainConfig: null,\n  bundleChain: [],\n  isProduction: false,\n  css: { lightningcss: undefined },\n  preview: {\n    port: undefined,\n    strictPort: undefined,\n    host: true,\n    allowedHosts: undefined,\n    https: undefined,\n    open: undefined,\n    proxy: { '/api': [Object] },\n    cors: undefined,\n    headers: undefined\n  },\n  envDir: 'C:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend',\n  env: {\n    VITE_API_BASE_URL: 'http://localhost:3001',\n    VITE_API_TIMEOUT: '10000',\n    VITE_APP_NAME: 'Volcano App Admin',\n    VITE_APP_VERSION: '1.0.0',\n    VITE_APP_DESCRIPTION: 'Panel administrativo para gestión de alertas volcánicas',\n    VITE_DEFAULT_LAT: '-39.420000',\n    VITE_DEFAULT_LNG: '-71.939167',\n    VITE_DEFAULT_ZOOM: '10',\n    VITE_ENABLE_DEVTOOLS: 'true',\n    VITE_LOG_LEVEL: 'debug',\n    VITE_DEBUG: 'true',\n    BASE_URL: '/',\n    MODE: 'development',\n    DEV: true,\n    PROD: false\n  },\n  assetsInclude: [Function: assetsInclude],\n  logger: {\n    hasWarned: false,\n    info: [Function: info],\n    warn: [Function: warn],\n    warnOnce: [Function: warnOnce],\n    error: [Function: error],\n    clearScreen: [Function: clearScreen],\n    hasErrorLogged: [Function: hasErrorLogged]\n  },\n  packageCache: Map(1) {\n    'fnpd_C:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend' => {\n      dir: 'C:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend',\n      data: [Object],\n      hasSideEffects: [Function: hasSideEffects],\n      webResolvedImports: {},\n      nodeResolvedImports: {},\n      setResolvedCache: [Function: setResolvedCache],\n      getResolvedCache: [Function: getResolvedCache]\n    },\n    set: [Function (anonymous)]\n  },\n  createResolver: [Function: createResolver],\n  worker: { format: 'iife', plugins: '() => plugins', rollupOptions: {} },\n  appType: 'spa',\n  experimental: { importGlobRestoreExtension: false, hmrPartialAccept: false },\n  webSocketToken: 's0gwEWQd0rfb',\n  additionalAllowedHosts: [],\n  getSortedPlugins: [Function: getSortedPlugins],\n  getSortedPluginHooks: [Function: getSortedPluginHooks]\n}\n",
  "id": "error_1748891362737_tm6x8ege0"
}
---
{
  "timestamp": "2025-06-02T19:10:58.270Z",
  "type": "javascript_error",
  "severity": "high",
  "message": "\u001b[2m3:10:58 p. m.\u001b[22m \u001b[31m\u001b[1m[vite]\u001b[22m\u001b[39m Pre-transform error: Transform failed with 1 error:\nC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/utils/debug.ts:96:7: ERROR: Expected \";\" but found \")\"",
  "raw": "\u001b[2m3:10:58 p. m.\u001b[22m \u001b[31m\u001b[1m[vite]\u001b[22m\u001b[39m Pre-transform error: Transform failed with 1 error:\nC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/utils/debug.ts:96:7: ERROR: Expected \";\" but found \")\"\n",
  "id": "error_1748891458270_ty92vvxg5"
}
---
{
  "timestamp": "2025-06-02T19:10:58.774Z",
  "type": "javascript_error",
  "severity": "high",
  "message": "\u001b[2m3:10:58 p. m.\u001b[22m \u001b[31m\u001b[1m[vite]\u001b[22m\u001b[39m \u001b[31mInternal server error: Transform failed with 1 error:\nC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/utils/debug.ts:96:7: ERROR: Expected \";\" but found \")\"\u001b[39m\n  Plugin: \u001b[35mvite:esbuild\u001b[39m\n  File: \u001b[36mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/utils/debug.ts\u001b[39m:96:7\n\u001b[33m  \n  \u001b[33mExpected \";\" but found \")\"\u001b[33m\n  94 |            data: args.length === 1 && typeof args[0] === 'object' ? args[0] : args,\n  95 |          });\n  96 |        });\n     |         ^\n  97 |      });\n  98 |    }\n  \u001b[39m\n      at failureErrorWithLog (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\frontend\\node_modules\\esbuild\\lib\\main.js:1472:15)\n      at C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\frontend\\node_modules\\esbuild\\lib\\main.js:755:50\n      at responseCallbacks.<computed> (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\frontend\\node_modules\\esbuild\\lib\\main.js:622:9)\n      at handleIncomingPacket (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\frontend\\node_modules\\esbuild\\lib\\main.js:677:12)\n      at Socket.readFromStdout (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\frontend\\node_modules\\esbuild\\lib\\main.js:600:7)\n      at Socket.emit (node:events:518:28)\n      at addChunk (node:internal/streams/readable:561:12)\n      at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n      at Readable.push (node:internal/streams/readable:392:5)\n      at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)",
  "raw": "\u001b[2m3:10:58 p. m.\u001b[22m \u001b[31m\u001b[1m[vite]\u001b[22m\u001b[39m \u001b[31mInternal server error: Transform failed with 1 error:\nC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/utils/debug.ts:96:7: ERROR: Expected \";\" but found \")\"\u001b[39m\n  Plugin: \u001b[35mvite:esbuild\u001b[39m\n  File: \u001b[36mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/utils/debug.ts\u001b[39m:96:7\n\u001b[33m  \n  \u001b[33mExpected \";\" but found \")\"\u001b[33m\n  94 |            data: args.length === 1 && typeof args[0] === 'object' ? args[0] : args,\n  95 |          });\n  96 |        });\n     |         ^\n  97 |      });\n  98 |    }\n  \u001b[39m\n      at failureErrorWithLog (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\frontend\\node_modules\\esbuild\\lib\\main.js:1472:15)\n      at C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\frontend\\node_modules\\esbuild\\lib\\main.js:755:50\n      at responseCallbacks.<computed> (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\frontend\\node_modules\\esbuild\\lib\\main.js:622:9)\n      at handleIncomingPacket (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\frontend\\node_modules\\esbuild\\lib\\main.js:677:12)\n      at Socket.readFromStdout (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\frontend\\node_modules\\esbuild\\lib\\main.js:600:7)\n      at Socket.emit (node:events:518:28)\n      at addChunk (node:internal/streams/readable:561:12)\n      at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n      at Readable.push (node:internal/streams/readable:392:5)\n      at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)\n",
  "id": "error_1748891458774_3b1bm2wx5"
}
---
{
  "timestamp": "2025-06-02T19:10:58.776Z",
  "type": "javascript_error",
  "severity": "high",
  "message": "2025-06-02T19:10:58.775Z vite:time \u001b[31m428.29ms\u001b[39m \u001b[2m/src/utils/debug.ts\u001b[22m\n\u001b[2m3:10:58 p. m.\u001b[22m \u001b[31m\u001b[1m[vite]\u001b[22m\u001b[39m Pre-transform error: Transform failed with 1 error:\nC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/utils/debug.ts:96:7: ERROR: Expected \";\" but found \")\"",
  "raw": "2025-06-02T19:10:58.775Z vite:time \u001b[31m428.29ms\u001b[39m \u001b[2m/src/utils/debug.ts\u001b[22m\n\u001b[2m3:10:58 p. m.\u001b[22m \u001b[31m\u001b[1m[vite]\u001b[22m\u001b[39m Pre-transform error: Transform failed with 1 error:\nC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/utils/debug.ts:96:7: ERROR: Expected \";\" but found \")\"\n",
  "id": "error_1748891458777_nn020m3bj"
}
---
