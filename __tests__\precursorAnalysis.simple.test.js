/**
 * 🌋 Volcano App - Tests Simples para Análisis de Precursores
 * Tests unitarios básicos sin dependencias de React Native
 */

// Importar funciones directamente sin alias
const {
  validarDatos,
  calcularPrimeraDerivada,
  calcularSegundaDerivada,
  analizarPrecursor,
  determinarNivelAlerta,
  obtenerUltimoValorValido,
  formatearValor,
} = require('../utils/precursorAnalysis');

const { DATOS_PRUEBA } = require('../types/precursor');

describe('Análisis de Precursores - Tests Básicos', () => {
  
  describe('validarDatos', () => {
    test('debe validar datos correctos', () => {
      expect(validarDatos([1, 2, 3, 4])).toBe(true);
      expect(validarDatos([0, -1, 5.5, 10])).toBe(true);
    });

    test('debe rechazar datos inválidos', () => {
      expect(validarDatos([])).toBe(false);
      expect(validarDatos([1])).toBe(false);
      expect(validarDatos([1, NaN, 3])).toBe(false);
      expect(validarDatos([1, Infinity, 3])).toBe(false);
    });
  });

  describe('calcularPrimeraDerivada', () => {
    test('debe calcular primera derivada correctamente', () => {
      const datos = [2, 3, 3, 4, 6];
      const resultado = calcularPrimeraDerivada(datos);
      expect(resultado).toEqual([null, 1, 0, 1, 2]);
    });

    test('debe manejar datos de prueba', () => {
      const resultado = calcularPrimeraDerivada(DATOS_PRUEBA.valores);
      expect(resultado[0]).toBe(null);
      expect(resultado[1]).toBe(1); // 3 - 2 = 1
      expect(resultado[2]).toBe(0); // 3 - 3 = 0
      expect(resultado[3]).toBe(1); // 4 - 3 = 1
    });

    test('debe lanzar error con datos inválidos', () => {
      expect(() => calcularPrimeraDerivada([])).toThrow();
      expect(() => calcularPrimeraDerivada([1])).toThrow();
    });
  });

  describe('calcularSegundaDerivada', () => {
    test('debe calcular segunda derivada correctamente', () => {
      const primeraderivada = [null, 1, 0, 1, 2];
      const resultado = calcularSegundaDerivada(primeraderivada);
      expect(resultado).toEqual([null, null, -1, 1, 1]);
    });

    test('debe manejar valores null en primera derivada', () => {
      const primeraderivada = [null, 1, null, 2, 3];
      const resultado = calcularSegundaDerivada(primeraderivada);
      expect(resultado[0]).toBe(null);
      expect(resultado[1]).toBe(null);
      expect(resultado[2]).toBe(null); // null - 1 = null
      expect(resultado[3]).toBe(null); // 2 - null = null
      expect(resultado[4]).toBe(1); // 3 - 2 = 1
    });
  });

  describe('analizarPrecursor', () => {
    test('debe realizar análisis completo con datos de prueba', () => {
      const resultado = analizarPrecursor(DATOS_PRUEBA.valores);
      
      expect(resultado.datosOriginales).toEqual(DATOS_PRUEBA.valores);
      expect(resultado.primeraderivada).toHaveLength(DATOS_PRUEBA.valores.length);
      expect(resultado.segundaDerivada).toHaveLength(DATOS_PRUEBA.valores.length);
      expect(resultado.primeraderivada[0]).toBe(null);
      expect(resultado.segundaDerivada[0]).toBe(null);
      expect(resultado.segundaDerivada[1]).toBe(null);
    });
  });

  describe('obtenerUltimoValorValido', () => {
    test('debe obtener último valor válido', () => {
      expect(obtenerUltimoValorValido([1, 2, null, 4, null])).toBe(4);
      expect(obtenerUltimoValorValido([null, null, 3])).toBe(3);
      expect(obtenerUltimoValorValido([1, 2, 3])).toBe(3);
    });

    test('debe retornar null si no hay valores válidos', () => {
      expect(obtenerUltimoValorValido([null, null, null])).toBe(null);
      expect(obtenerUltimoValorValido([])).toBe(null);
    });
  });

  describe('determinarNivelAlerta', () => {
    const umbrales = { verde: 1, amarillo: 5, rojo: 5 };

    test('debe determinar nivel Verde', () => {
      const segundaDerivada = [null, null, 0.5, 1, 0.8];
      const resultado = determinarNivelAlerta(segundaDerivada, umbrales);
      
      expect(resultado.nivel).toBe('Verde');
      expect(resultado.valor).toBe(0.8);
      expect(resultado.mensaje).toContain('estable');
    });

    test('debe determinar nivel Amarillo', () => {
      const segundaDerivada = [null, null, 1.5, 3, 4];
      const resultado = determinarNivelAlerta(segundaDerivada, umbrales);
      
      expect(resultado.nivel).toBe('Amarillo');
      expect(resultado.valor).toBe(4);
      expect(resultado.mensaje).toContain('acelerando');
    });

    test('debe determinar nivel Rojo', () => {
      const segundaDerivada = [null, null, 2, 6, 8];
      const resultado = determinarNivelAlerta(segundaDerivada, umbrales);
      
      expect(resultado.nivel).toBe('Rojo');
      expect(resultado.valor).toBe(8);
      expect(resultado.mensaje).toContain('peligrosa');
    });
  });

  describe('formatearValor', () => {
    test('debe formatear valores numéricos', () => {
      expect(formatearValor(3.14159, 2)).toBe('3.14');
      expect(formatearValor(10, 0)).toBe('10');
      expect(formatearValor(1.5, 1)).toBe('1.5');
    });

    test('debe manejar valores null', () => {
      expect(formatearValor(null)).toBe('N/A');
      expect(formatearValor(null, 3)).toBe('N/A');
    });
  });

  describe('Casos Edge', () => {
    test('debe manejar datos con valores negativos', () => {
      const datos = [-5, -3, -1, 2, 5];
      const resultado = analizarPrecursor(datos);
      
      expect(resultado.datosOriginales).toEqual(datos);
      expect(resultado.primeraderivada[1]).toBe(2); // -3 - (-5) = 2
    });

    test('debe detectar escalada en datos de prueba', () => {
      // Los datos de prueba [2, 3, 3, 4, 6, 9, 14, 22, 35, 50, 68] 
      // deberían generar una segunda derivada con valores altos
      const resultado = analizarPrecursor(DATOS_PRUEBA.valores);
      const ultimoValor = obtenerUltimoValorValido(resultado.segundaDerivada);
      
      // Verificar que hay aceleración significativa
      expect(ultimoValor).toBeGreaterThan(5);
    });
  });
});
