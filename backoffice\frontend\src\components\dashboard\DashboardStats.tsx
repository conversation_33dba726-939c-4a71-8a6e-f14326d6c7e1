import {
  Activity,
  AlertTriangle,
  Clock,
  Shield,
  TrendingDown,
  TrendingUp
} from 'lucide-react'
import React from 'react'
import { cn } from '../../lib/utils'
import { Badge } from '../ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Progress } from '../ui/progress'

interface DashboardStatsProps {
  health: any
  alerts: any[]
  zones: any[]
  auditLogs?: any[]
}

interface StatCardProps {
  title: string
  value: string | number
  description?: string
  icon: React.ReactNode
  trend?: {
    value: number
    isPositive: boolean
  }
  variant?: 'default' | 'success' | 'warning' | 'destructive'
  progress?: number
  isLoading?: boolean
}

function StatCard({
  title,
  value,
  description,
  icon,
  trend,
  variant = 'default',
  progress,
  isLoading = false
}: StatCardProps) {
  const variantStyles = {
    default: 'border-border bg-card hover:bg-accent/5',
    success: 'border-green-200 bg-gradient-to-br from-green-50 to-green-100/50 hover:from-green-100 hover:to-green-200/50',
    warning: 'border-yellow-200 bg-gradient-to-br from-yellow-50 to-yellow-100/50 hover:from-yellow-100 hover:to-yellow-200/50',
    destructive: 'border-red-200 bg-gradient-to-br from-red-50 to-red-100/50 hover:from-red-100 hover:to-red-200/50'
  }

  const iconColors = {
    default: 'text-muted-foreground',
    success: 'text-green-600',
    warning: 'text-yellow-600',
    destructive: 'text-red-600'
  }

  return (
    <Card className={cn(
      'transition-all duration-300 hover:shadow-lg hover:scale-[1.02] cursor-pointer group',
      variantStyles[variant]
    )}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
        <CardTitle className="text-sm font-medium text-muted-foreground group-hover:text-foreground transition-colors">
          {title}
        </CardTitle>
        <div className={cn(
          "h-5 w-5 transition-all duration-300 group-hover:scale-110",
          iconColors[variant]
        )}>
          {icon}
        </div>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="flex items-baseline space-x-2">
          <div className={cn(
            "text-3xl font-bold transition-colors",
            isLoading && "animate-pulse"
          )}>
            {isLoading ? "..." : value}
          </div>
        </div>

        {progress !== undefined && (
          <div className="space-y-2">
            <div className="relative">
              <Progress
                value={progress}
                className={cn(
                  "h-2",
                  variant === 'success' && "[&>div]:bg-green-500",
                  variant === 'warning' && "[&>div]:bg-yellow-500",
                  variant === 'destructive' && "[&>div]:bg-red-500"
                )}
              />
            </div>
            <p className="text-xs text-muted-foreground">
              {progress}% del objetivo
            </p>
          </div>
        )}

        {description && (
          <p className="text-xs text-muted-foreground leading-relaxed">
            {description}
          </p>
        )}

        {trend && (
          <div className="flex items-center space-x-1 pt-1">
            {trend.isPositive ? (
              <TrendingUp className="h-3 w-3 text-green-500" />
            ) : (
              <TrendingDown className="h-3 w-3 text-red-500" />
            )}
            <span className={cn(
              "text-xs font-semibold",
              trend.isPositive ? "text-green-600" : "text-red-600"
            )}>
              {trend.isPositive ? '+' : ''}{trend.value}%
            </span>
            <span className="text-xs text-muted-foreground">vs mes anterior</span>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export function DashboardStats({ health, alerts, zones, auditLogs = [] }: DashboardStatsProps) {
  const activeAlerts = alerts.filter(alert => alert.is_active)
  const activeZones = zones.filter(zone => zone.is_active)
  const emergencyAlerts = activeAlerts.filter(alert =>
    alert.alert_level === 'WARNING' || alert.alert_level === 'EMERGENCY'
  )

  // Calcular estadísticas avanzadas
  const recentActivity = auditLogs.filter(log => {
    const logDate = new Date(log.created_at)
    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000)
    return logDate > oneDayAgo
  }).length

  const systemStatus = health?.status === 'healthy' ? 'Operativo' : 'Con problemas'
  const systemVariant = health?.status === 'healthy' ? 'success' : 'destructive'

  // Calcular métricas de rendimiento
  const systemUptime = health?.uptime || 99.5
  const alertResolutionRate = activeAlerts.length > 0 ?
    Math.round((alerts.length - activeAlerts.length) / alerts.length * 100) : 100
  const zonesCoverage = zones.length > 0 ?
    Math.round(activeZones.length / zones.length * 100) : 0

  return (
    <div className="space-y-8">
      {/* Header con título mejorado */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Panel de Control</h2>
          <p className="text-muted-foreground">
            Monitoreo en tiempo real del sistema volcánico
          </p>
        </div>
        <Badge variant="outline" className="text-xs">
          Actualizado: {new Date().toLocaleTimeString()}
        </Badge>
      </div>

      {/* Estadísticas principales mejoradas */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <StatCard
          title="Estado del Sistema"
          value={systemStatus}
          description={health?.timestamp ?
            `Última verificación: ${new Date(health.timestamp).toLocaleTimeString()}` :
            'Verificando estado del sistema...'
          }
          icon={<Activity />}
          variant={systemVariant}
          progress={systemUptime}
          trend={{
            value: 2.1,
            isPositive: true
          }}
        />

        <StatCard
          title="Alertas Activas"
          value={activeAlerts.length}
          description={`${emergencyAlerts.length} críticas • ${alerts.length} total`}
          icon={<AlertTriangle />}
          variant={emergencyAlerts.length > 0 ? 'destructive' : activeAlerts.length > 0 ? 'warning' : 'success'}
          progress={alertResolutionRate}
          trend={{
            value: 15,
            isPositive: false
          }}
        />

        <StatCard
          title="Zonas Monitoreadas"
          value={activeZones.length}
          description={`${zones.length} zonas configuradas`}
          icon={<Shield />}
          variant={zonesCoverage > 80 ? 'success' : zonesCoverage > 50 ? 'warning' : 'destructive'}
          progress={zonesCoverage}
          trend={{
            value: 8,
            isPositive: true
          }}
        />

        <StatCard
          title="Actividad Reciente"
          value={recentActivity}
          description="Eventos en las últimas 24h"
          icon={<Clock />}
          variant="default"
          progress={Math.min(recentActivity * 10, 100)}
          trend={{
            value: 23,
            isPositive: true
          }}
        />
      </div>

      {/* Estado detallado del sistema mejorado */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        <Card className="col-span-1">
          <CardHeader className="pb-3">
            <div className="flex items-center space-x-2">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Activity className="h-4 w-4 text-blue-600" />
              </div>
              <div>
                <CardTitle className="text-lg">Estado de Servicios</CardTitle>
                <CardDescription>
                  Componentes del sistema en tiempo real
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            {health?.services ? (
              <>
                <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className={cn(
                      "h-2 w-2 rounded-full",
                      health.services.database ? "bg-green-500 animate-pulse" : "bg-red-500"
                    )} />
                    <span className="text-sm font-medium">Base de Datos</span>
                  </div>
                  <Badge variant={health.services.database ? "success" : "destructive"}>
                    {health.services.database ? "Conectada" : "Desconectada"}
                  </Badge>
                </div>

                <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className={cn(
                      "h-2 w-2 rounded-full",
                      health.services.server ? "bg-green-500 animate-pulse" : "bg-red-500"
                    )} />
                    <span className="text-sm font-medium">Servidor API</span>
                  </div>
                  <Badge variant={health.services.server ? "success" : "destructive"}>
                    {health.services.server ? "Activo" : "Inactivo"}
                  </Badge>
                </div>

                <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="h-2 w-2 rounded-full bg-green-500 animate-pulse" />
                    <span className="text-sm font-medium">WebSocket</span>
                  </div>
                  <Badge variant="success">Conectado</Badge>
                </div>
              </>
            ) : (
              <div className="text-center py-4">
                <div className="animate-spin h-6 w-6 border-2 border-primary border-t-transparent rounded-full mx-auto mb-2" />
                <p className="text-sm text-muted-foreground">Verificando servicios...</p>
              </div>
            )}
          </CardContent>
        </Card>

        <Card className="col-span-1">
          <CardHeader className="pb-3">
            <div className="flex items-center space-x-2">
              <div className="p-2 bg-orange-100 rounded-lg">
                <AlertTriangle className="h-4 w-4 text-orange-600" />
              </div>
              <div>
                <CardTitle className="text-lg">Distribución de Alertas</CardTitle>
                <CardDescription>
                  Niveles de alerta por categoría
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-3">
            {['NORMAL', 'ADVISORY', 'WATCH', 'WARNING', 'EMERGENCY'].map(level => {
              const count = alerts.filter(alert => alert.alert_level === level && alert.is_active).length
              const total = alerts.filter(alert => alert.alert_level === level).length
              const percentage = total > 0 ? Math.round((count / total) * 100) : 0

              const levelConfig = {
                NORMAL: { color: 'bg-green-500', label: 'Normal', variant: 'success' as const },
                ADVISORY: { color: 'bg-yellow-500', label: 'Aviso', variant: 'warning' as const },
                WATCH: { color: 'bg-orange-500', label: 'Vigilancia', variant: 'warning' as const },
                WARNING: { color: 'bg-red-500', label: 'Alerta', variant: 'destructive' as const },
                EMERGENCY: { color: 'bg-red-700', label: 'Emergencia', variant: 'destructive' as const }
              }

              const config = levelConfig[level as keyof typeof levelConfig]

              return (
                <div key={level} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className={cn("h-3 w-3 rounded-full", config.color)} />
                      <span className="text-sm font-medium">{config.label}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant={config.variant} className="text-xs">
                        {count}
                      </Badge>
                      <span className="text-xs text-muted-foreground">
                        {percentage}%
                      </span>
                    </div>
                  </div>
                  <Progress value={percentage} className="h-1" />
                </div>
              )
            })}
          </CardContent>
        </Card>

        <Card className="col-span-1 md:col-span-2 lg:col-span-1">
          <CardHeader className="pb-3">
            <div className="flex items-center space-x-2">
              <div className="p-2 bg-green-100 rounded-lg">
                <Shield className="h-4 w-4 text-green-600" />
              </div>
              <div>
                <CardTitle className="text-lg">Zonas de Seguridad</CardTitle>
                <CardDescription>
                  Estado de las zonas monitoreadas
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-3 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">{activeZones.length}</div>
                <div className="text-xs text-green-600 font-medium">Zonas Activas</div>
              </div>
              <div className="text-center p-3 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">{zones.length}</div>
                <div className="text-xs text-blue-600 font-medium">Total Zonas</div>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Cobertura del Sistema</span>
                <span className="font-medium">{zonesCoverage}%</span>
              </div>
              <Progress value={zonesCoverage} className="h-2" />
            </div>

            <div className="pt-2 border-t">
              <div className="text-xs text-muted-foreground">
                Última actualización: {new Date().toLocaleTimeString()}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Actividad reciente mejorada */}
      <Card className="col-span-full">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Clock className="h-4 w-4 text-purple-600" />
              </div>
              <div>
                <CardTitle className="text-lg">Actividad del Sistema</CardTitle>
                <CardDescription>
                  Registro de eventos y acciones recientes
                </CardDescription>
              </div>
            </div>
            <Badge variant="secondary" className="text-xs">
              {auditLogs.length} eventos registrados
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          {auditLogs.length > 0 ? (
            <div className="space-y-4">
              {auditLogs.slice(0, 6).map((log, index) => {
                const actionIcons = {
                  'CREATE': <div className="h-2 w-2 rounded-full bg-green-500" />,
                  'UPDATE': <div className="h-2 w-2 rounded-full bg-blue-500" />,
                  'DELETE': <div className="h-2 w-2 rounded-full bg-red-500" />,
                  'LOGIN': <div className="h-2 w-2 rounded-full bg-purple-500" />,
                  'LOGOUT': <div className="h-2 w-2 rounded-full bg-gray-500" />
                }

                const actionColors = {
                  'CREATE': 'text-green-600 bg-green-50',
                  'UPDATE': 'text-blue-600 bg-blue-50',
                  'DELETE': 'text-red-600 bg-red-50',
                  'LOGIN': 'text-purple-600 bg-purple-50',
                  'LOGOUT': 'text-gray-600 bg-gray-50'
                }

                const actionType = log.action?.toUpperCase() || 'UPDATE'
                const icon = actionIcons[actionType as keyof typeof actionIcons] || actionIcons.UPDATE
                const colorClass = actionColors[actionType as keyof typeof actionColors] || actionColors.UPDATE

                return (
                  <div
                    key={log.id}
                    className={cn(
                      "flex items-center gap-4 p-3 rounded-lg border transition-all hover:shadow-sm",
                      index === 0 && "ring-2 ring-primary/20 bg-primary/5"
                    )}
                  >
                    <div className="flex items-center gap-3 flex-1">
                      {icon}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2">
                          <span className={cn(
                            "text-xs font-medium px-2 py-1 rounded-full",
                            colorClass
                          )}>
                            {log.action || 'Actualización'}
                          </span>
                          <span className="text-sm font-medium truncate">
                            {log.table_name || 'Sistema'}
                          </span>
                        </div>
                        <p className="text-xs text-muted-foreground mt-1">
                          {log.details || `Acción realizada en ${log.table_name || 'el sistema'}`}
                        </p>
                      </div>
                    </div>

                    <div className="text-right">
                      <div className="text-xs font-medium">
                        {new Date(log.created_at).toLocaleTimeString()}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {new Date(log.created_at).toLocaleDateString()}
                      </div>
                    </div>
                  </div>
                )
              })}

              {auditLogs.length > 6 && (
                <div className="text-center pt-4 border-t">
                  <button className="text-sm text-primary hover:text-primary/80 font-medium">
                    Ver todos los eventos ({auditLogs.length})
                  </button>
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-8">
              <div className="mx-auto w-12 h-12 bg-muted rounded-full flex items-center justify-center mb-4">
                <Clock className="h-6 w-6 text-muted-foreground" />
              </div>
              <h3 className="text-sm font-medium mb-1">No hay actividad reciente</h3>
              <p className="text-xs text-muted-foreground">
                Los eventos del sistema aparecerán aquí cuando ocurran
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
