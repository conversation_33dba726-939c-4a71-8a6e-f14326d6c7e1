#!/usr/bin/env node

/**
 * 🌋 Volcano App - Script para ejecutar todos los tests
 * Script de utilidad para ejecutar tests de manera eficiente en todo el proyecto
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Colores para la consola
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// Configuración de componentes
const components = {
  mobile: {
    name: '📱 Mobile App',
    path: '.',
    testCommand: 'npm run test:ci',
    color: colors.blue
  },
  frontend: {
    name: '🖥️ Frontend',
    path: 'backoffice/frontend',
    testCommand: 'npm run test:ci',
    color: colors.green
  },
  backend: {
    name: '⚙️ Backend',
    path: 'backoffice/backend',
    testCommand: 'npm run test:ci',
    color: colors.magenta
  }
};

// Función para imprimir con color
function colorLog(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

// Función para ejecutar comando
function runCommand(command, cwd) {
  return new Promise((resolve, reject) => {
    const [cmd, ...args] = command.split(' ');
    const child = spawn(cmd, args, {
      cwd,
      stdio: 'pipe',
      shell: true
    });

    let stdout = '';
    let stderr = '';

    child.stdout.on('data', (data) => {
      stdout += data.toString();
    });

    child.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    child.on('close', (code) => {
      if (code === 0) {
        resolve({ stdout, stderr, code });
      } else {
        reject({ stdout, stderr, code });
      }
    });

    child.on('error', (error) => {
      reject({ error, stdout, stderr, code: -1 });
    });
  });
}

// Función para verificar si existe package.json
function hasPackageJson(componentPath) {
  const packageJsonPath = path.join(componentPath, 'package.json');
  return fs.existsSync(packageJsonPath);
}

// Función para ejecutar tests de un componente
async function runComponentTests(componentKey, component) {
  const startTime = Date.now();
  
  colorLog(`\n${component.color}${colors.bright}=== ${component.name} Tests ===${colors.reset}`);
  
  if (!hasPackageJson(component.path)) {
    colorLog(`❌ No se encontró package.json en ${component.path}`, colors.red);
    return { success: false, duration: 0, error: 'Missing package.json' };
  }

  try {
    colorLog(`📂 Directorio: ${component.path}`);
    colorLog(`🔧 Comando: ${component.testCommand}`);
    colorLog('⏳ Ejecutando tests...\n');

    const result = await runCommand(component.testCommand, component.path);
    const duration = Date.now() - startTime;

    // Extraer información de cobertura si está disponible
    const coverageMatch = result.stdout.match(/All files\s+\|\s+([\d.]+)\s+\|\s+([\d.]+)\s+\|\s+([\d.]+)\s+\|\s+([\d.]+)/);
    const coverage = coverageMatch ? {
      statements: coverageMatch[1],
      branches: coverageMatch[2],
      functions: coverageMatch[3],
      lines: coverageMatch[4]
    } : null;

    colorLog(`✅ ${component.name} tests completados`, colors.green);
    if (coverage) {
      colorLog(`📊 Cobertura: ${coverage.lines}% líneas, ${coverage.functions}% funciones`, colors.cyan);
    }
    colorLog(`⏱️ Duración: ${(duration / 1000).toFixed(2)}s\n`);

    return { success: true, duration, coverage, output: result.stdout };

  } catch (error) {
    const duration = Date.now() - startTime;
    
    colorLog(`❌ ${component.name} tests fallaron`, colors.red);
    colorLog(`⏱️ Duración: ${(duration / 1000).toFixed(2)}s`, colors.yellow);
    
    if (error.stdout) {
      colorLog('\n📄 Salida:', colors.yellow);
      console.log(error.stdout);
    }
    
    if (error.stderr) {
      colorLog('\n🚨 Errores:', colors.red);
      console.log(error.stderr);
    }
    
    return { success: false, duration, error: error.stderr || error.stdout || 'Unknown error' };
  }
}

// Función principal
async function runAllTests() {
  const startTime = Date.now();
  const results = {};
  
  colorLog(`${colors.bright}${colors.cyan}🌋 Volcano App - Ejecutando todos los tests${colors.reset}`);
  colorLog(`📅 Fecha: ${new Date().toLocaleString()}`);
  colorLog(`📁 Directorio: ${process.cwd()}\n`);

  // Verificar argumentos de línea de comandos
  const args = process.argv.slice(2);
  const componentsToTest = args.length > 0 ? args.filter(arg => components[arg]) : Object.keys(components);
  
  if (componentsToTest.length === 0) {
    colorLog('❌ No se especificaron componentes válidos', colors.red);
    colorLog('💡 Componentes disponibles: mobile, frontend, backend', colors.yellow);
    process.exit(1);
  }

  colorLog(`🎯 Componentes a testear: ${componentsToTest.join(', ')}\n`);

  // Ejecutar tests para cada componente
  for (const componentKey of componentsToTest) {
    const component = components[componentKey];
    results[componentKey] = await runComponentTests(componentKey, component);
  }

  // Generar reporte final
  const totalDuration = Date.now() - startTime;
  const successfulTests = Object.values(results).filter(r => r.success).length;
  const totalTests = Object.keys(results).length;

  colorLog(`${colors.bright}${colors.cyan}=== 📊 Reporte Final ===${colors.reset}`);
  colorLog(`⏱️ Tiempo total: ${(totalDuration / 1000).toFixed(2)}s`);
  colorLog(`✅ Tests exitosos: ${successfulTests}/${totalTests}`);
  colorLog(`❌ Tests fallidos: ${totalTests - successfulTests}/${totalTests}\n`);

  // Detalles por componente
  for (const [componentKey, result] of Object.entries(results)) {
    const component = components[componentKey];
    const status = result.success ? '✅' : '❌';
    const duration = `${(result.duration / 1000).toFixed(2)}s`;
    
    colorLog(`${status} ${component.name}: ${duration}`, result.success ? colors.green : colors.red);
    
    if (result.coverage) {
      colorLog(`   📊 Cobertura: ${result.coverage.lines}% líneas`, colors.cyan);
    }
    
    if (!result.success && result.error) {
      colorLog(`   🚨 Error: ${result.error.split('\n')[0]}`, colors.red);
    }
  }

  // Generar archivo de reporte JSON
  const reportPath = path.join(process.cwd(), 'test-report.json');
  const report = {
    timestamp: new Date().toISOString(),
    totalDuration: totalDuration,
    components: results,
    summary: {
      total: totalTests,
      successful: successfulTests,
      failed: totalTests - successfulTests,
      successRate: ((successfulTests / totalTests) * 100).toFixed(2)
    }
  };

  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  colorLog(`\n📄 Reporte guardado en: ${reportPath}`, colors.cyan);

  // Salir con código de error si algún test falló
  if (successfulTests < totalTests) {
    colorLog(`\n🚨 Algunos tests fallaron. Revisa los errores arriba.`, colors.red);
    process.exit(1);
  } else {
    colorLog(`\n🎉 ¡Todos los tests pasaron exitosamente!`, colors.green);
    process.exit(0);
  }
}

// Manejo de errores no capturados
process.on('uncaughtException', (error) => {
  colorLog(`\n🚨 Error no capturado: ${error.message}`, colors.red);
  console.error(error.stack);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  colorLog(`\n🚨 Promesa rechazada no manejada: ${reason}`, colors.red);
  console.error('Promise:', promise);
  process.exit(1);
});

// Ejecutar si es llamado directamente
if (require.main === module) {
  runAllTests().catch((error) => {
    colorLog(`\n🚨 Error ejecutando tests: ${error.message}`, colors.red);
    console.error(error.stack);
    process.exit(1);
  });
}

module.exports = { runAllTests, runComponentTests, components };
