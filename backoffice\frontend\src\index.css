/* Tailwind CSS imports - Updated */
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 5.9% 10%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    margin: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
      'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
      sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Animaciones personalizadas */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Estilos para componentes específicos */
.volcano-card {
  @apply bg-white shadow-sm border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200;
}

.volcano-button {
  @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200;
}

.volcano-button-secondary {
  @apply bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded-md transition-colors duration-200;
}

/* Estilos para estados de alerta */
.alert-normal {
  @apply bg-green-50 border-green-200 text-green-800;
}

.alert-advisory {
  @apply bg-yellow-50 border-yellow-200 text-yellow-800;
}

.alert-warning {
  @apply bg-orange-50 border-orange-200 text-orange-800;
}

.alert-critical {
  @apply bg-red-50 border-red-200 text-red-800;
}

/* Estilos para tipos de zona */
.zone-safe {
  @apply bg-green-50 border-green-200;
}

.zone-emergency {
  @apply bg-blue-50 border-blue-200;
}

.zone-evacuation {
  @apply bg-yellow-50 border-yellow-200;
}

.zone-danger {
  @apply bg-red-50 border-red-200;
}

/* Estilos para Leaflet Map */
.leaflet-container {
  font-family: inherit;
}

.leaflet-popup-content-wrapper {
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.leaflet-popup-content {
  margin: 12px 16px;
  line-height: 1.4;
}

.leaflet-popup-tip {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Custom marker styles */
.custom-alert-marker {
  background: transparent;
  border: none;
}

/* Leaflet control styles */
.leaflet-control-zoom {
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.leaflet-control-zoom a {
  border-radius: 6px;
  font-size: 16px;
  line-height: 26px;
}

/* Drawing controls */
.leaflet-draw-toolbar {
  background: white;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Responsive map */
@media (max-width: 768px) {
  .leaflet-control-zoom {
    margin-right: 10px;
    margin-top: 10px;
  }

  .leaflet-popup-content-wrapper {
    max-width: 250px;
  }
}
