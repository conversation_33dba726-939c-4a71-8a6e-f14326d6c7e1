# 🌋 Volcano App Backend

Backend API para el sistema de administración de alertas volcánicas y gestión de zonas de seguridad.

## 🚀 Inicio Rápido

### Prerrequisitos
- Node.js 18+ 
- npm o yarn
- Cuenta de Supabase configurada

### Instalación

```bash
# Clonar el repositorio
git clone <repository-url>
cd volcanoApp/backoffice/backend

# Instalar dependencias
npm install

# Configurar variables de entorno
cp .env.example .env
# Editar .env con tus configuraciones

# Compilar TypeScript
npm run build

# Iniciar servidor
npm start
```

### Desarrollo

```bash
# Modo desarrollo con hot reload
npm run dev

# Linting
npm run lint
npm run lint:fix

# Testing
npm test
npm run test:watch
```

## 🏗️ Arquitectura

### Estructura del Proyecto

```
src/
├── config/          # Configuración de variables de entorno
├── controllers/     # Lógica de negocio
├── middleware/      # Middleware personalizado
├── routes/          # Definición de rutas
├── services/        # Servicios externos (Supabase)
├── types/           # Tipos TypeScript
├── utils/           # Utilidades y helpers
└── index.ts         # Punto de entrada
```

### Stack Tecnológico

- **Runtime**: Node.js
- **Framework**: Express.js
- **Lenguaje**: TypeScript
- **Base de Datos**: Supabase (PostgreSQL)
- **Autenticación**: JWT
- **Validación**: express-validator
- **Logging**: Winston
- **Documentación**: JSDoc

## 📡 API Endpoints

### Autenticación
```
POST   /api/auth/login              # Iniciar sesión
POST   /api/auth/logout             # Cerrar sesión
POST   /api/auth/refresh            # Renovar token
GET    /api/auth/me                 # Obtener perfil
PUT    /api/auth/change-password    # Cambiar contraseña
```

### Alertas Volcánicas
```
GET    /api/alerts                  # Listar alertas
POST   /api/alerts                 # Crear alerta
GET    /api/alerts/:id              # Obtener alerta
PUT    /api/alerts/:id              # Actualizar alerta
DELETE /api/alerts/:id              # Eliminar alerta
```

### Zonas de Seguridad
```
GET    /api/zones                   # Listar zonas
POST   /api/zones                  # Crear zona
GET    /api/zones/:id               # Obtener zona
PUT    /api/zones/:id               # Actualizar zona
DELETE /api/zones/:id               # Eliminar zona
```

### App Móvil
```
GET    /api/mobile/alerts/current   # Alerta actual
GET    /api/mobile/zones/all        # Todas las zonas
POST   /api/mobile/location/report  # Reportar ubicación
POST   /api/mobile/location/check   # Verificar ubicación
```

### Sistema
```
GET    /health                      # Health check
GET    /api/test                    # Info de la API
```

## 🔒 Seguridad

### Autenticación
- JWT con tokens de acceso (24h) y refresh (7d)
- Bcrypt para hash de contraseñas
- Verificación de usuarios activos

### Autorización
```typescript
enum UserRole {
  VIEWER = 'VIEWER',     # Solo lectura
  OPERATOR = 'OPERATOR', # Operaciones básicas
  ADMIN = 'ADMIN'        # Acceso completo
}
```

### Protecciones
- Rate limiting (100 req/15min)
- CORS configurado
- Validación de entrada
- Sanitización de datos

## ⚙️ Configuración

### Variables de Entorno

```bash
# Servidor
NODE_ENV=development
PORT=3001
HOST=localhost

# Supabase
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# JWT
JWT_SECRET=your-jwt-secret
JWT_REFRESH_SECRET=your-refresh-secret

# Seguridad
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS
CORS_ORIGIN=http://localhost:3000,http://localhost:5173
CORS_CREDENTIALS=true

# Logging
LOG_LEVEL=info
LOG_FILE_PATH=./logs/app.log
```

### Base de Datos

El proyecto utiliza Supabase con las siguientes tablas principales:

- `admin_users` - Usuarios del sistema
- `volcano_alerts` - Alertas volcánicas
- `safety_zones` - Zonas de seguridad
- `audit_logs` - Logs de auditoría
- `system_config` - Configuración del sistema
- `app_user_locations` - Ubicaciones de usuarios móviles

## 📊 Logging y Monitoreo

### Logs
- **Archivo**: `logs/app.log` (rotación automática)
- **Niveles**: error, warn, info, http, debug
- **Formato**: JSON estructurado

### Health Check
```bash
curl http://localhost:3001/health
```

### Métricas
- Requests HTTP con tiempo de respuesta
- Errores de autenticación
- Actividad de base de datos
- Logs de auditoría completos

## 🧪 Testing

### Configuración
```bash
# Ejecutar tests
npm test

# Tests en modo watch
npm run test:watch

# Coverage
npm run test:coverage
```

### Estructura de Tests
```
tests/
├── unit/           # Tests unitarios
├── integration/    # Tests de integración
├── fixtures/       # Datos de prueba
└── helpers/        # Utilidades de testing
```

## 🚀 Deployment

### Producción

```bash
# Build
npm run build

# Iniciar
npm start
```

### Docker (Recomendado)

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY dist ./dist
EXPOSE 3001
CMD ["npm", "start"]
```

### Variables de Entorno de Producción

```bash
NODE_ENV=production
PORT=3001
# Configurar todas las variables requeridas
```

## 📚 Documentación

### API Documentation
- **Swagger**: `/api-docs` (en desarrollo)
- **Postman**: Colección disponible en `/docs`

### Código
- JSDoc en todas las funciones públicas
- Tipos TypeScript completos
- Comentarios explicativos

## 🔄 Versionado

### Semantic Versioning
- **Major**: Cambios incompatibles
- **Minor**: Nuevas funcionalidades
- **Patch**: Bug fixes

### Changelog
Ver `CHANGELOG.md` para historial de cambios.

## 🤝 Contribución

### Workflow
1. Fork del repositorio
2. Crear branch feature (`git checkout -b feature/nueva-funcionalidad`)
3. Commit cambios (`git commit -am 'Agregar nueva funcionalidad'`)
4. Push al branch (`git push origin feature/nueva-funcionalidad`)
5. Crear Pull Request

### Estándares
- ESLint para linting
- Prettier para formateo
- Conventional Commits
- Tests requeridos para nuevas funcionalidades

## 📞 Soporte

### Issues
- Reportar bugs en GitHub Issues
- Incluir logs relevantes
- Especificar versión y entorno

### Contacto
- **Email**: <EMAIL>
- **Slack**: #volcano-app-backend

## 📄 Licencia

Este proyecto está bajo la Licencia MIT. Ver `LICENSE` para más detalles.

---

## 🔗 Enlaces Útiles

- [Documentación de Supabase](https://supabase.com/docs)
- [Express.js Guide](https://expressjs.com/)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [Winston Logging](https://github.com/winstonjs/winston)

---

**Versión**: 1.0.0  
**Última actualización**: Junio 2025  
**Estado**: ✅ Producción Ready
