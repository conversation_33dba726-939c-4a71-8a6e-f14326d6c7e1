/**
 * 🌋 Volcano App Backend - Servidor Principal
 * Configuración y arranque del servidor Express
 */

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import { createServer } from 'http';
import swaggerUi from 'swagger-ui-express';

import { logger, morganStream, requestLogger, errorLogger } from '@/utils/logger';
import { initializeSupabase, healthCheck } from '@/services/supabase';
import { initializeRedis, closeRedis, isRedisAvailable } from '@/services/cache';
import { initializeWebSocket } from '@/services/websocket';
import { swaggerSpec } from '@/config/swagger';
import { CONFIG, validateRequiredConfig } from '@/config/env';

// Validar configuración requerida
validateRequiredConfig();

// =====================================================
// CONFIGURACIÓN DEL SERVIDOR
// =====================================================

const app = express();
const { PORT, HOST, NODE_ENV } = CONFIG.SERVER;

// =====================================================
// MIDDLEWARE DE SEGURIDAD
// =====================================================

// Helmet para headers de seguridad
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
  crossOriginEmbedderPolicy: false
}));

// CORS
const corsOptions = {
  origin: NODE_ENV === 'development'
    ? true // Permitir todos los orígenes en desarrollo
    : CONFIG.CORS.CORS_ORIGIN,
  credentials: CONFIG.CORS.CORS_CREDENTIALS,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  allowedHeaders: [
    'Content-Type',
    'Authorization',
    'X-Requested-With',
    'Accept',
    'Origin',
    'Cache-Control',
    'X-File-Name'
  ],
  exposedHeaders: ['X-Total-Count'],
  preflightContinue: false,
  optionsSuccessStatus: 204
};

app.use(cors(corsOptions));

// Rate limiting
const limiter = rateLimit({
  windowMs: CONFIG.SECURITY.RATE_LIMIT_WINDOW_MS,
  max: CONFIG.SECURITY.RATE_LIMIT_MAX_REQUESTS,
  message: {
    success: false,
    error: 'Too many requests, please try again later',
    timestamp: new Date()
  },
  standardHeaders: true,
  legacyHeaders: false,
});

app.use('/api/', limiter);

// =====================================================
// MIDDLEWARE GENERAL
// =====================================================

// Compresión
app.use(compression());

// Parsing de JSON y URL
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Logging de requests
app.use(morgan('combined', { stream: morganStream }));
app.use(requestLogger());

// =====================================================
// RUTAS DE SALUD Y ESTADO
// =====================================================

// Health check básico
app.get('/health', async (req, res) => {
  try {
    const health = await healthCheck();
    const status = Object.values(health).every(Boolean) ? 'healthy' : 'unhealthy';
    
    res.status(status === 'healthy' ? 200 : 503).json({
      status,
      timestamp: new Date(),
      services: health,
      version: process.env.npm_package_version || '1.0.0',
      environment: NODE_ENV
    });
  } catch (error) {
    logger.error('Health check error:', error);
    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date(),
      error: 'Health check failed'
    });
  }
});

// Status endpoint más detallado
app.get('/status', async (req, res) => {
  try {
    const health = await healthCheck();
    
    res.json({
      success: true,
      data: {
        server: {
          status: 'running',
          uptime: process.uptime(),
          memory: process.memoryUsage(),
          version: process.env.npm_package_version || '1.0.0',
          environment: NODE_ENV,
          node_version: process.version
        },
        services: health,
        timestamp: new Date()
      }
    });
  } catch (error) {
    logger.error('Status check error:', error);
    res.status(500).json({
      success: false,
      error: 'Status check failed',
      timestamp: new Date()
    });
  }
});

// =====================================================
// RUTAS DE API
// =====================================================

// Importar rutas
import apiRoutes from '@/routes/index';

// Configurar rutas principales
app.use('/api', apiRoutes);
app.use('/', apiRoutes); // Para rutas como /health y /status

// =====================================================
// DOCUMENTACIÓN API (SWAGGER)
// =====================================================

// Swagger UI setup
app.use('/api-docs', swaggerUi.serve);
app.get('/api-docs', swaggerUi.setup(swaggerSpec, {
  customCss: '.swagger-ui .topbar { display: none }',
  customSiteTitle: 'Volcano App API Documentation',
  customfavIcon: '/favicon.ico',
  swaggerOptions: {
    persistAuthorization: true,
    displayRequestDuration: true,
    filter: true,
    showExtensions: true,
    showCommonExtensions: true
  }
}));

// API spec endpoint
app.get('/api-docs/swagger.json', (req, res) => {
  res.setHeader('Content-Type', 'application/json');
  res.send(swaggerSpec);
});

// =====================================================
// MANEJO DE ERRORES
// =====================================================

// Importar middleware de manejo de errores
import { errorHandler } from '@/middleware/errorHandler';

// Middleware de manejo de errores
app.use(errorLogger());
app.use(errorHandler());

// =====================================================
// INICIALIZACIÓN DEL SERVIDOR
// =====================================================

async function startServer() {
  try {
    logger.info('Starting Volcano App Backend...');

    // Inicializar Redis (opcional)
    try {
      await initializeRedis();
      if (isRedisAvailable()) {
        logger.info('✅ Redis cache connection established');
      } else {
        logger.warn('⚠️  Redis cache not available - running without cache');
      }
    } catch (error) {
      logger.warn('⚠️  Redis initialization failed - running without cache:', error);
    }

    // Inicializar Supabase
    await initializeSupabase();
    logger.info('✅ Supabase connection established');

    // Crear servidor HTTP
    const httpServer = createServer(app);

    // Inicializar WebSocket
    const io = initializeWebSocket(httpServer);
    logger.info('✅ WebSocket server initialized');

    // Iniciar servidor - Forzar IPv4 en Windows
    const server = httpServer.listen(PORT, '0.0.0.0', () => {
      logger.info(`🌋 Volcano App Backend running on http://0.0.0.0:${PORT}`);
      logger.info(`📊 Environment: ${NODE_ENV}`);
      logger.info(`🔍 Health check: http://localhost:${PORT}/health`);
      logger.info(`🔍 Network access: http://************:${PORT}/health`);
      logger.info(`📚 API Docs: http://localhost:${PORT}/api-docs`);
      logger.info(`🔌 WebSocket: ws://localhost:${PORT}`);
      logger.info(`🔌 WebSocket (network): ws://************:${PORT}`);

      if (isRedisAvailable()) {
        logger.info(`🚀 Redis cache: enabled`);
      }
    });

    // Manejo de cierre graceful
    const gracefulShutdown = async (signal: string) => {
      logger.info(`Received ${signal}, shutting down gracefully...`);

      // Cerrar WebSocket connections
      if (io) {
        io.close();
        logger.info('WebSocket server closed');
      }

      // Cerrar Redis connection
      try {
        await closeRedis();
      } catch (error) {
        logger.error('Error closing Redis:', error);
      }

      // Cerrar servidor HTTP
      server.close(() => {
        logger.info('HTTP server closed');
        process.exit(0);
      });

      // Forzar cierre después de 10 segundos
      setTimeout(() => {
        logger.error('Forced shutdown');
        process.exit(1);
      }, 10000);
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
}

// =====================================================
// MANEJO DE EXCEPCIONES NO CAPTURADAS
// =====================================================

process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// =====================================================
// INICIAR SERVIDOR
// =====================================================

if (require.main === module) {
  startServer();
}

export default app;
