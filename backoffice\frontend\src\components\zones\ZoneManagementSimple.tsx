/**
 * 🌋 Volcano App Backoffice - Gestión de Zonas Simplificada (Para Debugging)
 * Versión simplificada para diagnosticar problemas
 */

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Map, Table } from 'lucide-react';

export function ZoneManagementSimple() {
  const [activeTab, setActiveTab] = useState('table');

  console.log('🎛️ ZoneManagementSimple component rendered');

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Map className="h-5 w-5" />
            Gestión de Zonas de Seguridad (Modo Debug)
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">
            Componente simplificado para diagnosticar problemas con el mapa interactivo.
          </p>
        </CardContent>
      </Card>

      <Tabs value={activeTab} onValueChange={(value) => {
        console.log('🔄 Changing tab to:', value);
        setActiveTab(value);
      }}>
        <TabsList>
          <TabsTrigger value="table">Vista de Tabla</TabsTrigger>
          <TabsTrigger value="map">Vista de Mapa</TabsTrigger>
        </TabsList>

        <TabsContent value="table" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Table className="h-5 w-5" />
                Tabla de Zonas
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                Aquí se mostraría la tabla de zonas de seguridad.
              </p>
              <Button className="mt-4">
                Nueva Zona
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="map" className="space-y-4">
          {console.log('🗺️ Rendering simple map tab content')}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Map className="h-5 w-5" />
                Mapa Interactivo (Simplificado)
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[400px] bg-gray-100 rounded-lg flex items-center justify-center">
                <div className="text-center">
                  <Map className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                  <h3 className="text-lg font-semibold mb-2">Mapa en Desarrollo</h3>
                  <p className="text-muted-foreground">
                    El mapa interactivo se cargará aquí una vez resueltos los problemas de dependencias.
                  </p>
                  <Button className="mt-4">
                    Cargar Mapa Completo
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default ZoneManagementSimple;
