/**
 * 🌋 Volcano App Backend - Controlador para App Móvil
 * Endpoints específicos para la aplicación móvil
 */

import {
    cacheActiveZones,
    cacheCurrentAlert,
    cacheMobileConfig,
    getCachedActiveZones,
    getCachedCurrentAlert,
    getCachedMobileConfig
} from '@/services/cache';
import { supabase, supabaseAdmin } from '@/services/supabase';
import { ReportLocationDto } from '@/types';
import { logger } from '@/utils/logger';
import { Request, Response } from 'express';

// =====================================================
// OBTENER ALERTA ACTUAL
// =====================================================

/**
 * Obtener la alerta volcánica actual para la app móvil
 * GET /api/mobile/alerts/current
 */
export async function getCurrentAlert(req: Request, res: Response) {
  try {
    // Try to get from cache first
    let alert = await getCachedCurrentAlert();

    if (!alert) {
      // Fetch from database if not in cache
      const { data: dbAlert, error } = await supabase
        .from('volcano_alerts')
        .select(`
          id,
          title,
          message,
          alert_level,
          volcano_name,
          volcano_lat,
          volcano_lng,
          created_at,
          expires_at,
          metadata
        `)
        .eq('is_active', true)
        .order('created_at', { ascending: false })
        .limit(1)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      alert = dbAlert;

      // Cache the result
      if (alert) {
        await cacheCurrentAlert(alert);
      }
    }

    res.json({
      success: true,
      data: alert || null,
      cached: !!alert,
      timestamp: new Date()
    });

  } catch (error) {
    logger.error('Get current alert error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get current alert',
      timestamp: new Date()
    });
  }
}

// =====================================================
// OBTENER TODAS LAS ZONAS PARA MÓVIL
// =====================================================

/**
 * Obtener todas las zonas de seguridad para la app móvil
 * GET /api/mobile/zones/all
 */
export async function getAllZones(req: Request, res: Response) {
  try {
    const { data: zones, error } = await supabase
      .from('safety_zones')
      .select(`
        id,
        name,
        description,
        zone_type,
        geometry,
        capacity,
        contact_info,
        facilities,
        is_active,
        version
      `)
      .eq('is_active', true)
      .order('zone_type', { ascending: true });

    if (error) {
      throw error;
    }

    res.json({
      success: true,
      data: zones || [],
      timestamp: new Date()
    });

  } catch (error) {
    logger.error('Get mobile zones error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get zones for mobile app',
      timestamp: new Date()
    });
  }
}

// =====================================================
// REPORTAR UBICACIÓN DE USUARIO
// =====================================================

/**
 * Reportar ubicación de usuario anónimo desde la app móvil
 * POST /api/mobile/location/report
 */
export async function reportLocation(req: Request, res: Response) {
  try {
    const locationData: ReportLocationDto = req.body;

    // Validaciones básicas
    if (!locationData.anonymous_id || !locationData.latitude || !locationData.longitude) {
      return res.status(400).json({
        success: false,
        error: 'anonymous_id, latitude, and longitude are required',
        timestamp: new Date()
      });
    }

    // Validar coordenadas
    if (locationData.latitude < -90 || locationData.latitude > 90) {
      return res.status(400).json({
        success: false,
        error: 'Invalid latitude. Must be between -90 and 90',
        timestamp: new Date()
      });
    }

    if (locationData.longitude < -180 || locationData.longitude > 180) {
      return res.status(400).json({
        success: false,
        error: 'Invalid longitude. Must be between -180 and 180',
        timestamp: new Date()
      });
    }

    // Preparar datos para inserción
    const insertData = {
      anonymous_id: locationData.anonymous_id,
      location: `POINT(${locationData.longitude} ${locationData.latitude})`,
      accuracy: locationData.accuracy || null,
      reported_at: new Date().toISOString(),
      app_version: locationData.app_version || null,
      device_type: locationData.device_type || null,
      expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 horas
    };

    // Insertar ubicación (permitir múltiples reportes del mismo dispositivo)
    const { data: location, error } = await supabaseAdmin
      .from('app_users_locations')
      .insert(insertData)
      .select()
      .single();

    if (error) {
      throw error;
    }

    res.json({
      success: true,
      data: {
        id: location.id,
        reported_at: location.reported_at,
        expires_at: location.expires_at
      },
      message: 'Location reported successfully',
      timestamp: new Date()
    });

  } catch (error) {
    logger.error('Report location error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to report location',
      timestamp: new Date()
    });
  }
}

// =====================================================
// VERIFICAR UBICACIÓN EN ZONAS
// =====================================================

/**
 * Verificar si una ubicación está dentro de zonas de seguridad
 * POST /api/mobile/location/check
 */
export async function checkLocationInZones(req: Request, res: Response) {
  try {
    const { latitude, longitude } = req.body;

    // Validaciones básicas
    if (!latitude || !longitude) {
      return res.status(400).json({
        success: false,
        error: 'latitude and longitude are required',
        timestamp: new Date()
      });
    }

    // Validar coordenadas
    if (latitude < -90 || latitude > 90 || longitude < -180 || longitude > 180) {
      return res.status(400).json({
        success: false,
        error: 'Invalid coordinates',
        timestamp: new Date()
      });
    }

    // Buscar zonas que contengan el punto
    const { data: zones, error } = await supabaseAdmin
      .rpc('find_zones_containing_point', {
        point_lat: latitude,
        point_lng: longitude
      });

    if (error) {
      throw error;
    }

    // Determinar el estado de seguridad
    let safetyStatus = 'unknown';
    let recommendations: string[] = [];

    if (zones && zones.length > 0) {
      const zoneTypes = zones.map((zone: any) => zone.zone_type);
      
      if (zoneTypes.includes('DANGER')) {
        safetyStatus = 'danger';
        recommendations.push('You are in a danger zone. Evacuate immediately.');
      } else if (zoneTypes.includes('EVACUATION')) {
        safetyStatus = 'evacuation';
        recommendations.push('You are in an evacuation zone. Follow evacuation procedures.');
      } else if (zoneTypes.includes('SAFE')) {
        safetyStatus = 'safe';
        recommendations.push('You are in a safe zone.');
      } else if (zoneTypes.includes('EMERGENCY')) {
        safetyStatus = 'emergency';
        recommendations.push('You are in an emergency zone. Stay alert and follow instructions.');
      }
    } else {
      safetyStatus = 'outside';
      recommendations.push('You are outside defined safety zones. Stay informed about volcanic activity.');
    }

    res.json({
      success: true,
      data: {
        location: {
          latitude,
          longitude
        },
        safety_status: safetyStatus,
        zones: zones || [],
        recommendations,
        checked_at: new Date()
      },
      timestamp: new Date()
    });

  } catch (error) {
    logger.error('Check location in zones error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to check location',
      timestamp: new Date()
    });
  }
}

// =====================================================
// OBTENER CONFIGURACIÓN PARA MÓVIL
// =====================================================

/**
 * Obtener configuración específica para la app móvil
 * GET /api/mobile/config
 */
export async function getMobileConfig(req: Request, res: Response) {
  try {
    // Obtener configuraciones relevantes para móvil
    const { data: configs, error } = await supabase
      .from('system_config')
      .select('key, value')
      .in('key', [
        'mobile_app_version',
        'volcano_coordinates',
        'emergency_contacts',
        'update_intervals',
        'notification_settings'
      ]);

    if (error) {
      throw error;
    }

    // Convertir a objeto
    const configObject = configs?.reduce((acc: any, config: any) => {
      acc[config.key] = config.value;
      return acc;
    }, {}) || {};

    // Configuración por defecto si no existe
    const defaultConfig = {
      mobile_app_version: '1.0.0',
      volcano_coordinates: {
        lat: -39.420000,
        lng: -71.939167,
        name: 'Volcán Villarrica'
      },
      emergency_contacts: {
        emergency: '133',
        police: '133',
        fire: '132',
        medical: '131'
      },
      update_intervals: {
        alerts: 30000, // 30 segundos
        zones: 300000, // 5 minutos
        location: 60000 // 1 minuto
      },
      notification_settings: {
        enabled: true,
        sound: true,
        vibration: true
      }
    };

    const finalConfig = { ...defaultConfig, ...configObject };

    res.json({
      success: true,
      data: finalConfig,
      timestamp: new Date()
    });

  } catch (error) {
    logger.error('Get mobile config error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get mobile configuration',
      timestamp: new Date()
    });
  }
}

// =====================================================
// BULK SYNC FOR MOBILE APPS
// =====================================================

/**
 * Bulk sync endpoint for mobile apps - optimized for offline-first apps
 * POST /api/mobile/sync
 */
export async function bulkSync(req: Request, res: Response) {
  try {
    const {
      lastSync,
      deviceInfo,
      appVersion,
      requestedData = ['alerts', 'zones', 'config']
    } = req.body;

    const syncTimestamp = lastSync ? new Date(lastSync) : new Date(0);
    const syncData: any = {
      syncTimestamp: new Date().toISOString(),
      serverTime: new Date().toISOString()
    };

    // Get alerts if requested
    if (requestedData.includes('alerts')) {
      let alerts = await getCachedCurrentAlert();
      if (!alerts) {
        const { data: dbAlerts, error } = await supabase
          .from('volcano_alerts')
          .select('*')
          .eq('is_active', true)
          .gte('updated_at', syncTimestamp.toISOString())
          .order('created_at', { ascending: false });

        if (error) throw error;
        alerts = dbAlerts;
        if (alerts && alerts.length > 0) {
          await cacheCurrentAlert(alerts[0]);
        }
      }
      syncData.alerts = Array.isArray(alerts) ? alerts : (alerts ? [alerts] : []);
    }

    // Get zones if requested
    if (requestedData.includes('zones')) {
      let zones = await getCachedActiveZones();
      if (!zones || zones.length === 0) {
        const { data: dbZones, error } = await supabase
          .from('safety_zones')
          .select(`
            id, name, description, zone_type, geometry,
            capacity, contact_info, facilities, is_active, version
          `)
          .eq('is_active', true)
          .gte('updated_at', syncTimestamp.toISOString())
          .order('zone_type', { ascending: true });

        if (error) throw error;
        zones = dbZones || [];
        await cacheActiveZones(zones);
      }
      syncData.zones = zones;
    }

    // Get config if requested
    if (requestedData.includes('config')) {
      let config = await getCachedMobileConfig();
      if (!config) {
        const { data: configs, error } = await supabase
          .from('system_config')
          .select('key, value')
          .in('key', [
            'mobile_app_version',
            'volcano_coordinates',
            'emergency_contacts',
            'update_intervals',
            'notification_settings'
          ]);

        if (error) throw error;

        config = configs?.reduce((acc: any, item: any) => {
          acc[item.key] = item.value;
          return acc;
        }, {}) || {};

        // Add defaults
        config = {
          mobile_app_version: '1.0.0',
          volcano_coordinates: { lat: -39.420000, lng: -71.939167, name: 'Volcán Villarrica' },
          emergency_contacts: { emergency: '133', police: '133', fire: '132', medical: '131' },
          update_intervals: { alerts: 30000, zones: 300000, location: 60000 },
          notification_settings: { enabled: true, sound: true, vibration: true },
          ...config
        };

        await cacheMobileConfig(config);
      }
      syncData.config = config;
    }

    // Add metadata
    syncData.metadata = {
      deviceInfo,
      appVersion,
      requestedData,
      dataSize: JSON.stringify(syncData).length,
      compressionAvailable: req.headers['accept-encoding']?.includes('gzip')
    };

    res.json({
      success: true,
      data: syncData,
      timestamp: new Date()
    });

  } catch (error) {
    logger.error('Bulk sync error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to sync data',
      timestamp: new Date()
    });
  }
}

/**
 * Check app version compatibility
 * GET /api/mobile/version/check
 */
export async function checkAppVersion(req: Request, res: Response) {
  try {
    const { version, platform } = req.query;

    if (!version) {
      return res.status(400).json({
        success: false,
        error: 'App version is required',
        timestamp: new Date()
      });
    }

    // Get minimum supported versions from config
    const { data: versionConfig } = await supabase
      .from('system_config')
      .select('value')
      .eq('key', 'mobile_app_versions')
      .single();

    const supportedVersions = versionConfig?.value || {
      minimum: '1.0.0',
      latest: '1.0.0',
      deprecated: []
    };

    const isSupported = compareVersions(version as string, supportedVersions.minimum) >= 0;
    const isLatest = version === supportedVersions.latest;
    const isDeprecated = supportedVersions.deprecated.includes(version);

    res.json({
      success: true,
      data: {
        version: version,
        platform: platform,
        isSupported,
        isLatest,
        isDeprecated,
        minimumVersion: supportedVersions.minimum,
        latestVersion: supportedVersions.latest,
        updateRequired: !isSupported,
        updateRecommended: !isLatest && isSupported
      },
      timestamp: new Date()
    });

  } catch (error) {
    logger.error('Version check error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to check app version',
      timestamp: new Date()
    });
  }
}

/**
 * Batch location reporting for offline sync
 * POST /api/mobile/location/batch
 */
export async function batchReportLocation(req: Request, res: Response) {
  try {
    const { locations, deviceInfo } = req.body;

    if (!Array.isArray(locations) || locations.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Locations array is required and cannot be empty',
        timestamp: new Date()
      });
    }

    if (locations.length > 100) {
      return res.status(400).json({
        success: false,
        error: 'Maximum 100 locations per batch',
        timestamp: new Date()
      });
    }

    const processedLocations = [];
    const errors = [];

    for (const [index, location] of locations.entries()) {
      try {
        // Validate each location
        if (!location.anonymous_id || !location.latitude || !location.longitude) {
          errors.push({
            index,
            error: 'Missing required fields: anonymous_id, latitude, longitude'
          });
          continue;
        }

        if (location.latitude < -90 || location.latitude > 90 ||
            location.longitude < -180 || location.longitude > 180) {
          errors.push({
            index,
            error: 'Invalid coordinates'
          });
          continue;
        }

        const insertData = {
          anonymous_id: location.anonymous_id,
          location: `POINT(${location.longitude} ${location.latitude})`,
          accuracy: location.accuracy || null,
          reported_at: location.timestamp || new Date().toISOString(),
          app_version: deviceInfo?.appVersion || null,
          device_type: deviceInfo?.platform || null,
          expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
        };

        processedLocations.push(insertData);
      } catch (error) {
        errors.push({
          index,
          error: error instanceof Error ? error.message : 'Processing error'
        });
      }
    }

    // Batch insert processed locations
    let insertedCount = 0;
    if (processedLocations.length > 0) {
      const { data, error } = await supabaseAdmin
        .from('app_users_locations')
        .insert(processedLocations)
        .select('id');

      if (error) {
        throw error;
      }

      insertedCount = data?.length || 0;
    }

    res.json({
      success: true,
      data: {
        totalReceived: locations.length,
        processed: processedLocations.length,
        inserted: insertedCount,
        errors: errors.length,
        errorDetails: errors
      },
      timestamp: new Date()
    });

  } catch (error) {
    logger.error('Batch location report error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to process batch location report',
      timestamp: new Date()
    });
  }
}

// =====================================================
// UTILITY FUNCTIONS
// =====================================================

/**
 * Compare semantic versions
 */
function compareVersions(version1: string, version2: string): number {
  const v1parts = version1.split('.').map(Number);
  const v2parts = version2.split('.').map(Number);

  for (let i = 0; i < Math.max(v1parts.length, v2parts.length); i++) {
    const v1part = v1parts[i] || 0;
    const v2part = v2parts[i] || 0;

    if (v1part > v2part) return 1;
    if (v1part < v2part) return -1;
  }

  return 0;
}

// =====================================================
// EXPORTACIONES
// =====================================================

export default {
  getCurrentAlert,
  getAllZones,
  reportLocation,
  checkLocationInZones,
  getMobileConfig,
  bulkSync,
  checkAppVersion,
  batchReportLocation
};
