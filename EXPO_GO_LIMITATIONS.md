# 🌋 Volcano App - Limitaciones de Expo Go

## 📱 **Limitaciones de Notificaciones Push en Expo Go**

### ⚠️ **Limitaciones Conocidas:**

1. **Notificaciones Push Remotas:**
   - ❌ **No funcionan** en Expo Go desde SDK 53
   - ❌ **No se pueden recibir** notificaciones desde servicios externos
   - ❌ **No se puede obtener** token de push válido

2. **Funcionalidades Disponibles:**
   - ✅ **Notificaciones locales** funcionan correctamente
   - ✅ **Permisos de notificaciones** se pueden solicitar
   - ✅ **Efectos hápticos** y vibración funcionan
   - ✅ **Sonidos de notificación** funcionan

### 🔧 **Configuración Actual:**

```typescript
// En services/notifications-simple.ts
async registerForPushNotifications(): Promise<string | null> {
  try {
    const projectId = Constants.expoConfig?.extra?.eas?.projectId || 'volcano-app-mobile';
    
    const token = await Notifications.getExpoPushTokenAsync({
      projectId: projectId,
    });
    
    return token.data;
  } catch (error) {
    console.warn('⚠️ Push notifications may not work in Expo Go. Use development build for full functionality.');
    return null;
  }
}
```

### 📋 **Funcionalidades Implementadas:**

#### ✅ **Notificaciones Locales:**
- Alertas volcánicas simuladas
- Notificaciones de prueba
- Efectos hápticos personalizados
- Vibración según tipo de alerta

#### ✅ **Integración con Backend:**
- WebSocket para notificaciones en tiempo real
- API REST para datos de volcanes
- Supabase para persistencia (con polling)

#### ✅ **Experiencia de Usuario:**
- Permisos de notificaciones
- Configuración de sonidos
- Navegación desde notificaciones
- Estados de carga y error

### 🚀 **Para Funcionalidad Completa:**

#### **Opción 1: Development Build**
```bash
# Instalar EAS CLI
npm install -g @expo/eas-cli

# Configurar proyecto
eas build:configure

# Crear development build
eas build --platform android --profile development
```

#### **Opción 2: Expo Prebuild**
```bash
# Generar código nativo
npx expo prebuild

# Ejecutar en Android
npx expo run:android

# Ejecutar en iOS
npx expo run:ios
```

### 🧪 **Cómo Probar en Expo Go:**

#### **1. Notificaciones Locales:**
```typescript
// Desde la app, usar el hook useNotifications
const { showTestNotification } = useNotifications();

// Mostrar notificación de prueba
await showTestNotification();
```

#### **2. Simulación de Alertas:**
```typescript
// Simular alerta volcánica
await notificationService.showLocalNotification(
  '🌋 Alerta Volcánica: AMARILLA',
  'Actividad volcánica detectada en Volcán Cotopaxi',
  {
    type: 'volcano_alert',
    alertLevel: 'AMARILLA',
    volcanoName: 'Cotopaxi',
    timestamp: new Date().toISOString()
  }
);
```

#### **3. Efectos Hápticos:**
```typescript
// Vibración de emergencia
Vibration.vibrate([0, 1000, 500, 1000, 500, 1000]);

// Hápticos en iOS
Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
```

### 📊 **Monitoreo y Logs:**

#### **Logs Esperados en Expo Go:**
```
✅ Polyfills initialized for React Native
🔔 Initializing simple notification service...
📱 Attempting to get push token with projectId: volcano-app-mobile
⚠️ Push notifications may not work in Expo Go. Use development build for full functionality.
✅ Simple notification service initialized successfully
```

#### **Errores Normales (No críticos):**
```
ERROR expo-notifications: Android Push notifications (remote notifications) functionality provided by expo-notifications was removed from Expo Go with the release of SDK 53.
ERROR Error getting push token: [Error: No "projectId" found...]
```

### 🔄 **Alternativas Implementadas:**

#### **1. Polling en lugar de WebSockets:**
- Verificación cada 30 segundos de nuevas alertas
- Compatible con todas las plataformas
- Menor consumo de batería

#### **2. Notificaciones Locales Programadas:**
- Simulación de alertas en tiempo real
- Efectos visuales y sonoros
- Navegación contextual

#### **3. Sincronización con Backend:**
- API REST para datos actualizados
- Cache local para modo offline
- Reintentos automáticos

### 📝 **Recomendaciones:**

1. **Para Desarrollo:**
   - Usar Expo Go para pruebas rápidas
   - Probar notificaciones locales
   - Validar UX y navegación

2. **Para Producción:**
   - Crear development build
   - Implementar notificaciones push reales
   - Configurar certificados de producción

3. **Para Testing:**
   - Usar simulador de alertas
   - Probar diferentes tipos de notificaciones
   - Validar efectos hápticos

### 🆘 **Solución de Problemas:**

#### **Si no funcionan las notificaciones locales:**
```typescript
// Verificar permisos
const permissions = await Notifications.getPermissionsAsync();
console.log('Permissions:', permissions);

// Solicitar permisos si es necesario
if (permissions.status !== 'granted') {
  await Notifications.requestPermissionsAsync();
}
```

#### **Si no se conecta al backend:**
```typescript
// Verificar configuración de red
console.log('API URL:', process.env.EXPO_PUBLIC_API_URL);
console.log('WebSocket URL:', process.env.EXPO_PUBLIC_WEBSOCKET_URL);
```

### 📚 **Referencias:**

- [Expo Notifications Documentation](https://docs.expo.dev/versions/latest/sdk/notifications/)
- [Development Builds](https://docs.expo.dev/develop/development-builds/introduction/)
- [EAS Build](https://docs.expo.dev/build/introduction/)
- [Push Notifications Guide](https://docs.expo.dev/push-notifications/overview/)
