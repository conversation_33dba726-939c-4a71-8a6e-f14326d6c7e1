import { <PERSON><PERSON><PERSON><PERSON>gle, MapPin } from 'lucide-react'
import React, { useEffect, useState } from 'react'
import { Badge } from '../ui/badge'
import { Button } from '../ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import {
    <PERSON>alog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from '../ui/dialog'
import { Input } from '../ui/input'
import { Label } from '../ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select'
import { Textarea } from '../ui/textarea'

interface Alert {
  id: string
  title: string
  message: string
  alert_level: 'NORMAL' | 'ADVISORY' | 'WATCH' | 'WARNING' | 'EMERGENCY'
  volcano_name: string
  volcano_lat: number
  volcano_lng: number
  is_active: boolean
  is_scheduled: boolean
  scheduled_for?: string
  expires_at?: string
  created_by?: string
  created_at: string
  updated_at: string
  metadata: any
}

interface AlertFormData {
  title: string
  message: string
  alert_level: string
  volcano_name: string
  volcano_lat: number
  volcano_lng: number
  is_active: boolean
  expires_at?: string
}

interface AlertDialogProps {
  open: boolean
  onClose: () => void
  alert?: Alert | null
  onSave: (id: string | null, data: AlertFormData) => void
  isSubmitting: boolean
}

const alertLevels = [
  { value: 'NORMAL', label: 'Normal', color: 'success', icon: '✅', description: 'Actividad volcánica normal' },
  { value: 'ADVISORY', label: 'Aviso', color: 'info', icon: '🟡', description: 'Actividad volcánica elevada' },
  { value: 'WATCH', label: 'Vigilancia', color: 'warning', icon: '🟠', description: 'Actividad volcánica significativa' },
  { value: 'WARNING', label: 'Alerta', color: 'destructive', icon: '🔴', description: 'Actividad volcánica peligrosa' },
  { value: 'EMERGENCY', label: 'Emergencia', color: 'destructive', icon: '🚨', description: 'Erupción inminente o en curso' }
]

const volcanoes = [
  { name: 'Villarrica', lat: -39.420000, lng: -71.939167 },
  { name: 'Osorno', lat: -41.103333, lng: -72.493333 },
  { name: 'Calbuco', lat: -41.330000, lng: -72.614167 },
  { name: 'Chaitén', lat: -42.833333, lng: -72.646111 },
  { name: 'Llaima', lat: -38.692222, lng: -71.729167 }
]

export function AlertDialog({ open, onClose, alert, onSave, isSubmitting }: AlertDialogProps) {
  const [formData, setFormData] = useState<AlertFormData>({
    title: '',
    message: '',
    alert_level: 'NORMAL',
    volcano_name: 'Villarrica',
    volcano_lat: -39.420000,
    volcano_lng: -71.939167,
    is_active: true,
    expires_at: ''
  })

  const [errors, setErrors] = useState<Record<string, string>>({})

  useEffect(() => {
    if (alert) {
      setFormData({
        title: alert.title,
        message: alert.message,
        alert_level: alert.alert_level,
        volcano_name: alert.volcano_name,
        volcano_lat: alert.volcano_lat,
        volcano_lng: alert.volcano_lng,
        is_active: alert.is_active,
        expires_at: alert.expires_at || ''
      })
    } else {
      setFormData({
        title: '',
        message: '',
        alert_level: 'NORMAL',
        volcano_name: 'Villarrica',
        volcano_lat: -39.420000,
        volcano_lng: -71.939167,
        is_active: true,
        expires_at: ''
      })
    }
    setErrors({})
  }, [alert, open])

  const handleVolcanoChange = (volcanoName: string) => {
    const volcano = volcanoes.find(v => v.name === volcanoName)
    if (volcano) {
      setFormData(prev => ({
        ...prev,
        volcano_name: volcano.name,
        volcano_lat: volcano.lat,
        volcano_lng: volcano.lng
      }))
    }
  }

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!formData.title.trim()) {
      newErrors.title = 'El título es requerido'
    } else if (formData.title.trim().length < 3) {
      newErrors.title = 'El título debe tener al menos 3 caracteres'
    } else if (formData.title.trim().length > 200) {
      newErrors.title = 'El título no puede exceder 200 caracteres'
    }

    if (!formData.message.trim()) {
      newErrors.message = 'El mensaje es requerido'
    } else if (formData.message.trim().length < 10) {
      newErrors.message = 'El mensaje debe tener al menos 10 caracteres'
    } else if (formData.message.trim().length > 1000) {
      newErrors.message = 'El mensaje no puede exceder 1000 caracteres'
    }

    // Validar fecha de expiración si se proporciona
    if (formData.expires_at && formData.expires_at.trim()) {
      const expirationDate = new Date(formData.expires_at)
      if (isNaN(expirationDate.getTime())) {
        newErrors.expires_at = 'La fecha de expiración debe ser válida'
      } else if (expirationDate <= new Date()) {
        newErrors.expires_at = 'La fecha de expiración debe ser futura'
      }
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    // Preparar datos para envío, convirtiendo cadenas vacías a null
    const submitData = {
      ...formData,
      expires_at: formData.expires_at && formData.expires_at.trim() ? formData.expires_at : null
    }

    onSave(alert?.id || null, submitData)
  }

  const selectedLevel = alertLevels.find(level => level.value === formData.alert_level)

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5" />
            {alert ? 'Editar Alerta Volcánica' : 'Nueva Alerta Volcánica'}
          </DialogTitle>
          <DialogDescription>
            {alert 
              ? 'Modifica los detalles de la alerta volcánica existente.'
              : 'Crea una nueva alerta para el sistema de monitoreo volcánico.'
            }
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Información básica */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Información Básica</CardTitle>
              <CardDescription>Detalles principales de la alerta</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="title">Título de la Alerta</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                  placeholder="Ej: Actividad volcánica elevada en Villarrica"
                  className={errors.title ? 'border-destructive' : ''}
                />
                {errors.title && (
                  <p className="text-sm text-destructive">{errors.title}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="message">Mensaje de la Alerta</Label>
                <Textarea
                  id="message"
                  value={formData.message}
                  onChange={(e) => setFormData(prev => ({ ...prev, message: e.target.value }))}
                  placeholder="Describe los detalles de la alerta volcánica..."
                  rows={4}
                  className={errors.message ? 'border-destructive' : ''}
                />
                <div className="flex justify-between text-sm text-muted-foreground">
                  {errors.message && (
                    <span className="text-destructive">{errors.message}</span>
                  )}
                  <span className="ml-auto">{formData.message.length}/1000</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Nivel de alerta */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Nivel de Alerta</CardTitle>
              <CardDescription>Selecciona la severidad de la alerta</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Label>Nivel de Alerta</Label>
                <Select
                  value={formData.alert_level}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, alert_level: value }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {alertLevels.map((level) => (
                      <SelectItem key={level.value} value={level.value}>
                        <div className="flex items-center gap-2">
                          <span>{level.icon}</span>
                          <span>{level.label}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {selectedLevel && (
                  <div className="flex items-center gap-2 mt-2">
                    <Badge variant={selectedLevel.color as any}>
                      {selectedLevel.icon} {selectedLevel.label}
                    </Badge>
                    <span className="text-sm text-muted-foreground">
                      {selectedLevel.description}
                    </span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Ubicación */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <MapPin className="h-4 w-4" />
                Ubicación del Volcán
              </CardTitle>
              <CardDescription>Selecciona el volcán asociado a esta alerta</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Label>Volcán</Label>
                <Select
                  value={formData.volcano_name}
                  onValueChange={handleVolcanoChange}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {volcanoes.map((volcano) => (
                      <SelectItem key={volcano.name} value={volcano.name}>
                        {volcano.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <div className="text-sm text-muted-foreground">
                  Coordenadas: {formData.volcano_lat.toFixed(6)}, {formData.volcano_lng.toFixed(6)}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Configuración */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Configuración</CardTitle>
              <CardDescription>Opciones adicionales de la alerta</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="is_active"
                  checked={formData.is_active}
                  onChange={(e) => setFormData(prev => ({ ...prev, is_active: e.target.checked }))}
                  className="rounded border-gray-300"
                />
                <Label htmlFor="is_active">Alerta activa</Label>
              </div>

              <div className="space-y-2">
                <Label htmlFor="expires_at">Fecha de Expiración (Opcional)</Label>
                <Input
                  id="expires_at"
                  type="datetime-local"
                  value={formData.expires_at}
                  onChange={(e) => setFormData(prev => ({ ...prev, expires_at: e.target.value }))}
                  className={errors.expires_at ? 'border-destructive' : ''}
                />
                {errors.expires_at && (
                  <p className="text-sm text-destructive">{errors.expires_at}</p>
                )}
                <p className="text-xs text-muted-foreground">
                  Si no se especifica, la alerta no expirará automáticamente
                </p>
              </div>
            </CardContent>
          </Card>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose}>
              Cancelar
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'Guardando...' : (alert ? 'Actualizar' : 'Crear')} Alerta
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
