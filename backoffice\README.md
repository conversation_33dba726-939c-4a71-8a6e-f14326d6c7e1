# 🌋 Volcano App - Sistema de Backoffice (Monorepo)

Sistema de administración centralizado para gestionar alertas volcánicas y zonas de seguridad, estructurado como monorepo para máxima eficiencia y mantenibilidad.

## 🏗️ Estructura del Monorepo

```
volcano-app/
├── apps/                    # Aplicaciones principales
│   ├── mobile/              # App móvil React Native (existente)
│   │   ├── components/
│   │   ├── app/
│   │   └── package.json
│   ├── admin-web/           # Panel administrativo React
│   │   ├── src/
│   │   │   ├── components/
│   │   │   ├── pages/
│   │   │   ├── hooks/
│   │   │   ├── services/
│   │   │   └── types/
│   │   ├── package.json
│   │   └── vite.config.ts
│   └── api/                 # Backend API Node.js
│       ├── src/
│       │   ├── controllers/
│       │   ├── middleware/
│       │   ├── services/
│       │   ├── routes/
│       │   └── types/
│       ├── package.json
│       └── tsconfig.json
├── packages/                # Paquetes compartidos
│   ├── shared-types/        # Tipos TypeScript compartidos
│   │   ├── src/
│   │   │   ├── auth.ts
│   │   │   ├── alerts.ts
│   │   │   ├── zones.ts
│   │   │   └── index.ts
│   │   └── package.json
│   ├── ui-components/       # Componentes UI compartidos
│   │   ├── src/
│   │   │   ├── Button/
│   │   │   ├── Modal/
│   │   │   ├── Map/
│   │   │   └── index.ts
│   │   └── package.json
│   ├── api-client/          # Cliente API compartido
│   │   ├── src/
│   │   │   ├── auth.ts
│   │   │   ├── alerts.ts
│   │   │   ├── zones.ts
│   │   │   └── index.ts
│   │   └── package.json
│   └── utils/               # Utilidades compartidas
│       ├── src/
│       │   ├── validation.ts
│       │   ├── formatting.ts
│       │   ├── constants.ts
│       │   └── index.ts
│       └── package.json
├── database/                # Scripts de base de datos
│   ├── schema.sql
│   ├── seed.sql
│   ├── migrations/
│   └── functions/
├── docs/                    # Documentación
│   ├── api/
│   ├── deployment/
│   ├── development/
│   └── architecture/
├── tools/                   # Herramientas de desarrollo
│   ├── scripts/
│   ├── configs/
│   └── docker/
├── package.json             # Root package.json
├── pnpm-workspace.yaml      # Configuración workspace
├── turbo.json               # Configuración Turborepo
├── .gitignore
└── README.md
```

## 🏗️ Arquitectura del Monorepo

### ¿Por qué un Monorepo?

Un **monorepo** es la mejor opción para Volcano App porque:

✅ **Código compartido**: Tipos, utilidades y componentes reutilizables
✅ **Sincronización automática**: Cambios en tipos se propagan instantáneamente
✅ **Builds optimizados**: Turborepo solo reconstruye lo que cambió
✅ **Testing unificado**: Una sola configuración para todas las apps
✅ **Deployment coordinado**: Versiones sincronizadas entre frontend y backend
✅ **Developer Experience**: Un solo comando para desarrollar todo

### Estructura Recomendada

```
volcano-app/                 # 🌋 Monorepo principal
├── apps/                    # 📱 Aplicaciones
│   ├── mobile/              # App móvil React Native (existente)
│   ├── admin-web/           # Panel administrativo React
│   └── api/                 # Backend API Node.js
├── packages/                # � Paquetes compartidos
│   ├── shared-types/        # Tipos TypeScript
│   ├── utils/               # Utilidades comunes
│   ├── ui-components/       # Componentes UI
│   └── api-client/          # Cliente API
├── database/                # 🗄️ Scripts de BD
├── tools/                   # 🛠️ Herramientas
├── package.json             # Configuración raíz
├── pnpm-workspace.yaml      # Workspace config
└── turbo.json               # Turborepo config
```

## �🚀 Tecnologías Utilizadas

### Monorepo Tools
- **pnpm** - Package manager con workspaces
- **Turborepo** - Build system optimizado
- **Changesets** - Versionado y releases
- **TypeScript** - Tipado en todo el stack

### Backend (apps/api/)
- **Node.js** + **TypeScript** - Runtime y tipado
- **Express.js** - Framework web
- **Supabase** - Base de datos PostgreSQL con PostGIS
- **JWT** - Autenticación
- **Winston** - Logging
- **Swagger** - Documentación API

### Frontend Admin (apps/admin-web/)
- **React 18** + **TypeScript** - UI Framework
- **Vite** - Build tool ultra-rápido
- **Tailwind CSS** - Estilos
- **React Query** - Gestión de estado servidor
- **React Hook Form** - Formularios
- **Leaflet** - Mapas interactivos
- **Recharts** - Gráficos y métricas

### Mobile App (apps/mobile/)
- **React Native** + **Expo** - Framework móvil
- **TypeScript** - Tipado
- **Expo Router** - Navegación
- **Leaflet** - Mapas (WebView)
- **Lucide Icons** - Iconografía moderna

### Paquetes Compartidos (packages/)
- **shared-types** - Tipos TypeScript comunes
- **utils** - Utilidades geoespaciales y validación
- **ui-components** - Componentes React reutilizables
- **api-client** - Cliente HTTP para APIs

### Base de Datos
- **PostgreSQL** - Base de datos principal
- **PostGIS** - Extensión geoespacial
- **Supabase** - Backend as a Service

## 🗄️ Esquema de Base de Datos

### Tablas Principales

#### `volcano_alerts`
- Gestión de alertas volcánicas
- Niveles de alerta y programación
- Historial completo

#### `safety_zones`
- Zonas de seguridad georreferenciadas
- Polígonos, círculos y rectángulos
- Tipos: segura, emergencia, peligro

#### `admin_users`
- Usuarios administradores
- Roles y permisos
- Autenticación JWT

#### `audit_logs`
- Registro de todas las acciones
- Trazabilidad completa
- Logs de seguridad

## 🔧 Instalación y Configuración del Monorepo

### Prerrequisitos
- **Node.js 18+** - Runtime de JavaScript
- **pnpm 8+** - Package manager (recomendado para monorepos)
- **Git** - Control de versiones
- **Cuenta de Supabase** - Base de datos

### 1. Configuración Inicial

```bash
# Clonar el repositorio
git clone https://github.com/your-org/volcano-app.git
cd volcano-app

# Instalar pnpm globalmente (si no lo tienes)
npm install -g pnpm

# Instalar todas las dependencias del monorepo
pnpm install

# Verificar que todo esté configurado
pnpm type-check
```

### 2. Configurar Base de Datos (Supabase)

```sql
-- 1. Ejecutar en Supabase SQL Editor
-- Crear esquema y tablas
\i database/schema.sql

-- 2. Insertar datos iniciales
\i database/seed.sql
```

### 3. Configurar Variables de Entorno

```bash
# Backend API
cp apps/api/.env.example apps/api/.env
# Editar apps/api/.env con tus credenciales de Supabase

# Frontend Admin
cp apps/admin-web/.env.example apps/admin-web/.env
# Configurar URL del API

# App Móvil (si es necesario)
cp apps/mobile/.env.example apps/mobile/.env
```

### 4. Desarrollo Local

```bash
# Opción 1: Ejecutar todo el stack completo
pnpm dev

# Opción 2: Ejecutar aplicaciones individuales
pnpm api:dev      # Solo backend API
pnpm admin:dev    # Solo panel admin
pnpm mobile:dev   # Solo app móvil

# Opción 3: Ejecutar en paralelo (recomendado)
pnpm api:dev & pnpm admin:dev
```

### 5. Verificar Instalación

```bash
# Verificar que el API esté funcionando
curl http://localhost:3001/health

# Verificar que el admin esté funcionando
open http://localhost:3000

# Verificar que la app móvil esté funcionando
pnpm mobile:start
```

## 📡 API Endpoints

### Autenticación
- `POST /api/auth/login` - Iniciar sesión
- `POST /api/auth/logout` - Cerrar sesión
- `GET /api/auth/me` - Perfil usuario

### Alertas Volcánicas
- `GET /api/alerts` - Listar alertas
- `POST /api/alerts` - Crear alerta
- `PUT /api/alerts/:id` - Actualizar alerta
- `DELETE /api/alerts/:id` - Eliminar alerta
- `GET /api/alerts/active` - Alertas activas (para app móvil)

### Zonas de Seguridad
- `GET /api/zones` - Listar zonas
- `POST /api/zones` - Crear zona
- `PUT /api/zones/:id` - Actualizar zona
- `DELETE /api/zones/:id` - Eliminar zona
- `GET /api/zones/public` - Zonas públicas (para app móvil)

### Dashboard
- `GET /api/dashboard/stats` - Estadísticas generales
- `GET /api/dashboard/users` - Métricas de usuarios
- `GET /api/dashboard/alerts-history` - Historial de alertas

## 🔐 Seguridad

### Autenticación
- JWT tokens con expiración
- Refresh tokens para sesiones largas
- Roles de administrador

### Autorización
- Middleware de verificación de roles
- Permisos granulares por endpoint
- Validación de datos de entrada

### Auditoría
- Logs de todas las acciones críticas
- Registro de cambios en alertas y zonas
- Trazabilidad completa

## 📱 Integración con App Móvil

### Cambios Necesarios
1. **Remover herramientas de dibujo** del mapa
2. **Consumir APIs oficiales** para alertas y zonas
3. **Implementar sincronización** automática
4. **Agregar notificaciones push** (preparado)

### Endpoints para App Móvil
- `GET /api/mobile/alerts/current` - Alerta actual
- `GET /api/mobile/zones/all` - Todas las zonas oficiales
- `POST /api/mobile/location` - Reportar ubicación (opcional)

## 🚀 Despliegue

### Desarrollo
```bash
# Backend
npm run dev

# Frontend
npm start
```

### Producción
```bash
# Build
npm run build

# Docker (opcional)
docker-compose up -d
```

## 📊 Funcionalidades del Panel Admin

### Dashboard Principal
- Estado actual del volcán
- Métricas de usuarios activos
- Alertas recientes
- Mapa con ubicaciones

### Gestión de Alertas
- CRUD completo de alertas
- Preview de notificaciones
- Programación automática
- Historial detallado

### Gestión de Zonas
- Editor de mapas con herramientas de dibujo
- Validación de geometrías
- Tipos de zonas configurables
- Sincronización automática

### Administración
- Gestión de usuarios admin
- Logs de auditoría
- Configuración del sistema
- Exportación de datos

## 🔄 Flujo de Trabajo

1. **Admin crea/modifica alerta** → API → Base de datos → App móvil
2. **Admin dibuja zona** → Validación → PostGIS → Sincronización
3. **Usuario abre app** → Consulta APIs → Datos oficiales actualizados
4. **Cambios críticos** → Logs de auditoría → Notificaciones

## 📈 Métricas y Monitoreo

- Usuarios activos en tiempo real
- Alertas enviadas por período
- Zonas más consultadas
- Rendimiento de APIs
- Logs de errores y accesos

---

**Próximos pasos:**
1. Configurar base de datos en Supabase
2. Implementar backend API
3. Desarrollar frontend administrativo
4. Modificar app móvil para consumir APIs
5. Testing y documentación completa
