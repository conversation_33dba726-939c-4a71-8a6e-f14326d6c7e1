/**
 * 🌋 Volcano App Frontend - Vitest Configuration
 * Configuración de Vitest para testing del frontend del backoffice
 */

import { defineConfig } from 'vitest/config';
import react from '@vitejs/plugin-react';
import path from 'path';

export default defineConfig({
  plugins: [react()],
  
  test: {
    // Entorno de testing
    environment: 'jsdom',
    
    // Archivos de configuración
    setupFiles: ['./src/test/setup.ts'],
    
    // Patrones de archivos de test
    include: [
      'src/**/*.{test,spec}.{ts,tsx}',
      'src/**/__tests__/**/*.{ts,tsx}'
    ],
    
    // Archivos a excluir
    exclude: [
      'node_modules',
      'dist',
      'build',
      '.next',
      '.nuxt',
      '.vercel',
      '.vscode'
    ],
    
    // Configuración de cobertura
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html', 'lcov'],
      reportsDirectory: './coverage',
      include: [
        'src/**/*.{ts,tsx}'
      ],
      exclude: [
        'src/**/*.d.ts',
        'src/**/*.test.{ts,tsx}',
        'src/**/*.spec.{ts,tsx}',
        'src/**/__tests__/**',
        'src/test/**',
        'src/vite-env.d.ts',
        'src/main.tsx'
      ],
      thresholds: {
        global: {
          branches: 70,
          functions: 70,
          lines: 70,
          statements: 70
        }
      }
    },
    
    // Configuración global
    globals: true,
    
    // Timeout para tests
    testTimeout: 30000,
    
    // Configuración de mock
    clearMocks: true,
    restoreMocks: true,
    
    // Configuración de watch
    watch: false,
    
    // Configuración de reportes
    reporter: ['verbose', 'json', 'html'],
    
    // Configuración de threads
    threads: true,
    
    // Configuración de pool
    pool: 'threads',
    
    // Configuración de retry
    retry: 2
  },
  
  // Alias de resolución
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@/components': path.resolve(__dirname, './src/components'),
      '@/hooks': path.resolve(__dirname, './src/hooks'),
      '@/services': path.resolve(__dirname, './src/services'),
      '@/utils': path.resolve(__dirname, './src/utils'),
      '@/types': path.resolve(__dirname, './src/types'),
      '@/contexts': path.resolve(__dirname, './src/contexts'),
      '@/lib': path.resolve(__dirname, './src/lib')
    }
  },
  
  // Configuración de optimización
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      '@testing-library/react',
      '@testing-library/jest-dom',
      '@testing-library/user-event'
    ]
  },
  
  // Configuración de define
  define: {
    'process.env.NODE_ENV': '"test"',
    'process.env.VITE_SUPABASE_URL': '"https://test.supabase.co"',
    'process.env.VITE_SUPABASE_ANON_KEY': '"test-anon-key"'
  }
});
