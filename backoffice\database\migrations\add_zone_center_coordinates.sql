-- =====================================================
-- MIGRACIÓN: Agregar coordenadas del centro a safety_zones
-- Fecha: 2025-06-16
-- Descripción: Agrega campos center_lat y center_lng para facilitar
--              la edición manual de posición de zonas
-- =====================================================

-- Agregar columnas de coordenadas del centro
ALTER TABLE safety_zones 
ADD COLUMN center_lat DECIMAL(10, 8),
ADD COLUMN center_lng DECIMAL(11, 8);

-- Agregar comentarios para documentar los nuevos campos
COMMENT ON COLUMN safety_zones.center_lat IS 'Latitud del centro de la zona para facilitar edición manual (-90 a 90)';
COMMENT ON COLUMN safety_zones.center_lng IS 'Longitud del centro de la zona para facilitar edición manual (-180 a 180)';

-- Función para calcular el centroide de un polígono y actualizar las coordenadas del centro
CREATE OR REPLACE FUNCTION update_zone_center_coordinates()
RETURNS void AS $$
DECLARE
    zone_record RECORD;
    centroid_point GEOMETRY;
BEGIN
    -- Iterar sobre todas las zonas existentes
    FOR zone_record IN 
        SELECT id, geometry 
        FROM safety_zones 
        WHERE center_lat IS NULL OR center_lng IS NULL
    LOOP
        -- Calcular el centroide del polígono
        centroid_point := ST_Centroid(zone_record.geometry);
        
        -- Actualizar las coordenadas del centro
        UPDATE safety_zones 
        SET 
            center_lat = ST_Y(centroid_point),
            center_lng = ST_X(centroid_point)
        WHERE id = zone_record.id;
        
        RAISE NOTICE 'Updated center coordinates for zone %', zone_record.id;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Ejecutar la función para actualizar zonas existentes
SELECT update_zone_center_coordinates();

-- Crear función trigger para mantener las coordenadas del centro actualizadas automáticamente
CREATE OR REPLACE FUNCTION update_zone_center_on_geometry_change()
RETURNS TRIGGER AS $$
BEGIN
    -- Si la geometría cambió, recalcular el centro
    IF OLD.geometry IS DISTINCT FROM NEW.geometry THEN
        NEW.center_lat := ST_Y(ST_Centroid(NEW.geometry));
        NEW.center_lng := ST_X(ST_Centroid(NEW.geometry));
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Crear trigger para actualizar automáticamente las coordenadas del centro
CREATE TRIGGER update_safety_zones_center_coordinates
    BEFORE UPDATE ON safety_zones
    FOR EACH ROW
    EXECUTE FUNCTION update_zone_center_on_geometry_change();

-- Crear trigger para INSERT también
CREATE OR REPLACE FUNCTION set_zone_center_on_insert()
RETURNS TRIGGER AS $$
BEGIN
    -- Si no se proporcionaron coordenadas del centro, calcularlas desde la geometría
    IF NEW.center_lat IS NULL OR NEW.center_lng IS NULL THEN
        NEW.center_lat := ST_Y(ST_Centroid(NEW.geometry));
        NEW.center_lng := ST_X(ST_Centroid(NEW.geometry));
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER set_safety_zones_center_on_insert
    BEFORE INSERT ON safety_zones
    FOR EACH ROW
    EXECUTE FUNCTION set_zone_center_on_insert();

-- Agregar índices para optimizar consultas por coordenadas
CREATE INDEX idx_safety_zones_center_lat ON safety_zones(center_lat);
CREATE INDEX idx_safety_zones_center_lng ON safety_zones(center_lng);

-- Agregar restricciones para validar rangos de coordenadas
ALTER TABLE safety_zones 
ADD CONSTRAINT check_center_lat_range 
CHECK (center_lat IS NULL OR (center_lat >= -90 AND center_lat <= 90));

ALTER TABLE safety_zones 
ADD CONSTRAINT check_center_lng_range 
CHECK (center_lng IS NULL OR (center_lng >= -180 AND center_lng <= 180));

-- Limpiar función temporal
DROP FUNCTION update_zone_center_coordinates();

COMMIT;
