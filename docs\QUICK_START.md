# ⚡ Inicio Rápido - Volcano App

## 🚀 **<PERSON><PERSON><PERSON><PERSON> con Un Solo Comando**

```bash
# Desde la raíz del proyecto
npm run start:all
```

Este comando arrancará automáticamente:
- 🔧 **Backend API** (Puerto 3001)
- 💻 **Frontend Admin** (Puerto 5173) 
- 📱 **App Móvil** (Puerto 8081)

---

## 📋 **Comandos Disponibles**

### **Inicio Automático:**
```bash
npm run start:all    # Arranca los 3 proyectos
npm run dev:all      # Alias del comando anterior
```

### **Inicio Individual:**
```bash
npm run dev:backend   # Solo Backend API
npm run dev:frontend  # Solo Frontend Admin
npm run dev:mobile    # Solo App Móvil (Expo)
```

### **Comandos Específicos de Expo:**
```bash
npm run dev          # App móvil (Expo)
npm run android      # App móvil en Android
npm run ios          # App móvil en iOS
npm run web          # App móvil en Web
```

---

## 🌐 **URLs de Acceso**

Una vez iniciados los proyectos:

| Aplicación | URL | Descripción |
|------------|-----|-------------|
| 🔧 **Backend API** | http://localhost:3001 | API REST |
| 📚 **API Docs** | http://localhost:3001/api-docs | Documentación Swagger |
| 💻 **Frontend Admin** | http://localhost:5173 | Panel Administrativo |
| 📱 **Expo DevTools** | http://localhost:8081 | Herramientas de Expo |

---

## ✅ **Verificación Rápida**

### **Comprobar que todo funciona:**
```bash
# Backend API
curl http://localhost:3001/health

# Frontend (abrir en navegador)
open http://localhost:5173

# Expo DevTools (abrir en navegador)
open http://localhost:8081
```

---

## 🔧 **Primera Vez - Configuración**

### **1. Instalar Dependencias:**
```bash
# Raíz (App Móvil)
npm install

# Backend
cd backoffice/backend && npm install

# Frontend  
cd ../frontend && npm install
```

### **2. Variables de Entorno:**

Crea los archivos `.env` necesarios:

#### **Backend** (`backoffice/backend/.env`):
```env
PORT=3001
SUPABASE_URL=tu_supabase_url
SUPABASE_ANON_KEY=tu_supabase_key
JWT_SECRET=tu_jwt_secret
NODE_ENV=development
```

#### **Frontend** (`backoffice/frontend/.env`):
```env
VITE_API_URL=http://localhost:3001
VITE_SUPABASE_URL=tu_supabase_url
VITE_SUPABASE_ANON_KEY=tu_supabase_key
```

#### **App Móvil** (`.env` en raíz):
```env
EXPO_PUBLIC_API_URL=http://localhost:3001
EXPO_PUBLIC_SUPABASE_URL=tu_supabase_url
EXPO_PUBLIC_SUPABASE_ANON_KEY=tu_supabase_key
```

---

## 🚨 **Solución de Problemas**

### **Puerto Ocupado:**
```bash
# Verificar qué está usando el puerto
lsof -i :3001
lsof -i :5173
lsof -i :8081

# Matar proceso en puerto específico
kill -9 $(lsof -t -i:3001)
```

### **Dependencias Faltantes:**
```bash
# Limpiar e instalar
rm -rf node_modules package-lock.json
npm install
```

### **Problemas de Expo:**
```bash
# Limpiar caché
expo start -c

# Reinstalar Expo CLI
npm install -g @expo/cli
```

### **Error de TypeScript:**
```bash
# Verificar tipos
npm run type-check

# En cada proyecto
cd backoffice/backend && npm run lint
cd ../frontend && npm run lint
```

---

## 📱 **Desarrollo Móvil**

### **Expo DevTools:**
1. Abre http://localhost:8081
2. Escanea el QR con Expo Go (móvil)
3. O presiona `w` para abrir en web

### **Comandos Útiles:**
```bash
expo start -c          # Limpiar caché
expo start --tunnel     # Túnel para testing remoto
expo start --lan        # Red local
expo install            # Instalar dependencias compatibles
```

---

## 🔄 **Flujo de Desarrollo**

### **Orden Recomendado:**
1. **Backend** → APIs disponibles
2. **Frontend Admin** → Gestión de datos
3. **App Móvil** → Consume APIs

### **Hot Reload:**
- ✅ **Backend**: Nodemon reinicia automáticamente
- ✅ **Frontend**: Vite recarga automáticamente  
- ✅ **App Móvil**: Expo Fast Refresh

---

## 📊 **Monitoreo**

### **Logs en Tiempo Real:**
```bash
# Ver logs de todos los proyectos
npm run start:all

# Logs individuales
npm run dev:backend   # Backend logs
npm run dev:frontend  # Frontend logs (en navegador)
npm run dev:mobile    # Expo logs
```

### **Health Checks:**
- Backend: `GET /health`
- Frontend: Página carga correctamente
- App Móvil: Expo DevTools responde

---

## 🛑 **Detener Todo**

```bash
# Si usaste npm run start:all
Ctrl + C  # Detiene todos los procesos

# Matar procesos manualmente
pkill -f "node.*backend"
pkill -f "vite"
pkill -f "expo"
```

---

## 📚 **Documentación Adicional**

- 📖 [Guía Completa de Inicio](./STARTUP_GUIDE.md)
- 🔧 [Documentación de API](./API_DOCUMENTATION.md)
- 📱 [Guía de Integración Móvil](./MOBILE_INTEGRATION_GUIDE.md)
- 🏗️ [Guía de Desarrollo](./DEVELOPMENT.md)

---

**¡Listo para desarrollar! 🌋**
