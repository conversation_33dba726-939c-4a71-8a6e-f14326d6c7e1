/**
 * 🌋 Volcano App Frontend - Tests para InteractiveMapPage
 * Tests unitarios para el componente de mapa interactivo
 */

import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi } from 'vitest';
import { InteractiveMapPage } from '../InteractiveMapPage';

const mockZones = [
  {
    id: '1',
    name: 'Zona Segura Centro',
    description: 'Zona de evacuación principal',
    zone_type: 'SAFE' as const,
    geometry: {
      type: 'Polygon' as const,
      coordinates: [[[-71.9048, -39.2904], [-71.9000, -39.2904], [-71.9000, -39.2850], [-71.9048, -39.2850], [-71.9048, -39.2904]]]
    },
    is_active: true,
    version: 1,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  },
  {
    id: '2',
    name: 'Zona de Peligro Norte',
    description: 'Zona de alto riesgo volcánico',
    zone_type: 'DANGER' as const,
    geometry: {
      type: 'Polygon' as const,
      coordinates: [[[-71.9100, -39.2800], [-71.9050, -39.2800], [-71.9050, -39.2750], [-71.9100, -39.2750], [-71.9100, -39.2800]]]
    },
    is_active: true,
    version: 1,
    created_at: '2024-01-02T00:00:00Z',
    updated_at: '2024-01-02T00:00:00Z'
  }
];

const mockAlerts = [
  {
    id: '1',
    title: 'Alerta Volcánica Amarilla',
    message: 'Actividad volcánica moderada detectada',
    alert_level: 'YELLOW',
    volcano_name: 'Villarrica',
    volcano_lat: -39.2904,
    volcano_lng: -71.9048,
    is_active: true,
    created_at: '2024-01-01T00:00:00Z',
    expires_at: '2024-12-31T23:59:59Z'
  }
];

const mockProps = {
  zones: mockZones,
  alerts: mockAlerts,
  onRefresh: vi.fn()
};

describe('InteractiveMapPage', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders map page correctly', () => {
    render(<InteractiveMapPage {...mockProps} />);
    
    expect(screen.getByText('Mapa Interactivo - Volcán Villarrica')).toBeInTheDocument();
    expect(screen.getByText('Visualización de zonas de seguridad y alertas volcánicas')).toBeInTheDocument();
  });

  it('displays zone statistics', () => {
    render(<InteractiveMapPage {...mockProps} />);
    
    expect(screen.getByText('Zonas Totales')).toBeInTheDocument();
    expect(screen.getByText('2')).toBeInTheDocument();
    expect(screen.getByText('Zonas Activas')).toBeInTheDocument();
    expect(screen.getByText('Alertas Activas')).toBeInTheDocument();
    expect(screen.getByText('1')).toBeInTheDocument();
  });

  it('shows refresh button', () => {
    render(<InteractiveMapPage {...mockProps} />);
    
    const refreshButton = screen.getByRole('button', { name: /actualizar/i });
    expect(refreshButton).toBeInTheDocument();
  });

  it('calls onRefresh when refresh button is clicked', () => {
    render(<InteractiveMapPage {...mockProps} />);
    
    const refreshButton = screen.getByRole('button', { name: /actualizar/i });
    fireEvent.click(refreshButton);
    
    expect(mockProps.onRefresh).toHaveBeenCalledTimes(1);
  });

  it('displays map controls', () => {
    render(<InteractiveMapPage {...mockProps} />);
    
    expect(screen.getByText('Controles del Mapa')).toBeInTheDocument();
    expect(screen.getByText('Mostrar Zonas Seguras')).toBeInTheDocument();
    expect(screen.getByText('Mostrar Zonas de Peligro')).toBeInTheDocument();
    expect(screen.getByText('Mostrar Alertas')).toBeInTheDocument();
  });

  it('handles zone type filter toggles', () => {
    render(<InteractiveMapPage {...mockProps} />);
    
    const safeZoneToggle = screen.getByLabelText('Mostrar Zonas Seguras');
    const dangerZoneToggle = screen.getByLabelText('Mostrar Zonas de Peligro');
    
    expect(safeZoneToggle).toBeChecked();
    expect(dangerZoneToggle).toBeChecked();
    
    fireEvent.click(safeZoneToggle);
    expect(safeZoneToggle).not.toBeChecked();
  });

  it('displays zone legend', () => {
    render(<InteractiveMapPage {...mockProps} />);
    
    expect(screen.getByText('Leyenda')).toBeInTheDocument();
    expect(screen.getByText('Zona Segura')).toBeInTheDocument();
    expect(screen.getByText('Zona de Emergencia')).toBeInTheDocument();
    expect(screen.getByText('Zona de Peligro')).toBeInTheDocument();
    expect(screen.getByText('Zona de Evacuación')).toBeInTheDocument();
  });

  it('shows map fullscreen toggle', () => {
    render(<InteractiveMapPage {...mockProps} />);
    
    const fullscreenButton = screen.getByRole('button', { name: /pantalla completa/i });
    expect(fullscreenButton).toBeInTheDocument();
  });

  it('handles fullscreen toggle', () => {
    render(<InteractiveMapPage {...mockProps} />);
    
    const fullscreenButton = screen.getByRole('button', { name: /pantalla completa/i });
    fireEvent.click(fullscreenButton);
    
    // Verificar que el mapa cambia a modo pantalla completa
    expect(screen.getByTestId('map-container')).toHaveClass('fullscreen');
  });

  it('displays current alert information', () => {
    render(<InteractiveMapPage {...mockProps} />);
    
    expect(screen.getByText('Alerta Actual')).toBeInTheDocument();
    expect(screen.getByText('Alerta Volcánica Amarilla')).toBeInTheDocument();
    expect(screen.getByText('Actividad volcánica moderada detectada')).toBeInTheDocument();
    expect(screen.getByText('YELLOW')).toBeInTheDocument();
  });

  it('shows no alerts message when no active alerts', () => {
    render(<InteractiveMapPage {...mockProps} alerts={[]} />);
    
    expect(screen.getByText('No hay alertas activas')).toBeInTheDocument();
  });

  it('displays zone information panel', () => {
    render(<InteractiveMapPage {...mockProps} />);
    
    expect(screen.getByText('Información de Zonas')).toBeInTheDocument();
    expect(screen.getByText('Zona Segura Centro')).toBeInTheDocument();
    expect(screen.getByText('Zona de Peligro Norte')).toBeInTheDocument();
  });

  it('handles zone selection', () => {
    render(<InteractiveMapPage {...mockProps} />);
    
    const zoneItem = screen.getByText('Zona Segura Centro');
    fireEvent.click(zoneItem);
    
    // Verificar que se muestra información detallada de la zona
    expect(screen.getByText('Zona de evacuación principal')).toBeInTheDocument();
  });

  it('shows map loading state', () => {
    render(<InteractiveMapPage {...mockProps} zones={[]} alerts={[]} />);
    
    expect(screen.getByText('Cargando mapa...')).toBeInTheDocument();
  });

  it('handles map error state', () => {
    // Simular error en el mapa
    vi.mocked(console.error).mockImplementation(() => {});
    
    render(<InteractiveMapPage {...mockProps} />);
    
    // Simular error de carga del mapa
    const mapContainer = screen.getByTestId('map-container');
    fireEvent.error(mapContainer);
    
    expect(screen.getByText('Error al cargar el mapa')).toBeInTheDocument();
  });

  it('displays correct zone colors in legend', () => {
    render(<InteractiveMapPage {...mockProps} />);
    
    const safeZoneLegend = screen.getByTestId('legend-safe');
    const dangerZoneLegend = screen.getByTestId('legend-danger');
    
    expect(safeZoneLegend).toHaveStyle('background-color: #10b981');
    expect(dangerZoneLegend).toHaveStyle('background-color: #ef4444');
  });

  it('shows volcano marker information', () => {
    render(<InteractiveMapPage {...mockProps} />);
    
    expect(screen.getByText('Volcán Villarrica')).toBeInTheDocument();
    expect(screen.getByText('Lat: -39.2904, Lng: -71.9048')).toBeInTheDocument();
  });

  it('handles map zoom controls', () => {
    render(<InteractiveMapPage {...mockProps} />);
    
    const zoomInButton = screen.getByRole('button', { name: /zoom in/i });
    const zoomOutButton = screen.getByRole('button', { name: /zoom out/i });
    
    expect(zoomInButton).toBeInTheDocument();
    expect(zoomOutButton).toBeInTheDocument();
    
    fireEvent.click(zoomInButton);
    fireEvent.click(zoomOutButton);
  });

  it('displays map scale and coordinates', () => {
    render(<InteractiveMapPage {...mockProps} />);
    
    expect(screen.getByText('Escala: 1:50000')).toBeInTheDocument();
    expect(screen.getByText('Centro: -39.2904, -71.9048')).toBeInTheDocument();
  });

  it('handles keyboard navigation', () => {
    render(<InteractiveMapPage {...mockProps} />);
    
    const mapContainer = screen.getByTestId('map-container');
    
    // Simular navegación con teclado
    fireEvent.keyDown(mapContainer, { key: 'ArrowUp' });
    fireEvent.keyDown(mapContainer, { key: 'ArrowDown' });
    fireEvent.keyDown(mapContainer, { key: 'ArrowLeft' });
    fireEvent.keyDown(mapContainer, { key: 'ArrowRight' });
    
    // Verificar que el mapa responde a la navegación
    expect(mapContainer).toHaveFocus();
  });

  it('shows export options', () => {
    render(<InteractiveMapPage {...mockProps} />);
    
    expect(screen.getByText('Exportar')).toBeInTheDocument();
    expect(screen.getByText('Descargar PNG')).toBeInTheDocument();
    expect(screen.getByText('Descargar PDF')).toBeInTheDocument();
  });

  it('handles map export functionality', () => {
    render(<InteractiveMapPage {...mockProps} />);
    
    const exportPngButton = screen.getByText('Descargar PNG');
    fireEvent.click(exportPngButton);
    
    // Verificar que se inicia la descarga
    expect(screen.getByText('Generando imagen...')).toBeInTheDocument();
  });
});
