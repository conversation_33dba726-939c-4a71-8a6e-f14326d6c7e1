# 🌋 Volcano App - Sistema de Diseño

## Filosofía de Diseño

### Principios Fundamentales

1. **🎯 Accesibilidad Primero**
   - Diseño inclusivo desde el inicio
   - Cumple con WCAG AA
   - Soporte para lectores de pantalla
   - Escalabilidad de texto
   - Alto contraste

2. **🔍 Claridad Radical**
   - Información directa y sin ambigüedades
   - Lenguaje sencillo y comprensible
   - Iconografía universal
   - Jerarquía visual clara

3. **🛡️ Confianza y Autoridad**
   - Transmite credibilidad oficial
   - Colores de alerta estandarizados
   - Tipografía profesional
   - Datos verificados

4. **😌 Calma en Crisis**
   - UI tranquilizadora bajo estrés
   - Evita elementos abrumadores
   - Proporciona sensación de control
   - Feedback claro y positivo

5. **⚡ Eficiencia de Acceso**
   - Mínimos pasos para información crítica
   - Acciones rápidas prominentes
   - Navegación intuitiva
   - Modo offline robusto

## Sistema de Colores

### Niveles de Alerta (Estándares Internacionales)

```typescript
NORMAL: '#22C55E'    // Verde - Actividad normal
ADVISORY: '#F59E0B'  // Amarillo - Cambios detectados
WATCH: '#F97316'     // Naranja - Actividad elevada
WARNING: '#EF4444'   // Rojo - Erupción probable
EMERGENCY: '#8B5CF6' // Púrpura - Erupción inminente/en curso
```

### Colores de Marca

```typescript
Primary: '#1E40AF'    // Azul confiable
Secondary: '#64748B'  // Gris neutro
Accent: '#0EA5E9'     // Azul cielo para destacados
```

### Paleta Neutral (Alto Contraste)

- **Texto**: `#0F172A` (light) / `#F1F5F9` (dark)
- **Fondos**: `#FFFFFF` / `#0F172A`
- **Bordes**: `#E2E8F0` / `#334155`

## Tipografía

### Familias de Fuentes

- **Primaria**: SF Pro Display (iOS) / Roboto (Android)
- **Secundaria**: SF Pro Text (iOS) / Roboto (Android)
- **Monoespaciada**: SF Mono (iOS) / Roboto Mono (Android)

### Escala Tipográfica

```typescript
h1: 36px, Bold, Tight line-height
h2: 30px, Bold, Tight line-height
h3: 24px, Semibold, Normal line-height
h4: 20px, Semibold, Normal line-height
body: 16px, Regular, Relaxed line-height
bodyLarge: 18px, Regular, Relaxed line-height
button: 16px, Semibold, Wide letter-spacing
alert: 18px, Bold, Wide letter-spacing
```

### Escalas de Accesibilidad

- **Normal**: 1.0x
- **Grande**: 1.15x
- **Extra Grande**: 1.3x
- **Enorme**: 1.5x

## Espaciado y Layout

### Sistema de Grilla (8pt)

```typescript
xs: 4px
sm: 8px
md: 16px
lg: 24px
xl: 32px
2xl: 48px
3xl: 64px
4xl: 96px
```

### Objetivos de Toque (Accesibilidad)

- **Mínimo**: 44px (iOS/Android estándar)
- **Cómodo**: 48px
- **Grande**: 56px
- **Emergencia**: 64px+

### Radios de Borde

```typescript
sm: 4px   // Elementos pequeños
md: 8px   // Botones estándar
lg: 12px  // Tarjetas
xl: 16px  // Botones grandes
2xl: 24px // Modales
```

## Componentes Clave

### 1. AccessibleText
- Escalabilidad automática
- Soporte para múltiples variantes
- Colores semánticos
- Modo de emergencia

### 2. AccessibleButton
- Feedback háptico
- Múltiples variantes (primary, secondary, outline, emergency)
- Tamaños adaptativos
- Estados de carga y deshabilitado

### 3. VolcanoStatus
- Indicador visual del nivel de alerta
- Información contextual
- Última actualización
- Modo compacto disponible

### 4. AlertSystem
- Notificaciones inteligentes
- Priorización automática
- Efectos de vibración/sonido
- Auto-dismiss para alertas menores

### 5. EmergencyMode
- UI ultra-simplificada
- Activación automática
- Acciones críticas prominentes
- Efectos visuales de atención

## Patrones de Interacción

### Microinteracciones

1. **Feedback Háptico**
   - Toque ligero: Navegación
   - Toque medio: Acciones importantes
   - Toque fuerte: Emergencias

2. **Animaciones**
   - Transiciones suaves (300ms)
   - Pulso para elementos críticos
   - Fade in/out para alertas

3. **Estados de Carga**
   - Indicadores claros
   - Mensajes informativos
   - Cancelación disponible

### Navegación

- **Tab Bar**: Acceso rápido a secciones principales
- **Botones de Acción**: Prominentes y accesibles
- **Breadcrumbs**: Para navegación profunda
- **Botón de Pánico**: Siempre visible

## Modo de Emergencia Extrema

### Características

- **Activación**: Automática en alerta EMERGENCY o manual (3 toques)
- **UI**: Ultra-simplificada, texto grande, colores de alto contraste
- **Acciones**: Solo las más críticas (llamar 133, rutas de evacuación)
- **Efectos**: Vibración intensa, pulso visual, sonidos de alerta

### Layout de Emergencia

```typescript
Botones: 80px altura mínima
Espaciado: 32px entre elementos
Texto: 48px+ para títulos, 30px+ para instrucciones
Colores: Rojo de emergencia con texto blanco
```

## Accesibilidad

### Cumplimiento WCAG AA

- **Contraste**: Mínimo 4.5:1 para texto normal, 3:1 para texto grande
- **Teclado**: Navegación completa sin mouse
- **Lectores de Pantalla**: Labels descriptivos, roles semánticos
- **Escalabilidad**: Hasta 200% sin pérdida de funcionalidad

### Características Inclusivas

- **Dislexia**: Fuentes legibles, espaciado amplio
- **Baja Visión**: Alto contraste, texto escalable
- **Discapacidad Motriz**: Objetivos de toque grandes
- **Discapacidad Auditiva**: Alertas visuales y vibratorias
- **Conectividad Limitada**: Modo offline, datos ligeros

## Responsive Design

### Breakpoints

```typescript
sm: 640px   // Teléfonos grandes
md: 768px   // Tablets pequeñas
lg: 1024px  // Tablets grandes
xl: 1280px  // Desktop
```

### Adaptaciones

- **Móvil**: Layout vertical, botones grandes
- **Tablet**: Layout híbrido, aprovecha espacio horizontal
- **Desktop**: Sidebar, múltiples columnas

## Modo Offline

### Estrategia

1. **Datos Críticos**: Pre-cargados y almacenados localmente
2. **Mapas**: Tiles offline para área de Pucón/Villarrica
3. **Guías**: Contenido estático disponible sin conexión
4. **Alertas**: Sistema de cola para sincronización posterior

### Indicadores

- **Estado de Conexión**: Visible y claro
- **Datos Disponibles**: Lista de contenido offline
- **Última Sincronización**: Timestamp visible

## Testing y Validación

### Pruebas de Accesibilidad

- **Lectores de Pantalla**: VoiceOver (iOS), TalkBack (Android)
- **Navegación por Teclado**: Tab order, focus management
- **Contraste**: Herramientas automáticas de validación
- **Escalabilidad**: Pruebas con diferentes tamaños de texto

### Pruebas de Usabilidad

- **Usuarios Reales**: Comunidades locales, turistas
- **Escenarios de Estrés**: Simulación de emergencias
- **Dispositivos Variados**: Gama baja, media, alta
- **Conectividad**: 2G, 3G, WiFi lento, offline

## Implementación

### Estructura de Archivos

```
constants/
  ├── Colors.ts      // Sistema de colores
  ├── Typography.ts  // Tipografía y escalas
  └── Layout.ts      // Espaciado y patrones

components/
  ├── ui/
  │   ├── AccessibleText.tsx
  │   └── AccessibleButton.tsx
  ├── VolcanoStatus.tsx
  ├── AlertSystem.tsx
  └── EmergencyMode.tsx
```

### Uso de Componentes

```typescript
// Texto accesible con escalabilidad
<AccessibleText variant="h1" scale="large" color="primary">
  Título Principal
</AccessibleText>

// Botón con feedback háptico
<PrimaryButton 
  size="large" 
  onPress={handleAction}
  hapticFeedback={true}
>
  Acción Principal
</PrimaryButton>

// Estado del volcán
<VolcanoStatus
  alertLevel="ADVISORY"
  volcanoName="Volcán Villarrica"
  lastUpdate={new Date()}
/>
```

## Próximos Pasos

1. **Implementar pantalla de Mapa** con rutas de evacuación
2. **Crear sistema de Guías** accesibles
3. **Integrar APIs oficiales** (SERNAGEOMIN, ONEMI)
4. **Desarrollar modo offline** completo
5. **Pruebas de usuario** con comunidades locales
6. **Optimización de rendimiento** para dispositivos de gama baja
