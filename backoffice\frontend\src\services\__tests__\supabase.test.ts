/**
 * 🌋 Volcano App Frontend - Tests para servicios de Supabase
 * Tests de integración para los servicios de Supabase
 */

import { vi } from 'vitest';

// Mock para Supabase
const mockSupabaseClient = {
  from: vi.fn(),
  auth: {
    signInWithPassword: vi.fn(),
    signOut: vi.fn(),
    getUser: vi.fn(),
    onAuthStateChange: vi.fn()
  },
  channel: vi.fn(),
  removeChannel: vi.fn()
};

vi.mock('@supabase/supabase-js', () => ({
  createClient: vi.fn(() => mockSupabaseClient)
}));

// Importar servicios después del mock
import { supabaseService } from '../supabase';

describe('Supabase Service', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Zones Service', () => {
    it('fetches zones correctly', async () => {
      const mockZones = [
        {
          id: '1',
          name: '<PERSON><PERSON> Segura',
          zone_type: 'SAFE',
          is_active: true
        }
      ];

      mockSupabaseClient.from.mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            order: vi.fn().mockResolvedValue({
              data: mockZones,
              error: null
            })
          })
        })
      });

      const result = await supabaseService.zones.getAll();

      expect(mockSupabaseClient.from).toHaveBeenCalledWith('safety_zones');
      expect(result.data).toEqual(mockZones);
      expect(result.error).toBeNull();
    });

    it('handles zone fetch error', async () => {
      const mockError = new Error('Database error');

      mockSupabaseClient.from.mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            order: vi.fn().mockResolvedValue({
              data: null,
              error: mockError
            })
          })
        })
      });

      const result = await supabaseService.zones.getAll();

      expect(result.data).toBeNull();
      expect(result.error).toEqual(mockError);
    });

    it('creates zone correctly', async () => {
      const newZone = {
        name: 'Nueva Zona',
        description: 'Descripción',
        zone_type: 'SAFE',
        geometry: {
          type: 'Polygon',
          coordinates: [[[-71.9048, -39.2904]]]
        }
      };

      const createdZone = { id: '1', ...newZone };

      mockSupabaseClient.from.mockReturnValue({
        insert: vi.fn().mockReturnValue({
          select: vi.fn().mockResolvedValue({
            data: [createdZone],
            error: null
          })
        })
      });

      const result = await supabaseService.zones.create(newZone);

      expect(mockSupabaseClient.from).toHaveBeenCalledWith('safety_zones');
      expect(result.data).toEqual([createdZone]);
      expect(result.error).toBeNull();
    });

    it('updates zone correctly', async () => {
      const zoneId = '1';
      const updates = {
        name: 'Zona Actualizada',
        description: 'Nueva descripción'
      };

      const updatedZone = { id: zoneId, ...updates };

      mockSupabaseClient.from.mockReturnValue({
        update: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            select: vi.fn().mockResolvedValue({
              data: [updatedZone],
              error: null
            })
          })
        })
      });

      const result = await supabaseService.zones.update(zoneId, updates);

      expect(mockSupabaseClient.from).toHaveBeenCalledWith('safety_zones');
      expect(result.data).toEqual([updatedZone]);
      expect(result.error).toBeNull();
    });

    it('deletes zone correctly', async () => {
      const zoneId = '1';

      mockSupabaseClient.from.mockReturnValue({
        delete: vi.fn().mockReturnValue({
          eq: vi.fn().mockResolvedValue({
            data: null,
            error: null
          })
        })
      });

      const result = await supabaseService.zones.delete(zoneId);

      expect(mockSupabaseClient.from).toHaveBeenCalledWith('safety_zones');
      expect(result.error).toBeNull();
    });
  });

  describe('Alerts Service', () => {
    it('fetches alerts correctly', async () => {
      const mockAlerts = [
        {
          id: '1',
          title: 'Alerta Volcánica',
          alert_level: 'YELLOW',
          is_active: true
        }
      ];

      mockSupabaseClient.from.mockReturnValue({
        select: vi.fn().mockReturnValue({
          order: vi.fn().mockResolvedValue({
            data: mockAlerts,
            error: null
          })
        })
      });

      const result = await supabaseService.alerts.getAll();

      expect(mockSupabaseClient.from).toHaveBeenCalledWith('volcano_alerts');
      expect(result.data).toEqual(mockAlerts);
      expect(result.error).toBeNull();
    });

    it('fetches current alert correctly', async () => {
      const mockAlert = {
        id: '1',
        title: 'Alerta Actual',
        alert_level: 'YELLOW',
        is_active: true
      };

      mockSupabaseClient.from.mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            order: vi.fn().mockReturnValue({
              limit: vi.fn().mockReturnValue({
                single: vi.fn().mockResolvedValue({
                  data: mockAlert,
                  error: null
                })
              })
            })
          })
        })
      });

      const result = await supabaseService.alerts.getCurrent();

      expect(result.data).toEqual(mockAlert);
      expect(result.error).toBeNull();
    });

    it('creates alert correctly', async () => {
      const newAlert = {
        title: 'Nueva Alerta',
        message: 'Mensaje de alerta',
        alert_level: 'YELLOW',
        volcano_name: 'Villarrica'
      };

      const createdAlert = { id: '1', ...newAlert };

      mockSupabaseClient.from.mockReturnValue({
        insert: vi.fn().mockReturnValue({
          select: vi.fn().mockResolvedValue({
            data: [createdAlert],
            error: null
          })
        })
      });

      const result = await supabaseService.alerts.create(newAlert);

      expect(result.data).toEqual([createdAlert]);
      expect(result.error).toBeNull();
    });
  });

  describe('Real-time Subscriptions', () => {
    it('subscribes to zone changes correctly', () => {
      const mockChannel = {
        on: vi.fn().mockReturnValue({
          subscribe: vi.fn()
        }),
        unsubscribe: vi.fn()
      };

      mockSupabaseClient.channel.mockReturnValue(mockChannel);

      const callback = vi.fn();
      supabaseService.subscriptions.subscribeToZones(callback);

      expect(mockSupabaseClient.channel).toHaveBeenCalledWith('zones-changes');
      expect(mockChannel.on).toHaveBeenCalledWith(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'safety_zones'
        },
        callback
      );
    });

    it('subscribes to alert changes correctly', () => {
      const mockChannel = {
        on: vi.fn().mockReturnValue({
          subscribe: vi.fn()
        }),
        unsubscribe: vi.fn()
      };

      mockSupabaseClient.channel.mockReturnValue(mockChannel);

      const callback = vi.fn();
      supabaseService.subscriptions.subscribeToAlerts(callback);

      expect(mockSupabaseClient.channel).toHaveBeenCalledWith('alerts-changes');
      expect(mockChannel.on).toHaveBeenCalledWith(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'volcano_alerts'
        },
        callback
      );
    });

    it('unsubscribes from channels correctly', () => {
      const mockChannel = {
        unsubscribe: vi.fn()
      };

      mockSupabaseClient.channel.mockReturnValue(mockChannel);

      supabaseService.subscriptions.unsubscribeAll();

      expect(mockSupabaseClient.removeChannel).toHaveBeenCalled();
    });
  });

  describe('Authentication Service', () => {
    it('signs in user correctly', async () => {
      const credentials = {
        email: '<EMAIL>',
        password: 'password123'
      };

      const mockUser = {
        id: '1',
        email: '<EMAIL>'
      };

      mockSupabaseClient.auth.signInWithPassword.mockResolvedValue({
        data: { user: mockUser },
        error: null
      });

      const result = await supabaseService.auth.signIn(credentials);

      expect(mockSupabaseClient.auth.signInWithPassword).toHaveBeenCalledWith(credentials);
      expect(result.data.user).toEqual(mockUser);
      expect(result.error).toBeNull();
    });

    it('handles sign in error', async () => {
      const credentials = {
        email: '<EMAIL>',
        password: 'wrongpassword'
      };

      const mockError = new Error('Invalid credentials');

      mockSupabaseClient.auth.signInWithPassword.mockResolvedValue({
        data: { user: null },
        error: mockError
      });

      const result = await supabaseService.auth.signIn(credentials);

      expect(result.data.user).toBeNull();
      expect(result.error).toEqual(mockError);
    });

    it('signs out user correctly', async () => {
      mockSupabaseClient.auth.signOut.mockResolvedValue({
        error: null
      });

      const result = await supabaseService.auth.signOut();

      expect(mockSupabaseClient.auth.signOut).toHaveBeenCalled();
      expect(result.error).toBeNull();
    });

    it('gets current user correctly', async () => {
      const mockUser = {
        id: '1',
        email: '<EMAIL>'
      };

      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null
      });

      const result = await supabaseService.auth.getCurrentUser();

      expect(result.data.user).toEqual(mockUser);
      expect(result.error).toBeNull();
    });
  });

  describe('Error Handling', () => {
    it('handles network errors gracefully', async () => {
      mockSupabaseClient.from.mockReturnValue({
        select: vi.fn().mockRejectedValue(new Error('Network error'))
      });

      try {
        await supabaseService.zones.getAll();
      } catch (error) {
        expect(error.message).toBe('Network error');
      }
    });

    it('handles malformed responses', async () => {
      mockSupabaseClient.from.mockReturnValue({
        select: vi.fn().mockResolvedValue({
          data: undefined,
          error: null
        })
      });

      const result = await supabaseService.zones.getAll();

      expect(result.data).toBeUndefined();
      expect(result.error).toBeNull();
    });
  });
});
