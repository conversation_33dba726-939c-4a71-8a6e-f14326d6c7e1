/**
 * 🌋 Volcano App Backend - Rutas de Configuración
 * Endpoints para gestión de configuración del sistema
 */

import { Router } from 'express';
import { param, body } from 'express-validator';
import { requireMinimumRole } from '@/middleware/auth';
import { handleValidationErrors } from '@/middleware/validation';
import { asyncHandler } from '@/middleware/errorHandler';
import { UserRole } from '@/types';

// Importar controladores
import {
  getAllConfigs,
  getConfigByKey,
  updateConfig,
  deleteConfig,
  getPublicConfigs,
  resetConfigs
} from '@/controllers/config';

// =====================================================
// ROUTER DE CONFIGURACIÓN
// =====================================================

const router = Router();

// =====================================================
// VALIDACIONES ESPECÍFICAS
// =====================================================

const validateConfigKey = [
  param('key')
    .notEmpty()
    .withMessage('Config key is required')
    .isLength({ min: 2, max: 100 })
    .withMessage('Config key must be between 2 and 100 characters')
    .matches(/^[a-zA-Z0-9_.-]+$/)
    .withMessage('Config key can only contain letters, numbers, underscores, dots, and hyphens'),
  handleValidationErrors()
];

const validateConfigUpdate = [
  param('key')
    .notEmpty()
    .withMessage('Config key is required')
    .isLength({ min: 2, max: 100 })
    .withMessage('Config key must be between 2 and 100 characters')
    .matches(/^[a-zA-Z0-9_.-]+$/)
    .withMessage('Config key can only contain letters, numbers, underscores, dots, and hyphens'),
  body('value')
    .notEmpty()
    .withMessage('Config value is required'),
  body('description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Description must not exceed 500 characters'),
  handleValidationErrors()
];

// =====================================================
// RUTAS PÚBLICAS
// =====================================================

/**
 * Obtener configuraciones públicas (sin autenticación)
 * GET /config/public
 */
router.get('/public', 
  asyncHandler(getPublicConfigs)
);

// =====================================================
// RUTAS DE CONSULTA (REQUIEREN AUTENTICACIÓN)
// =====================================================

/**
 * Obtener todas las configuraciones
 * GET /config
 * Requiere rol mínimo: OPERATOR
 */
router.get('/', 
  requireMinimumRole(UserRole.OPERATOR),
  asyncHandler(getAllConfigs)
);

/**
 * Obtener configuración por clave
 * GET /config/:key
 * Requiere rol mínimo: OPERATOR
 */
router.get('/:key', 
  requireMinimumRole(UserRole.OPERATOR),
  validateConfigKey,
  asyncHandler(getConfigByKey)
);

// =====================================================
// RUTAS DE MODIFICACIÓN (REQUIEREN PERMISOS ADMIN)
// =====================================================

/**
 * Actualizar o crear configuración
 * PUT /config/:key
 * Requiere rol mínimo: ADMIN
 */
router.put('/:key', 
  requireMinimumRole(UserRole.ADMIN),
  validateConfigUpdate,
  asyncHandler(updateConfig)
);

/**
 * Eliminar configuración
 * DELETE /config/:key
 * Requiere rol mínimo: ADMIN
 */
router.delete('/:key', 
  requireMinimumRole(UserRole.ADMIN),
  validateConfigKey,
  asyncHandler(deleteConfig)
);

/**
 * Restablecer configuraciones a valores por defecto
 * POST /config/reset
 * Requiere rol mínimo: ADMIN
 */
router.post('/reset', 
  requireMinimumRole(UserRole.ADMIN),
  asyncHandler(resetConfigs)
);

// =====================================================
// RUTAS DE INFORMACIÓN
// =====================================================

/**
 * Información sobre configuración
 * GET /config/_info
 */
router.get('/_info',
  requireMinimumRole(UserRole.OPERATOR),
  (req, res) => {
    res.json({
      success: true,
      data: {
        endpoints: {
          list: 'GET /api/config',
          get: 'GET /api/config/:key',
          update: 'PUT /api/config/:key',
          delete: 'DELETE /api/config/:key',
          public: 'GET /api/config/public',
          reset: 'POST /api/config/reset'
        },
        permissions: {
          view: 'OPERATOR or higher',
          modify: 'ADMIN only',
          public: 'No authentication required'
        },
        public_keys: [
          'volcano_coordinates',
          'emergency_contacts',
          'mobile_app_version',
          'system_status',
          'maintenance_mode'
        ],
        default_configs: {
          volcano_coordinates: {
            lat: -39.420000,
            lng: -71.939167,
            name: 'Volcán Villarrica'
          },
          emergency_contacts: {
            emergency: '133',
            police: '133',
            fire: '132',
            medical: '131'
          },
          mobile_app_version: '1.0.0',
          system_status: 'operational',
          maintenance_mode: false
        },
        key_format: {
          allowed_characters: 'a-z, A-Z, 0-9, _, ., -',
          min_length: 2,
          max_length: 100
        },
        value_types: [
          'string',
          'number',
          'boolean',
          'object',
          'array'
        ]
      },
      timestamp: new Date()
    });
  }
);

// =====================================================
// EXPORTACIÓN
// =====================================================

export default router;
