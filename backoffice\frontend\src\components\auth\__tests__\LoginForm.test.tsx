/**
 * 🌋 Volcano App Frontend - Tests para LoginForm
 * Tests unitarios para el formulario de inicio de sesión
 */

import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi } from 'vitest';
import { LoginForm } from '../LoginForm';

// Mock para react-hot-toast
vi.mock('react-hot-toast', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn()
  }
}));

// Mock para fetch
const mockFetch = vi.fn();
global.fetch = mockFetch;

const mockProps = {
  onLogin: vi.fn()
};

describe('LoginForm', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockFetch.mockClear();
  });

  it('renders login form correctly', () => {
    render(<LoginForm {...mockProps} />);
    
    expect(screen.getByText('Volcano App Admin')).toBeInTheDocument();
    expect(screen.getByText('Ingresa tus credenciales para acceder al panel administrativo')).toBeInTheDocument();
    expect(screen.getByLabelText('Email')).toBeInTheDocument();
    expect(screen.getByLabelText('Contraseña')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Iniciar Sesión' })).toBeInTheDocument();
  });

  it('displays volcano emoji icon', () => {
    render(<LoginForm {...mockProps} />);
    
    expect(screen.getByText('🌋')).toBeInTheDocument();
  });

  it('validates required fields', async () => {
    const user = userEvent.setup();
    render(<LoginForm {...mockProps} />);
    
    const submitButton = screen.getByRole('button', { name: 'Iniciar Sesión' });
    await user.click(submitButton);
    
    await waitFor(() => {
      expect(screen.getByText('El email es requerido')).toBeInTheDocument();
      expect(screen.getByText('La contraseña es requerida')).toBeInTheDocument();
    });
  });

  it('validates email format', async () => {
    const user = userEvent.setup();
    render(<LoginForm {...mockProps} />);
    
    const emailInput = screen.getByLabelText('Email');
    await user.type(emailInput, 'invalid-email');
    
    // Trigger validation
    await user.tab();
    
    await waitFor(() => {
      expect(screen.getByText('Email inválido')).toBeInTheDocument();
    });
  });

  it('handles successful login', async () => {
    const user = userEvent.setup();
    const mockTokens = {
      access_token: 'mock-access-token',
      refresh_token: 'mock-refresh-token'
    };
    
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: () => Promise.resolve({
        success: true,
        data: { tokens: mockTokens }
      })
    });
    
    render(<LoginForm {...mockProps} />);
    
    // Llenar formulario
    await user.type(screen.getByLabelText('Email'), '<EMAIL>');
    await user.type(screen.getByLabelText('Contraseña'), 'password123');
    
    // Enviar formulario
    const submitButton = screen.getByRole('button', { name: 'Iniciar Sesión' });
    await user.click(submitButton);
    
    await waitFor(() => {
      expect(mockProps.onLogin).toHaveBeenCalledWith(mockTokens);
    });
  });

  it('handles login error', async () => {
    const user = userEvent.setup();
    
    mockFetch.mockResolvedValueOnce({
      ok: false,
      json: () => Promise.resolve({
        success: false,
        error: 'Credenciales inválidas'
      })
    });
    
    render(<LoginForm {...mockProps} />);
    
    // Llenar formulario
    await user.type(screen.getByLabelText('Email'), '<EMAIL>');
    await user.type(screen.getByLabelText('Contraseña'), 'wrongpassword');
    
    // Enviar formulario
    const submitButton = screen.getByRole('button', { name: 'Iniciar Sesión' });
    await user.click(submitButton);
    
    await waitFor(() => {
      expect(screen.getByText('Credenciales inválidas')).toBeInTheDocument();
    });
  });

  it('handles network error', async () => {
    const user = userEvent.setup();
    
    mockFetch.mockRejectedValueOnce(new Error('Network error'));
    
    render(<LoginForm {...mockProps} />);
    
    // Llenar formulario
    await user.type(screen.getByLabelText('Email'), '<EMAIL>');
    await user.type(screen.getByLabelText('Contraseña'), 'password123');
    
    // Enviar formulario
    const submitButton = screen.getByRole('button', { name: 'Iniciar Sesión' });
    await user.click(submitButton);
    
    await waitFor(() => {
      expect(screen.getByText('Error de conexión')).toBeInTheDocument();
    });
  });

  it('shows loading state during submission', async () => {
    const user = userEvent.setup();
    
    // Mock para simular respuesta lenta
    mockFetch.mockImplementationOnce(() => 
      new Promise(resolve => setTimeout(() => resolve({
        ok: true,
        json: () => Promise.resolve({ success: true, data: { tokens: {} } })
      }), 100))
    );
    
    render(<LoginForm {...mockProps} />);
    
    // Llenar formulario
    await user.type(screen.getByLabelText('Email'), '<EMAIL>');
    await user.type(screen.getByLabelText('Contraseña'), 'password123');
    
    // Enviar formulario
    const submitButton = screen.getByRole('button', { name: 'Iniciar Sesión' });
    await user.click(submitButton);
    
    // Verificar estado de carga
    expect(screen.getByText('Iniciando sesión...')).toBeInTheDocument();
    expect(submitButton).toBeDisabled();
  });

  it('disables form during submission', async () => {
    const user = userEvent.setup();
    
    mockFetch.mockImplementationOnce(() => 
      new Promise(resolve => setTimeout(() => resolve({
        ok: true,
        json: () => Promise.resolve({ success: true, data: { tokens: {} } })
      }), 100))
    );
    
    render(<LoginForm {...mockProps} />);
    
    // Llenar formulario
    await user.type(screen.getByLabelText('Email'), '<EMAIL>');
    await user.type(screen.getByLabelText('Contraseña'), 'password123');
    
    // Enviar formulario
    const submitButton = screen.getByRole('button', { name: 'Iniciar Sesión' });
    await user.click(submitButton);
    
    // Verificar que los campos están deshabilitados
    expect(screen.getByLabelText('Email')).toBeDisabled();
    expect(screen.getByLabelText('Contraseña')).toBeDisabled();
    expect(submitButton).toBeDisabled();
  });

  it('sends correct request data', async () => {
    const user = userEvent.setup();
    
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: () => Promise.resolve({
        success: true,
        data: { tokens: {} }
      })
    });
    
    render(<LoginForm {...mockProps} />);
    
    // Llenar formulario
    await user.type(screen.getByLabelText('Email'), '<EMAIL>');
    await user.type(screen.getByLabelText('Contraseña'), 'password123');
    
    // Enviar formulario
    const submitButton = screen.getByRole('button', { name: 'Iniciar Sesión' });
    await user.click(submitButton);
    
    await waitFor(() => {
      expect(mockFetch).toHaveBeenCalledWith('/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'password123'
        })
      });
    });
  });

  it('handles password visibility toggle', async () => {
    const user = userEvent.setup();
    render(<LoginForm {...mockProps} />);
    
    const passwordInput = screen.getByLabelText('Contraseña');
    const toggleButton = screen.getByRole('button', { name: /mostrar contraseña/i });
    
    // Inicialmente debe ser tipo password
    expect(passwordInput).toHaveAttribute('type', 'password');
    
    // Hacer clic en toggle
    await user.click(toggleButton);
    expect(passwordInput).toHaveAttribute('type', 'text');
    
    // Hacer clic nuevamente
    await user.click(toggleButton);
    expect(passwordInput).toHaveAttribute('type', 'password');
  });

  it('handles form submission with Enter key', async () => {
    const user = userEvent.setup();
    
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: () => Promise.resolve({
        success: true,
        data: { tokens: {} }
      })
    });
    
    render(<LoginForm {...mockProps} />);
    
    // Llenar formulario
    await user.type(screen.getByLabelText('Email'), '<EMAIL>');
    await user.type(screen.getByLabelText('Contraseña'), 'password123');
    
    // Presionar Enter
    await user.keyboard('{Enter}');
    
    await waitFor(() => {
      expect(mockFetch).toHaveBeenCalled();
    });
  });

  it('shows placeholder text correctly', () => {
    render(<LoginForm {...mockProps} />);
    
    expect(screen.getByPlaceholderText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('••••••••')).toBeInTheDocument();
  });

  it('has proper accessibility attributes', () => {
    render(<LoginForm {...mockProps} />);
    
    const emailInput = screen.getByLabelText('Email');
    const passwordInput = screen.getByLabelText('Contraseña');
    const submitButton = screen.getByRole('button', { name: 'Iniciar Sesión' });
    
    expect(emailInput).toHaveAttribute('type', 'email');
    expect(emailInput).toHaveAttribute('required');
    expect(passwordInput).toHaveAttribute('type', 'password');
    expect(passwordInput).toHaveAttribute('required');
    expect(submitButton).toHaveAttribute('type', 'submit');
  });

  it('clears error messages when user starts typing', async () => {
    const user = userEvent.setup();
    
    mockFetch.mockResolvedValueOnce({
      ok: false,
      json: () => Promise.resolve({
        success: false,
        error: 'Credenciales inválidas'
      })
    });
    
    render(<LoginForm {...mockProps} />);
    
    // Enviar formulario con datos incorrectos
    await user.type(screen.getByLabelText('Email'), '<EMAIL>');
    await user.type(screen.getByLabelText('Contraseña'), 'wrongpassword');
    await user.click(screen.getByRole('button', { name: 'Iniciar Sesión' }));
    
    await waitFor(() => {
      expect(screen.getByText('Credenciales inválidas')).toBeInTheDocument();
    });
    
    // Empezar a escribir nuevamente
    await user.type(screen.getByLabelText('Email'), 'a');
    
    // El error debería desaparecer
    expect(screen.queryByText('Credenciales inválidas')).not.toBeInTheDocument();
  });
});
