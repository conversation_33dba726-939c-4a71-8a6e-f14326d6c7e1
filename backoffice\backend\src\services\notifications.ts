/**
 * 🌋 Volcano App Backend - Servicio de Notificaciones con Supabase Realtime
 * Gestión de notificaciones en tiempo real usando Supabase Realtime + notificaciones locales
 */

import { supabaseAdmin } from '@/services/supabase';
import { logger } from '@/utils/logger';

// =====================================================
// TIPOS Y INTERFACES
// =====================================================

export interface PushNotificationPayload {
  title: string;
  body: string;
  data?: Record<string, any>;
  sound?: boolean;
  vibration?: boolean;
  priority?: 'normal' | 'high';
  badge?: number;
}

export interface NotificationTarget {
  type: 'all' | 'zone' | 'location' | 'device';
  criteria?: {
    zoneId?: string;
    latitude?: number;
    longitude?: number;
    radius?: number; // en metros
    deviceIds?: string[];
  };
}

export interface NotificationResult {
  success: boolean;
  sent_count: number;
  failed_count: number;
  errors?: string[];
  notification_id?: string;
}

// =====================================================
// CONFIGURACIÓN DE NOTIFICACIONES
// =====================================================

const NOTIFICATION_CONFIG = {
  // Configuración para diferentes tipos de alertas
  ALERT_CONFIGS: {
    EMERGENCY: {
      sound: true,
      vibration: true,
      priority: 'high' as const,
      badge: 1,
      ttl: 3600, // 1 hora
    },
    WARNING: {
      sound: true,
      vibration: true,
      priority: 'high' as const,
      badge: 1,
      ttl: 7200, // 2 horas
    },
    WATCH: {
      sound: true,
      vibration: false,
      priority: 'normal' as const,
      badge: 1,
      ttl: 14400, // 4 horas
    },
    ADVISORY: {
      sound: false,
      vibration: false,
      priority: 'normal' as const,
      badge: 1,
      ttl: 28800, // 8 horas
    },
    NORMAL: {
      sound: false,
      vibration: false,
      priority: 'normal' as const,
      badge: 0,
      ttl: 86400, // 24 horas
    }
  },
  
  // Configuración de vibración por tipo de alerta
  VIBRATION_PATTERNS: {
    EMERGENCY: [0, 1000, 500, 1000, 500, 1000], // Patrón de emergencia
    WARNING: [0, 500, 200, 500, 200, 500],      // Patrón de advertencia
    WATCH: [0, 300, 100, 300],                  // Patrón de vigilancia
    ADVISORY: [0, 200],                         // Vibración simple
    NORMAL: []                                  // Sin vibración
  }
};

// =====================================================
// SERVICIO DE NOTIFICACIONES
// =====================================================

class NotificationService {
  private isInitialized = false;

  /**
   * Inicializar el servicio de notificaciones
   */
  async initialize(): Promise<void> {
    try {
      logger.info('Initializing notification service...');
      
      // Aquí se inicializarían servicios como Firebase, OneSignal, etc.
      // Por ahora, simulamos la inicialización
      
      this.isInitialized = true;
      logger.info('✅ Notification service initialized');
    } catch (error) {
      logger.error('❌ Failed to initialize notification service:', error);
      throw error;
    }
  }

  /**
   * Enviar notificación de alerta volcánica usando Supabase Realtime
   */
  async sendVolcanoAlert(
    alert: any,
    target: NotificationTarget = { type: 'all' }
  ): Promise<NotificationResult> {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      const alertConfig = NOTIFICATION_CONFIG.ALERT_CONFIGS[alert.alert_level as keyof typeof NOTIFICATION_CONFIG.ALERT_CONFIGS]
        || NOTIFICATION_CONFIG.ALERT_CONFIGS.NORMAL;

      const notificationData = {
        type: 'volcano_alert',
        alertId: alert.id,
        alertLevel: alert.alert_level,
        volcanoName: alert.volcano_name,
        volcanoLat: alert.volcano_lat,
        volcanoLng: alert.volcano_lng,
        title: `🌋 Alerta Volcánica: ${alert.alert_level}`,
        message: alert.message || `${alert.volcano_name} - ${alert.title}`,
        timestamp: new Date().toISOString(),
        vibrationPattern: NOTIFICATION_CONFIG.VIBRATION_PATTERNS[alert.alert_level as keyof typeof NOTIFICATION_CONFIG.VIBRATION_PATTERNS] || [],
        sound: alertConfig.sound,
        vibration: alertConfig.vibration,
        priority: alertConfig.priority
      };

      // Insertar notificación en tabla para que Supabase Realtime la propague
      const { data: notification, error } = await supabaseAdmin
        .from('realtime_notifications')
        .insert({
          type: 'volcano_alert',
          title: notificationData.title,
          message: notificationData.message,
          data: notificationData,
          target_type: target.type,
          target_criteria: target.criteria,
          created_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) {
        throw error;
      }

      // Registrar en logs de auditoría
      const logPayload: PushNotificationPayload = {
        title: notificationData.title,
        body: notificationData.message,
        data: notificationData,
        sound: notificationData.sound,
        vibration: notificationData.vibration,
        priority: notificationData.priority
      };

      await this.logNotification(alert.id, logPayload, target, {
        success: true,
        sent_count: 1, // Supabase Realtime se encarga de la distribución
        failed_count: 0,
        notification_id: notification.id
      });

      logger.info(`Volcano alert notification sent via Supabase Realtime: ${alert.id}`);

      return {
        success: true,
        sent_count: 1,
        failed_count: 0,
        notification_id: notification.id
      };

    } catch (error) {
      logger.error('Error sending volcano alert notification:', error);
      return {
        success: false,
        sent_count: 0,
        failed_count: 1,
        errors: [error instanceof Error ? error.message : 'Unknown error']
      };
    }
  }

  /**
   * Enviar notificación de actualización de zona
   */
  async sendZoneUpdate(
    zone: any,
    action: 'created' | 'updated' | 'deleted',
    target: NotificationTarget = { type: 'zone', criteria: { zoneId: zone.id } }
  ): Promise<NotificationResult> {
    try {
      const actionText = {
        created: 'creada',
        updated: 'actualizada',
        deleted: 'eliminada'
      };

      const payload: PushNotificationPayload = {
        title: `🗺️ Zona de Seguridad ${actionText[action]}`,
        body: `La zona "${zone.name}" ha sido ${actionText[action]}`,
        data: {
          type: 'zone_update',
          zoneId: zone.id,
          zoneName: zone.name,
          zoneType: zone.zone_type,
          action: action,
          timestamp: new Date().toISOString()
        },
        sound: false,
        vibration: false,
        priority: 'normal',
        badge: 0
      };

      const targetDevices = await this.getTargetDevices(target);
      const result = await this.sendPushNotifications(payload, targetDevices);

      await this.logNotification(zone.id, payload, target, result);

      return result;

    } catch (error) {
      logger.error('Error sending zone update notification:', error);
      return {
        success: false,
        sent_count: 0,
        failed_count: 1,
        errors: [error instanceof Error ? error.message : 'Unknown error']
      };
    }
  }

  /**
   * Obtener dispositivos objetivo basado en criterios
   */
  private async getTargetDevices(target: NotificationTarget): Promise<string[]> {
    try {
      let query = supabaseAdmin
        .from('app_user_locations')
        .select('device_id')
        .not('device_id', 'is', null);

      switch (target.type) {
        case 'all':
          // Todos los dispositivos activos
          break;

        case 'zone':
          if (target.criteria?.zoneId) {
            // Dispositivos en una zona específica
            // Esto requeriría una consulta geoespacial más compleja
            logger.warn('Zone-based targeting not fully implemented');
          }
          break;

        case 'location':
          if (target.criteria?.latitude && target.criteria?.longitude && target.criteria?.radius) {
            // Dispositivos en un radio específico
            // Esto requeriría una consulta geoespacial
            logger.warn('Location-based targeting not fully implemented');
          }
          break;

        case 'device':
          if (target.criteria?.deviceIds) {
            query = query.in('device_id', target.criteria.deviceIds);
          }
          break;
      }

      const { data: devices, error } = await query;

      if (error) {
        throw error;
      }

      return devices?.map(d => d.device_id).filter(Boolean) || [];

    } catch (error) {
      logger.error('Error getting target devices:', error);
      return [];
    }
  }

  /**
   * Enviar notificaciones push a dispositivos específicos
   */
  private async sendPushNotifications(
    payload: PushNotificationPayload,
    deviceIds: string[]
  ): Promise<NotificationResult> {
    try {
      // Aquí se implementaría la lógica real de envío
      // Por ejemplo, con Firebase Cloud Messaging, OneSignal, etc.
      
      logger.info(`Simulating push notification to ${deviceIds.length} devices:`, {
        title: payload.title,
        body: payload.body,
        deviceCount: deviceIds.length
      });

      // Simulación de envío exitoso
      return {
        success: true,
        sent_count: deviceIds.length,
        failed_count: 0,
        notification_id: `notif_${Date.now()}`
      };

    } catch (error) {
      logger.error('Error sending push notifications:', error);
      return {
        success: false,
        sent_count: 0,
        failed_count: deviceIds.length,
        errors: [error instanceof Error ? error.message : 'Unknown error']
      };
    }
  }

  /**
   * Registrar notificación en base de datos para auditoría
   */
  private async logNotification(
    entityId: string,
    payload: PushNotificationPayload,
    target: NotificationTarget,
    result: NotificationResult
  ): Promise<void> {
    try {
      await supabaseAdmin
        .from('notification_logs')
        .insert({
          entity_id: entityId,
          notification_type: payload.data?.type || 'unknown',
          title: payload.title,
          body: payload.body,
          target_type: target.type,
          target_criteria: target.criteria,
          sent_count: result.sent_count,
          failed_count: result.failed_count,
          success: result.success,
          notification_id: result.notification_id,
          created_at: new Date().toISOString()
        });
    } catch (error) {
      logger.error('Error logging notification:', error);
      // No lanzar error aquí para no afectar el flujo principal
    }
  }
}

// =====================================================
// EXPORTACIONES
// =====================================================

export const notificationService = new NotificationService();

export default notificationService;
