/**
 * 🌋 Volcano App Backend - Configuración de Variables de Entorno
 * Configuración centralizada y tipada de variables de entorno
 */

import dotenv from 'dotenv';

// Cargar variables de entorno
dotenv.config();

// =====================================================
// CONFIGURACIÓN DEL SERVIDOR
// =====================================================

export const SERVER_CONFIG = {
  PORT: parseInt(process.env['PORT'] || '3001'), // Puerto unificado a 3001
  HOST: process.env['HOST'] || 'localhost', // Usar localhost por defecto
  NODE_ENV: process.env['NODE_ENV'] || 'development'
} as const;

// =====================================================
// CONFIGURACIÓN DE BASE DE DATOS
// =====================================================

export const DATABASE_CONFIG = {
  SUPABASE_URL: process.env['SUPABASE_URL'] || '',
  SUPABASE_ANON_KEY: process.env['SUPABASE_ANON_KEY'] || '',
  SUPABASE_SERVICE_ROLE_KEY: process.env['SUPABASE_SERVICE_ROLE_KEY'] || ''
} as const;

// =====================================================
// CONFIGURACIÓN DE JWT
// =====================================================

export const JWT_CONFIG = {
  JWT_SECRET: process.env['JWT_SECRET'] || 'default-secret-change-in-production',
  JWT_REFRESH_SECRET: process.env['JWT_REFRESH_SECRET'] || 'default-refresh-secret-change-in-production',
  JWT_EXPIRES_IN: process.env['JWT_EXPIRES_IN'] || '24h',
  JWT_REFRESH_EXPIRES_IN: process.env['JWT_REFRESH_EXPIRES_IN'] || '7d'
} as const;

// =====================================================
// CONFIGURACIÓN DE SEGURIDAD
// =====================================================

export const SECURITY_CONFIG = {
  BCRYPT_ROUNDS: parseInt(process.env['BCRYPT_ROUNDS'] || '12'),
  RATE_LIMIT_WINDOW_MS: parseInt(process.env['RATE_LIMIT_WINDOW_MS'] || '900000'),
  RATE_LIMIT_MAX_REQUESTS: parseInt(process.env['RATE_LIMIT_MAX_REQUESTS'] || '100')
} as const;

// =====================================================
// CONFIGURACIÓN DE CORS
// =====================================================

export const CORS_CONFIG = {
  CORS_ORIGIN: process.env['CORS_ORIGIN']?.split(',') || [
    'http://localhost:5173',  // Vite dev server
    'http://localhost:3000',  // React dev server
    'http://localhost:8081',  // Expo dev server
    'http://localhost:19006', // Expo web fallback
    'http://127.0.0.1:8081',  // Alternative localhost
    'http://************:8081', // Network IP for mobile testing
    'exp://************:8081', // Expo protocol
    'http://************:3002', // Direct backend access from mobile
  ],
  CORS_CREDENTIALS: process.env['CORS_CREDENTIALS'] === 'true'
} as const;

// =====================================================
// CONFIGURACIÓN DE LOGGING
// =====================================================

export const LOGGING_CONFIG = {
  LOG_LEVEL: process.env['LOG_LEVEL'] || 'info',
  LOG_FILE_PATH: process.env['LOG_FILE_PATH'] || './logs/app.log',
  LOG_MAX_SIZE: process.env['LOG_MAX_SIZE'] || '10m',
  LOG_MAX_FILES: parseInt(process.env['LOG_MAX_FILES'] || '5')
} as const;

// =====================================================
// CONFIGURACIÓN DE REDIS
// =====================================================

export const REDIS_CONFIG = {
  REDIS_HOST: process.env['REDIS_HOST'] || 'localhost',
  REDIS_PORT: parseInt(process.env['REDIS_PORT'] || '6379'),
  REDIS_PASSWORD: process.env['REDIS_PASSWORD'] || undefined,
  REDIS_DB: parseInt(process.env['REDIS_DB'] || '0'),
  REDIS_DISABLED: process.env['REDIS_DISABLED'] === 'true'
} as const;

// =====================================================
// CONFIGURACIÓN DE WEBSOCKET
// =====================================================

export const WEBSOCKET_CONFIG = {
  WEBSOCKET_ENABLED: process.env['WEBSOCKET_ENABLED'] !== 'false',
  WEBSOCKET_PING_TIMEOUT: parseInt(process.env['WEBSOCKET_PING_TIMEOUT'] || '60000'),
  WEBSOCKET_PING_INTERVAL: parseInt(process.env['WEBSOCKET_PING_INTERVAL'] || '25000'),
  WEBSOCKET_MAX_BUFFER_SIZE: parseInt(process.env['WEBSOCKET_MAX_BUFFER_SIZE'] || '1048576') // 1MB
} as const;

// =====================================================
// CONFIGURACIÓN DE APLICACIÓN
// =====================================================

export const APP_CONFIG = {
  APP_VERSION: process.env['npm_package_version'] || '1.0.0',
  SWAGGER_ENABLED: process.env['SWAGGER_ENABLED'] !== 'false',
  SENTRY_DSN: process.env['SENTRY_DSN'] || ''
} as const;

// =====================================================
// VALIDACIÓN DE CONFIGURACIÓN REQUERIDA
// =====================================================

export function validateRequiredConfig(): void {
  const requiredVars = [
    { key: 'SUPABASE_URL', value: DATABASE_CONFIG.SUPABASE_URL },
    { key: 'SUPABASE_ANON_KEY', value: DATABASE_CONFIG.SUPABASE_ANON_KEY },
    { key: 'JWT_SECRET', value: JWT_CONFIG.JWT_SECRET },
    { key: 'JWT_REFRESH_SECRET', value: JWT_CONFIG.JWT_REFRESH_SECRET }
  ];

  const missing = requiredVars.filter(({ value }) => !value || value === '');

  if (missing.length > 0) {
    const missingKeys = missing.map(({ key }) => key).join(', ');
    throw new Error(`Missing required environment variables: ${missingKeys}`);
  }

  // Validar que no se usen valores por defecto en producción
  if (SERVER_CONFIG.NODE_ENV === 'production') {
    const defaultSecrets = [
      { key: 'JWT_SECRET', value: JWT_CONFIG.JWT_SECRET, default: 'default-secret-change-in-production' },
      { key: 'JWT_REFRESH_SECRET', value: JWT_CONFIG.JWT_REFRESH_SECRET, default: 'default-refresh-secret-change-in-production' }
    ];

    const usingDefaults = defaultSecrets.filter(({ value, default: defaultValue }) => value === defaultValue);

    if (usingDefaults.length > 0) {
      const defaultKeys = usingDefaults.map(({ key }) => key).join(', ');
      throw new Error(`Cannot use default secrets in production: ${defaultKeys}`);
    }
  }
}

// =====================================================
// CONFIGURACIÓN CONSOLIDADA
// =====================================================

export const CONFIG = {
  SERVER: SERVER_CONFIG,
  DATABASE: DATABASE_CONFIG,
  JWT: JWT_CONFIG,
  SECURITY: SECURITY_CONFIG,
  CORS: CORS_CONFIG,
  LOGGING: LOGGING_CONFIG,
  REDIS: REDIS_CONFIG,
  WEBSOCKET: WEBSOCKET_CONFIG,
  APP: APP_CONFIG
} as const;

// =====================================================
// EXPORTACIONES POR DEFECTO
// =====================================================

export default CONFIG;
