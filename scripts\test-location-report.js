/**
 * 🌋 Volcano App - Script de Prueba para Reporte de Ubicación
 * Script para probar el endpoint de reporte de ubicación con datos válidos
 */

const axios = require('axios');

// Configuración
const API_BASE_URL = 'http://localhost:3002/api';
const TEST_DEVICE_ID = 'volcano_test_device_' + Date.now() + '_' + Math.random().toString(36).substring(2, 15);

// Datos de prueba válidos
const testLocationData = {
  anonymous_id: TEST_DEVICE_ID,
  latitude: -39.2706, // Pucón, Chile (válido: -90 a 90)
  longitude: -71.9728, // Pucón, Chile (válido: -180 a 180)
  accuracy: 10.5,
  timestamp: new Date().toISOString(),
  app_version: '1.0.0',
  device_type: 'test'
};

// Datos de prueba inválidos para verificar validación
const invalidTestCases = [
  {
    name: 'Sin anonymous_id',
    data: { ...testLocationData, anonymous_id: undefined }
  },
  {
    name: 'anonymous_id muy corto',
    data: { ...testLocationData, anonymous_id: 'short' }
  },
  {
    name: 'anonymous_id muy largo',
    data: { ...testLocationData, anonymous_id: 'a'.repeat(101) }
  },
  {
    name: 'Latitud inválida (mayor a 90)',
    data: { ...testLocationData, latitude: 95 }
  },
  {
    name: 'Latitud inválida (menor a -90)',
    data: { ...testLocationData, latitude: -95 }
  },
  {
    name: 'Longitud inválida (mayor a 180)',
    data: { ...testLocationData, longitude: 185 }
  },
  {
    name: 'Longitud inválida (menor a -180)',
    data: { ...testLocationData, longitude: -185 }
  }
];

async function testValidLocationReport() {
  console.log('🧪 Probando reporte de ubicación válido...');
  console.log('📍 Datos de prueba:', JSON.stringify(testLocationData, null, 2));
  
  try {
    const response = await axios.post(`${API_BASE_URL}/mobile/location/report`, testLocationData);
    console.log('✅ Reporte exitoso:', response.data);
    return true;
  } catch (error) {
    console.error('❌ Error en reporte válido:', error.response?.data || error.message);
    return false;
  }
}

async function testInvalidLocationReports() {
  console.log('\n🧪 Probando reportes de ubicación inválidos...');
  
  for (const testCase of invalidTestCases) {
    console.log(`\n📍 Probando: ${testCase.name}`);
    console.log('📍 Datos:', JSON.stringify(testCase.data, null, 2));
    
    try {
      const response = await axios.post(`${API_BASE_URL}/mobile/location/report`, testCase.data);
      console.log('⚠️ Debería haber fallado pero fue exitoso:', response.data);
    } catch (error) {
      if (error.response?.status === 400) {
        console.log('✅ Validación correcta - Error 400:', error.response.data.error);
      } else {
        console.error('❌ Error inesperado:', error.response?.data || error.message);
      }
    }
  }
}

async function testBatchLocationReport() {
  console.log('\n🧪 Probando reporte de ubicación en lote...');
  
  const batchData = {
    locations: [
      { ...testLocationData, timestamp: new Date(Date.now() - 60000).toISOString() },
      { ...testLocationData, timestamp: new Date(Date.now() - 30000).toISOString() },
      { ...testLocationData, timestamp: new Date().toISOString() }
    ],
    deviceInfo: {
      appVersion: '1.0.0',
      platform: 'test'
    }
  };
  
  console.log('📍 Datos de lote:', JSON.stringify(batchData, null, 2));
  
  try {
    const response = await axios.post(`${API_BASE_URL}/mobile/location/batch`, batchData);
    console.log('✅ Reporte en lote exitoso:', response.data);
    return true;
  } catch (error) {
    console.error('❌ Error en reporte en lote:', error.response?.data || error.message);
    return false;
  }
}

async function testHealthCheck() {
  console.log('🧪 Verificando estado del servidor...');
  
  try {
    const response = await axios.get(`${API_BASE_URL}/mobile/health`);
    console.log('✅ Servidor funcionando:', response.data);
    return true;
  } catch (error) {
    console.error('❌ Servidor no disponible:', error.message);
    return false;
  }
}

async function runAllTests() {
  console.log('🌋 Volcano App - Pruebas de Reporte de Ubicación');
  console.log('='.repeat(50));
  
  // Verificar que el servidor esté funcionando
  const serverOk = await testHealthCheck();
  if (!serverOk) {
    console.log('\n❌ El servidor no está disponible. Asegúrate de que esté ejecutándose en el puerto 3002.');
    process.exit(1);
  }
  
  // Ejecutar pruebas
  const validTest = await testValidLocationReport();
  await testInvalidLocationReports();
  const batchTest = await testBatchLocationReport();
  
  // Resumen
  console.log('\n' + '='.repeat(50));
  console.log('📊 Resumen de Pruebas:');
  console.log(`✅ Reporte válido: ${validTest ? 'PASÓ' : 'FALLÓ'}`);
  console.log(`✅ Reporte en lote: ${batchTest ? 'PASÓ' : 'FALLÓ'}`);
  console.log('✅ Validaciones de errores: Verificar logs arriba');
  
  if (validTest && batchTest) {
    console.log('\n🎉 ¡Todas las pruebas principales pasaron!');
    console.log('📱 El sistema de reporte de ubicación está funcionando correctamente.');
  } else {
    console.log('\n⚠️ Algunas pruebas fallaron. Revisar los errores arriba.');
  }
}

// Ejecutar pruebas si se llama directamente
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  testValidLocationReport,
  testInvalidLocationReports,
  testBatchLocationReport,
  testHealthCheck,
  runAllTests
};
