/**
 * 🌋 Volcano App Backend - Swagger/OpenAPI Configuration
 * Comprehensive API documentation setup
 */

import swaggerJsdoc from 'swagger-jsdoc';
import { CONFIG } from './env';

// =====================================================
// SWAGGER CONFIGURATION
// =====================================================

const options: swaggerJsdoc.Options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'Volcano App Backend API',
      version: '1.0.0',
      description: `
        Comprehensive REST API for the Volcano App backoffice system and mobile application.
        
        ## Features
        - **Authentication**: JWT-based authentication with role-based authorization
        - **Real-time Updates**: WebSocket support for live data synchronization
        - **Mobile Optimized**: Specialized endpoints for mobile app consumption
        - **Geospatial**: PostGIS integration for location-based queries
        - **Audit Trail**: Complete audit logging for all operations
        
        ## Authentication
        Most endpoints require authentication using Bear<PERSON> tokens:
        \`\`\`
        Authorization: Bearer <your-jwt-token>
        \`\`\`
        
        ## Rate Limiting
        API requests are limited to 100 requests per 15-minute window per IP address.
        
        ## Error Handling
        All endpoints return consistent error responses with appropriate HTTP status codes.
      `,
      contact: {
        name: 'Volcano App Team',
        email: '<EMAIL>'
      },
      license: {
        name: 'MIT',
        url: 'https://opensource.org/licenses/MIT'
      }
    },
    servers: [
      {
        url: `http://${CONFIG.SERVER.HOST}:${CONFIG.SERVER.PORT}/api`,
        description: 'Development server'
      },
      {
        url: 'https://api.volcanoapp.com/api',
        description: 'Production server'
      }
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          description: 'JWT token obtained from /auth/login endpoint'
        },
        apiKey: {
          type: 'apiKey',
          in: 'header',
          name: 'X-API-Key',
          description: 'API key for mobile app authentication'
        }
      },
      schemas: {
        // Error responses
        ErrorResponse: {
          type: 'object',
          properties: {
            success: { type: 'boolean', example: false },
            error: { type: 'string', example: 'Error message' },
            timestamp: { type: 'string', format: 'date-time' }
          }
        },
        ValidationErrorResponse: {
          type: 'object',
          properties: {
            success: { type: 'boolean', example: false },
            error: { type: 'string', example: 'Validation failed' },
            details: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  field: { type: 'string' },
                  message: { type: 'string' }
                }
              }
            },
            timestamp: { type: 'string', format: 'date-time' }
          }
        },
        
        // Success responses
        SuccessResponse: {
          type: 'object',
          properties: {
            success: { type: 'boolean', example: true },
            data: { type: 'object' },
            timestamp: { type: 'string', format: 'date-time' }
          }
        },
        PaginatedResponse: {
          type: 'object',
          properties: {
            success: { type: 'boolean', example: true },
            data: { type: 'array', items: {} },
            pagination: {
              type: 'object',
              properties: {
                page: { type: 'integer', example: 1 },
                limit: { type: 'integer', example: 20 },
                total: { type: 'integer', example: 100 },
                pages: { type: 'integer', example: 5 }
              }
            },
            timestamp: { type: 'string', format: 'date-time' }
          }
        },
        
        // Data models
        User: {
          type: 'object',
          properties: {
            id: { type: 'string', format: 'uuid' },
            email: { type: 'string', format: 'email' },
            full_name: { type: 'string' },
            role: { type: 'string', enum: ['ADMIN', 'OPERATOR', 'VIEWER'] },
            is_active: { type: 'boolean' },
            last_login: { type: 'string', format: 'date-time', nullable: true },
            created_at: { type: 'string', format: 'date-time' },
            updated_at: { type: 'string', format: 'date-time' }
          }
        },
        
        VolcanoAlert: {
          type: 'object',
          properties: {
            id: { type: 'string', format: 'uuid' },
            title: { type: 'string' },
            message: { type: 'string' },
            alert_level: { 
              type: 'string', 
              enum: ['NORMAL', 'ADVISORY', 'WATCH', 'WARNING', 'EMERGENCY'] 
            },
            volcano_name: { type: 'string' },
            volcano_lat: { type: 'number', format: 'double' },
            volcano_lng: { type: 'number', format: 'double' },
            is_active: { type: 'boolean' },
            is_scheduled: { type: 'boolean' },
            scheduled_for: { type: 'string', format: 'date-time', nullable: true },
            expires_at: { type: 'string', format: 'date-time', nullable: true },
            created_by: { type: 'string', format: 'uuid', nullable: true },
            created_at: { type: 'string', format: 'date-time' },
            updated_at: { type: 'string', format: 'date-time' },
            metadata: { type: 'object', nullable: true }
          }
        },
        
        SafetyZone: {
          type: 'object',
          properties: {
            id: { type: 'string', format: 'uuid' },
            name: { type: 'string' },
            description: { type: 'string', nullable: true },
            zone_type: { 
              type: 'string', 
              enum: ['SAFE', 'EMERGENCY', 'DANGER', 'EVACUATION', 'RESTRICTED'] 
            },
            geometry: { 
              type: 'object',
              description: 'GeoJSON geometry object'
            },
            capacity: { type: 'integer', nullable: true },
            contact_info: { type: 'object', nullable: true },
            facilities: { type: 'object', nullable: true },
            is_active: { type: 'boolean' },
            version: { type: 'integer' },
            created_by: { type: 'string', format: 'uuid', nullable: true },
            created_at: { type: 'string', format: 'date-time' },
            updated_at: { type: 'string', format: 'date-time' },
            metadata: { type: 'object', nullable: true }
          }
        },
        
        // DTOs
        LoginRequest: {
          type: 'object',
          required: ['email', 'password'],
          properties: {
            email: { type: 'string', format: 'email' },
            password: { type: 'string', minLength: 8 }
          }
        },
        
        CreateAlertRequest: {
          type: 'object',
          required: ['title', 'message', 'alert_level'],
          properties: {
            title: { type: 'string', maxLength: 255 },
            message: { type: 'string' },
            alert_level: { 
              type: 'string', 
              enum: ['NORMAL', 'ADVISORY', 'WATCH', 'WARNING', 'EMERGENCY'] 
            },
            volcano_name: { type: 'string', default: 'Volcán Villarrica' },
            volcano_lat: { type: 'number', format: 'double', default: -39.420000 },
            volcano_lng: { type: 'number', format: 'double', default: -71.939167 },
            is_scheduled: { type: 'boolean', default: false },
            scheduled_for: { type: 'string', format: 'date-time', nullable: true },
            expires_at: { type: 'string', format: 'date-time', nullable: true },
            metadata: { type: 'object', nullable: true }
          }
        },
        
        LocationReport: {
          type: 'object',
          required: ['anonymous_id', 'latitude', 'longitude'],
          properties: {
            anonymous_id: { type: 'string' },
            latitude: { type: 'number', format: 'double', minimum: -90, maximum: 90 },
            longitude: { type: 'number', format: 'double', minimum: -180, maximum: 180 },
            accuracy: { type: 'number', format: 'double', nullable: true },
            app_version: { type: 'string', nullable: true },
            device_type: { type: 'string', nullable: true }
          }
        }
      }
    },
    tags: [
      {
        name: 'Authentication',
        description: 'User authentication and authorization endpoints'
      },
      {
        name: 'Volcano Alerts',
        description: 'Volcano alert management endpoints'
      },
      {
        name: 'Safety Zones',
        description: 'Safety zone management endpoints'
      },
      {
        name: 'Mobile API',
        description: 'Mobile app specific endpoints'
      },
      {
        name: 'Audit',
        description: 'Audit log and system monitoring endpoints'
      },
      {
        name: 'Configuration',
        description: 'System configuration management'
      },
      {
        name: 'Real-time',
        description: 'WebSocket and real-time data endpoints'
      },
      {
        name: 'System',
        description: 'Health checks and system status'
      }
    ]
  },
  apis: [
    './src/routes/*.ts',
    './src/controllers/*.ts',
    './src/middleware/*.ts'
  ]
};

export const swaggerSpec = swaggerJsdoc(options);

export default {
  swaggerSpec,
  options
};
