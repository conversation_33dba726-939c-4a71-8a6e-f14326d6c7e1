/**
 * 🌋 Volcano App Backend - Controlador de Logs de Auditoría
 * Gestión y consulta de logs de auditoría del sistema
 */

import { Response } from 'express';
import { supabaseAdmin } from '@/services/supabase';
import { logger } from '@/utils/logger';
import { 
  AuthenticatedRequest, 
  AuditAction,
  DEFAULT_PAGINATION 
} from '@/types';

// =====================================================
// OBTENER LOGS DE AUDITORÍA
// =====================================================

/**
 * Obtener logs de auditoría con filtros y paginación
 * GET /api/audit
 */
export async function getAuditLogs(req: AuthenticatedRequest, res: Response) {
  try {
    const {
      page = DEFAULT_PAGINATION.page,
      limit = DEFAULT_PAGINATION.limit,
      sort_by = 'created_at',
      sort_order = 'desc',
      table_name,
      action,
      user_id,
      start_date,
      end_date,
      record_id
    } = req.query as any;

    // Validar límite máximo
    const validLimit = Math.min(parseInt(limit), DEFAULT_PAGINATION.max_limit);
    const offset = (parseInt(page) - 1) * validLimit;

    // Construir query base
    let query = supabaseAdmin
      .from('audit_logs')
      .select(`
        id,
        user_id,
        action,
        table_name,
        record_id,
        old_values,
        new_values,
        ip_address,
        user_agent,
        created_at,
        admin_users!user_id(full_name, email)
      `, { count: 'exact' });

    // Aplicar filtros
    if (table_name) {
      query = query.eq('table_name', table_name);
    }

    if (action) {
      query = query.eq('action', action.toUpperCase());
    }

    if (user_id) {
      query = query.eq('user_id', user_id);
    }

    if (record_id) {
      query = query.eq('record_id', record_id);
    }

    if (start_date) {
      query = query.gte('created_at', start_date);
    }

    if (end_date) {
      query = query.lte('created_at', end_date);
    }

    // Aplicar ordenamiento y paginación
    query = query
      .order(sort_by, { ascending: sort_order === 'asc' })
      .range(offset, offset + validLimit - 1);

    const { data: auditLogs, error, count } = await query;

    if (error) {
      throw error;
    }

    const totalPages = Math.ceil((count || 0) / validLimit);

    res.json({
      success: true,
      data: auditLogs,
      pagination: {
        page: parseInt(page),
        limit: validLimit,
        total: count || 0,
        totalPages,
        hasNext: parseInt(page) < totalPages,
        hasPrev: parseInt(page) > 1
      },
      timestamp: new Date()
    });

  } catch (error) {
    logger.error('Get audit logs error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch audit logs',
      timestamp: new Date()
    });
  }
}

// =====================================================
// OBTENER LOG DE AUDITORÍA POR ID
// =====================================================

/**
 * Obtener un log de auditoría específico por ID
 * GET /api/audit/:id
 */
export async function getAuditLogById(req: AuthenticatedRequest, res: Response) {
  try {
    const { id } = req.params;

    const { data: auditLog, error } = await supabaseAdmin
      .from('audit_logs')
      .select(`
        *,
        admin_users!user_id(full_name, email, role)
      `)
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return res.status(404).json({
          success: false,
          error: 'Audit log not found',
          timestamp: new Date()
        });
      }
      throw error;
    }

    res.json({
      success: true,
      data: auditLog,
      timestamp: new Date()
    });

  } catch (error) {
    logger.error('Get audit log by ID error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch audit log',
      timestamp: new Date()
    });
  }
}

// =====================================================
// OBTENER ESTADÍSTICAS DE AUDITORÍA
// =====================================================

/**
 * Obtener estadísticas de actividad de auditoría
 * GET /api/audit/stats
 */
export async function getAuditStats(req: AuthenticatedRequest, res: Response) {
  try {
    const { 
      start_date = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 días atrás
      end_date = new Date().toISOString()
    } = req.query as any;

    // Estadísticas por acción
    const { data: actionStats, error: actionError } = await supabaseAdmin
      .from('audit_logs')
      .select('action')
      .gte('created_at', start_date)
      .lte('created_at', end_date);

    if (actionError) {
      throw actionError;
    }

    // Estadísticas por tabla
    const { data: tableStats, error: tableError } = await supabaseAdmin
      .from('audit_logs')
      .select('table_name')
      .gte('created_at', start_date)
      .lte('created_at', end_date);

    if (tableError) {
      throw tableError;
    }

    // Estadísticas por usuario
    const { data: userStats, error: userError } = await supabaseAdmin
      .from('audit_logs')
      .select(`
        user_id,
        admin_users!user_id(full_name, email)
      `)
      .gte('created_at', start_date)
      .lte('created_at', end_date);

    if (userError) {
      throw userError;
    }

    // Procesar estadísticas
    const actionCounts = actionStats?.reduce((acc: any, log: any) => {
      acc[log.action] = (acc[log.action] || 0) + 1;
      return acc;
    }, {}) || {};

    const tableCounts = tableStats?.reduce((acc: any, log: any) => {
      if (log.table_name) {
        acc[log.table_name] = (acc[log.table_name] || 0) + 1;
      }
      return acc;
    }, {}) || {};

    const userCounts = userStats?.reduce((acc: any, log: any) => {
      if (log.user_id) {
        const userKey = log.admin_users?.full_name || log.user_id;
        acc[userKey] = (acc[userKey] || 0) + 1;
      }
      return acc;
    }, {}) || {};

    res.json({
      success: true,
      data: {
        period: {
          start_date,
          end_date
        },
        total_logs: actionStats?.length || 0,
        by_action: actionCounts,
        by_table: tableCounts,
        by_user: userCounts
      },
      timestamp: new Date()
    });

  } catch (error) {
    logger.error('Get audit stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch audit statistics',
      timestamp: new Date()
    });
  }
}

// =====================================================
// OBTENER ACTIVIDAD RECIENTE
// =====================================================

/**
 * Obtener actividad reciente del sistema
 * GET /api/audit/recent
 */
export async function getRecentActivity(req: AuthenticatedRequest, res: Response) {
  try {
    const { 
      limit = 20,
      hours = 24 
    } = req.query as any;

    const validLimit = Math.min(parseInt(limit), 100);
    const startTime = new Date(Date.now() - parseInt(hours) * 60 * 60 * 1000).toISOString();

    const { data: recentLogs, error } = await supabaseAdmin
      .from('audit_logs')
      .select(`
        id,
        action,
        table_name,
        record_id,
        created_at,
        admin_users!user_id(full_name, email)
      `)
      .gte('created_at', startTime)
      .order('created_at', { ascending: false })
      .limit(validLimit);

    if (error) {
      throw error;
    }

    res.json({
      success: true,
      data: recentLogs || [],
      period: {
        hours: parseInt(hours),
        since: startTime
      },
      timestamp: new Date()
    });

  } catch (error) {
    logger.error('Get recent activity error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch recent activity',
      timestamp: new Date()
    });
  }
}

// =====================================================
// EXPORTACIONES
// =====================================================

export default {
  getAuditLogs,
  getAuditLogById,
  getAuditStats,
  getRecentActivity
};
