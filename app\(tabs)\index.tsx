/**
 * Volcano App - Dashboard Principal
 * Pantalla de inicio con estado del volcán y acceso rápido a funciones críticas
 */

import { ConnectionStatus } from '@/components/ConnectionStatus';
import {
    EmergencyButton,
    PrimaryButton,
    SecondaryButton
} from '@/components/ui/AccessibleButton';
import { AccessibleText } from '@/components/ui/AccessibleText';
import {
    AlertIcon,
    BellIcon,
    InfoIcon,
    MapIcon,
    SettingsIcon
} from '@/components/ui/ModernIcon';
import { LiveVolcanoStatus } from '@/components/VolcanoStatus';
import { AlertLevel, Colors } from '@/constants/Colors';
import { BorderRadius, Spacing } from '@/constants/Layout';
import { useVolcanoData } from '@/hooks/useApi';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useNotifications } from '@/hooks/useNotifications';
import { useWebSocket } from '@/services/websocket';
import React, { useState } from 'react';
import {
    Alert,
    RefreshControl,
    ScrollView,
    StyleSheet,
    View
} from 'react-native';

// Mock data - En producción esto vendría de APIs oficiales
const mockVolcanoData = {
  name: 'Volcán Villarrica',
  alertLevel: 'ADVISORY' as AlertLevel,
  lastUpdate: new Date(Date.now() - 15 * 60 * 1000), // 15 minutos atrás
  description: 'Se observa actividad sísmica moderada y emisiones de gases normales.',
  location: 'Pucón, Región de la Araucanía',
  elevation: '2,847 metros',
};

const mockAlerts = [
  {
    id: 1,
    type: 'info',
    title: 'Actualización de Monitoreo',
    message: 'SERNAGEOMIN reporta actividad normal en las últimas 24 horas.',
    time: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 horas atrás
  },
  {
    id: 2,
    type: 'warning',
    title: 'Recomendación Turística',
    message: 'Se recomienda mantenerse informado antes de realizar actividades en el volcán.',
    time: new Date(Date.now() - 6 * 60 * 60 * 1000), // 6 horas atrás
  },
];

export default function HomeScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const [refreshing, setRefreshing] = useState(false);
  const [volcanoData, setVolcanoData] = useState(mockVolcanoData);
  const [alerts, setAlerts] = useState(mockAlerts);

  // API hooks
  const { isLoading, hasError, isOnline, refetchAll } = useVolcanoData();
  const { isConnected, lastEvent } = useWebSocket();
  const { state: notificationState, isReady: notificationsReady } = useNotifications();

  // Actualizar datos desde la API
  const onRefresh = async () => {
    setRefreshing(true);
    try {
      await refetchAll();
    } catch (error) {
      console.error('Error refreshing data:', error);
    } finally {
      setRefreshing(false);
    }
  };

  // Manejar botón de emergencia
  const handleEmergencyAction = () => {
    Alert.alert(
      'Emergencia Volcánica',
      '¿Necesita ayuda inmediata?',
      [
        {
          text: 'Llamar 133 (Emergencias)',
          onPress: () => {
            // En producción: Linking.openURL('tel:133')
            Alert.alert('Llamando', 'Conectando con servicios de emergencia...');
          },
          style: 'destructive',
        },
        {
          text: 'Ver Rutas de Evacuación',
          onPress: () => {
            // Navegar a mapa de evacuación
            Alert.alert('Navegando', 'Abriendo mapa de rutas de evacuación...');
          },
        },
        {
          text: 'Cancelar',
          style: 'cancel',
        },
      ]
    );
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },

    scrollContent: {
      padding: Spacing.md,
    },

    header: {
      marginBottom: Spacing.lg,
    },

    welcomeText: {
      marginBottom: Spacing.sm,
    },

    locationText: {
      marginBottom: Spacing.md,
    },

    statusSection: {
      marginBottom: Spacing.lg,
    },

    quickActions: {
      marginBottom: Spacing.lg,
    },

    actionGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: Spacing.sm,
      marginTop: Spacing.md,
    },

    actionButton: {
      flex: 1,
      minWidth: '45%',
    },

    emergencySection: {
      marginBottom: Spacing.lg,
      padding: Spacing.md,
      backgroundColor: colors.backgroundSecondary,
      borderRadius: BorderRadius.lg,
      borderLeftWidth: 4,
      borderLeftColor: colors.error,
    },

    alertsSection: {
      marginBottom: Spacing.lg,
    },

    alertItem: {
      backgroundColor: colors.backgroundSecondary,
      padding: Spacing.md,
      borderRadius: BorderRadius.md,
      marginBottom: Spacing.sm,
      borderLeftWidth: 3,
    },

    alertInfo: {
      borderLeftColor: colors.info,
    },

    alertWarning: {
      borderLeftColor: colors.warning,
    },

    alertHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: Spacing.sm,
    },

    footer: {
      padding: Spacing.md,
      alignItems: 'center',
    },
  });

  const formatAlertTime = (date: Date) => {
    const now = new Date();
    const diffHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffHours < 1) return 'Hace menos de 1 hora';
    if (diffHours < 24) return `Hace ${diffHours} horas`;

    const diffDays = Math.floor(diffHours / 24);
    return `Hace ${diffDays} días`;
  };

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollContent}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            tintColor={colors.tint}
            colors={[colors.tint]}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.welcomeText}>
            <AccessibleText variant="h1">Volcano App</AccessibleText>
          </View>
          <View style={styles.locationText}>
            <AccessibleText variant="body" color="secondary">
              Monitoreo en tiempo real • {volcanoData.location}
            </AccessibleText>
          </View>
        </View>

        {/* Estado del Volcán */}
        <View style={styles.statusSection}>
          <LiveVolcanoStatus />
        </View>

        {/* Sección de Emergencia */}
        <View style={styles.emergencySection}>
          <AccessibleText variant="alert" color="error">¿Emergencia Volcánica?</AccessibleText>
          <AccessibleText variant="body" color="secondary" style={{ marginVertical: Spacing.sm }}>
            Acceso inmediato a ayuda y rutas de evacuación
          </AccessibleText>
          <EmergencyButton
            onPress={handleEmergencyAction}
            fullWidth
            accessibilityLabel="Botón de emergencia volcánica"
            icon={<AlertIcon size={24} color="white" strokeWidth={3} />}
            iconPosition="left"
          >
            EMERGENCIA
          </EmergencyButton>
        </View>

        {/* Acciones Rápidas */}
        <View style={styles.quickActions}>
          <AccessibleText variant="h3">Acciones Rápidas</AccessibleText>
          <View style={styles.actionGrid}>
            <PrimaryButton
              style={styles.actionButton}
              onPress={() => Alert.alert('Navegando', 'Abriendo mapa interactivo...')}
              icon={<MapIcon size={20} color="white" strokeWidth={2.5} />}
              iconPosition="left"
            >
              Mapa
            </PrimaryButton>
            <SecondaryButton
              style={styles.actionButton}
              onPress={() => Alert.alert('Navegando', 'Abriendo guías de preparación...')}
              icon={<InfoIcon size={20} strokeWidth={2.5} />}
              iconPosition="left"
            >
              Guías
            </SecondaryButton>
            <SecondaryButton
              style={styles.actionButton}
              onPress={() => Alert.alert('Navegando', 'Abriendo información oficial...')}
              icon={<BellIcon size={20} strokeWidth={2.5} />}
              iconPosition="left"
            >
              Noticias
            </SecondaryButton>
            <SecondaryButton
              style={styles.actionButton}
              onPress={() => Alert.alert('Navegando', 'Abriendo configuración...')}
              icon={<SettingsIcon size={20} strokeWidth={2.5} />}
              iconPosition="left"
            >
              Ajustes
            </SecondaryButton>
          </View>
        </View>

        {/* Alertas Recientes */}
        <View style={styles.alertsSection}>
          <AccessibleText variant="h3">Actualizaciones Recientes</AccessibleText>
          {alerts.map((alert) => (
            <View
              key={alert.id}
              style={[
                styles.alertItem,
                alert.type === 'info' ? styles.alertInfo : styles.alertWarning
              ]}
            >
              <View style={styles.alertHeader}>
                <AccessibleText variant="button">{alert.title}</AccessibleText>
                <AccessibleText variant="caption" color="muted">{formatAlertTime(alert.time)}</AccessibleText>
              </View>
              <AccessibleText variant="body" color="secondary">{alert.message}</AccessibleText>
            </View>
          ))}
        </View>

        {/* Connection Status - Solo en desarrollo */}
        {__DEV__ && <ConnectionStatus />}

        {/* Footer */}
        <View style={styles.footer}>
          <AccessibleText variant="caption" color="muted">
            Datos oficiales de SERNAGEOMIN • ONEMI
          </AccessibleText>
          <AccessibleText variant="caption" color="muted">
            Desliza hacia abajo para actualizar
          </AccessibleText>
        </View>
      </ScrollView>
    </View>
  );
}
