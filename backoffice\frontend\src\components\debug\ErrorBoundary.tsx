/**
 * 🚨 Error Boundary para capturar errores de React
 * Componente que captura errores de JavaScript en cualquier lugar del árbol de componentes
 */

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Alert<PERSON>riangle, Refresh<PERSON><PERSON>, Bug } from 'lucide-react';
import { But<PERSON> } from '../ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { debugManager } from '../../utils/debug';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string | null;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // Actualizar el estado para mostrar la UI de error
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Capturar el error con nuestro sistema de debugging
    const errorId = `react_error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    debugManager.captureError({
      type: 'react',
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      url: window.location.href,
      userAgent: navigator.userAgent,
      buildVersion: (window as any).__APP_VERSION__,
    });

    this.setState({
      errorInfo,
      errorId,
    });

    // Log adicional para debugging
    console.group('🚨 React Error Boundary');
    console.error('Error:', error);
    console.error('Error Info:', errorInfo);
    console.error('Component Stack:', errorInfo.componentStack);
    console.groupEnd();
  }

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
    });
  };

  handleReload = () => {
    window.location.reload();
  };

  handleOpenDebugPanel = () => {
    // Disparar evento personalizado para abrir el panel de debugging
    window.dispatchEvent(new CustomEvent('openDebugPanel'));
  };

  render() {
    if (this.state.hasError) {
      // Si se proporciona un fallback personalizado, usarlo
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // UI de error por defecto
      return (
        <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
          <Card className="w-full max-w-2xl border-red-200">
            <CardHeader className="text-center">
              <div className="flex justify-center mb-4">
                <AlertTriangle className="h-16 w-16 text-red-500" />
              </div>
              <CardTitle className="text-xl text-red-700">
                ¡Oops! Algo salió mal
              </CardTitle>
              <p className="text-gray-600 mt-2">
                Se produjo un error inesperado en la aplicación. Nuestro equipo ha sido notificado.
              </p>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Información del error */}
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <h4 className="font-semibold text-red-800 mb-2">Detalles del Error:</h4>
                <p className="text-sm text-red-700 font-mono">
                  {this.state.error?.message}
                </p>
                {this.state.errorId && (
                  <p className="text-xs text-red-600 mt-2">
                    ID del Error: {this.state.errorId}
                  </p>
                )}
              </div>

              {/* Stack trace (solo en desarrollo) */}
              {import.meta.env.DEV && this.state.error?.stack && (
                <details className="bg-gray-100 border rounded-lg p-4">
                  <summary className="cursor-pointer font-semibold text-gray-700 mb-2">
                    Ver Stack Trace (Desarrollo)
                  </summary>
                  <pre className="text-xs text-gray-600 overflow-auto whitespace-pre-wrap">
                    {this.state.error.stack}
                  </pre>
                </details>
              )}

              {/* Component stack (solo en desarrollo) */}
              {import.meta.env.DEV && this.state.errorInfo?.componentStack && (
                <details className="bg-gray-100 border rounded-lg p-4">
                  <summary className="cursor-pointer font-semibold text-gray-700 mb-2">
                    Ver Component Stack (Desarrollo)
                  </summary>
                  <pre className="text-xs text-gray-600 overflow-auto whitespace-pre-wrap">
                    {this.state.errorInfo.componentStack}
                  </pre>
                </details>
              )}

              {/* Acciones */}
              <div className="flex flex-col sm:flex-row gap-3 pt-4">
                <Button 
                  onClick={this.handleRetry}
                  className="flex-1"
                  variant="default"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Intentar de Nuevo
                </Button>
                
                <Button 
                  onClick={this.handleReload}
                  className="flex-1"
                  variant="outline"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Recargar Página
                </Button>

                {import.meta.env.DEV && (
                  <Button 
                    onClick={this.handleOpenDebugPanel}
                    className="flex-1"
                    variant="outline"
                  >
                    <Bug className="h-4 w-4 mr-2" />
                    Ver Debugging
                  </Button>
                )}
              </div>

              {/* Información adicional */}
              <div className="text-center text-sm text-gray-500 pt-4 border-t">
                <p>
                  Si el problema persiste, por favor contacta al soporte técnico.
                </p>
                <p className="mt-1">
                  Timestamp: {new Date().toLocaleString()}
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}

// Hook para usar el Error Boundary de forma más sencilla
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  fallback?: ReactNode
) {
  return function WrappedComponent(props: P) {
    return (
      <ErrorBoundary fallback={fallback}>
        <Component {...props} />
      </ErrorBoundary>
    );
  };
}

// Componente de error simple para casos específicos
export function ErrorFallback({ 
  error, 
  resetError 
}: { 
  error: Error; 
  resetError: () => void; 
}) {
  return (
    <div className="p-6 text-center border border-red-200 rounded-lg bg-red-50">
      <AlertTriangle className="h-8 w-8 text-red-500 mx-auto mb-3" />
      <h3 className="text-lg font-semibold text-red-700 mb-2">
        Error en el Componente
      </h3>
      <p className="text-red-600 mb-4 text-sm">
        {error.message}
      </p>
      <Button onClick={resetError} size="sm" variant="outline">
        <RefreshCw className="h-4 w-4 mr-2" />
        Reintentar
      </Button>
    </div>
  );
}
