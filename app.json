{"expo": {"name": "volcanoApp", "slug": "volcanoApp", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "volcanoapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "infoPlist": {"NSLocationWhenInUseUsageDescription": "Volcano App necesita acceso a tu ubicación para mostrarte tu posición en el mapa y calcular la distancia al volcán para tu seguridad.", "NSLocationAlwaysAndWhenInUseUsageDescription": "Volcano App necesita acceso a tu ubicación para enviarte alertas de emergencia basadas en tu proximidad al volcán."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "permissions": ["ACCESS_FINE_LOCATION", "ACCESS_COARSE_LOCATION", "RECEIVE_BOOT_COMPLETED", "VIBRATE", "WAKE_LOCK", "com.android.alarm.permission.SET_ALARM", "android.permission.SCHEDULE_EXACT_ALARM"]}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}]], "experiments": {"typedRoutes": true}, "extra": {"eas": {"projectId": "volcano-app-mobile"}}}}