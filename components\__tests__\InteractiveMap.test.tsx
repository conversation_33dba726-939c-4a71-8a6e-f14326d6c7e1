/**
 * 🌋 Volcano App Mobile - Tests para InteractiveMap
 * Tests unitarios para el componente de mapa interactivo móvil
 */

import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { InteractiveMap } from '../InteractiveMap';

// Mock para expo-location
jest.mock('expo-location', () => ({
  requestForegroundPermissionsAsync: jest.fn(() => 
    Promise.resolve({ status: 'granted' })
  ),
  getCurrentPositionAsync: jest.fn(() => 
    Promise.resolve({
      coords: {
        latitude: -39.2904,
        longitude: -71.9048,
        accuracy: 10
      }
    })
  ),
  Accuracy: {
    High: 1
  }
}));

// Mock para react-native-webview
jest.mock('react-native-webview', () => {
  const { View } = require('react-native');
  return {
    WebView: (props: any) => {
      return <View testID="webview" {...props} />;
    }
  };
});

// Mock para useApi
jest.mock('@/hooks/useApi', () => ({
  useLocationCheck: jest.fn(() => ({
    mutate: jest.fn(),
    isLoading: false,
    error: null
  })),
  useMobileConfig: jest.fn(() => ({
    data: {
      mobile_app_version: '1.0.0',
      emergency_contact: '+56912345678'
    },
    isLoading: false,
    error: null
  }))
}));

const mockProps = {
  showUserLocation: true,
  showSafetyZones: true,
  onLocationUpdate: jest.fn()
};

describe('InteractiveMap', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders map component correctly', () => {
    const { getByTestId } = render(<InteractiveMap {...mockProps} />);
    
    expect(getByTestId('webview')).toBeTruthy();
  });

  it('requests location permissions on mount', async () => {
    const Location = require('expo-location');
    
    render(<InteractiveMap {...mockProps} />);
    
    await waitFor(() => {
      expect(Location.requestForegroundPermissionsAsync).toHaveBeenCalled();
    });
  });

  it('gets current location when permissions granted', async () => {
    const Location = require('expo-location');
    
    render(<InteractiveMap {...mockProps} />);
    
    await waitFor(() => {
      expect(Location.getCurrentPositionAsync).toHaveBeenCalledWith({
        accuracy: Location.Accuracy.High,
      });
    });
  });

  it('calls onLocationUpdate when location is obtained', async () => {
    render(<InteractiveMap {...mockProps} />);
    
    await waitFor(() => {
      expect(mockProps.onLocationUpdate).toHaveBeenCalledWith({
        lat: -39.2904,
        lng: -71.9048
      });
    });
  });

  it('handles location permission denied', async () => {
    const Location = require('expo-location');
    Location.requestForegroundPermissionsAsync.mockResolvedValueOnce({
      status: 'denied'
    });
    
    const { getByText } = render(<InteractiveMap {...mockProps} />);
    
    await waitFor(() => {
      expect(getByText('Permisos de ubicación requeridos')).toBeTruthy();
    });
  });

  it('handles location error gracefully', async () => {
    const Location = require('expo-location');
    Location.getCurrentPositionAsync.mockRejectedValueOnce(new Error('Location error'));
    
    const { getByText } = render(<InteractiveMap {...mockProps} />);
    
    await waitFor(() => {
      expect(getByText('Error al obtener ubicación')).toBeTruthy();
    });
  });

  it('shows loading state initially', () => {
    const { getByText } = render(<InteractiveMap {...mockProps} />);
    
    expect(getByText('Cargando mapa...')).toBeTruthy();
  });

  it('hides user location when showUserLocation is false', () => {
    const { getByTestId } = render(
      <InteractiveMap {...mockProps} showUserLocation={false} />
    );
    
    const webview = getByTestId('webview');
    expect(webview.props.source.html).not.toContain('userLocation');
  });

  it('hides safety zones when showSafetyZones is false', () => {
    const { getByTestId } = render(
      <InteractiveMap {...mockProps} showSafetyZones={false} />
    );
    
    const webview = getByTestId('webview');
    expect(webview.props.source.html).not.toContain('safetyZones');
  });

  it('handles webview messages correctly', () => {
    const { getByTestId } = render(<InteractiveMap {...mockProps} />);
    
    const webview = getByTestId('webview');
    const mockMessage = {
      nativeEvent: {
        data: JSON.stringify({
          type: 'locationUpdate',
          data: {
            coords: { lat: -39.2904, lng: -71.9048 },
            distanceToVolcano: '5.2'
          }
        })
      }
    };
    
    fireEvent(webview, 'onMessage', mockMessage);
    
    expect(mockProps.onLocationUpdate).toHaveBeenCalledWith({
      lat: -39.2904,
      lng: -71.9048
    });
  });

  it('handles invalid webview messages', () => {
    const { getByTestId } = render(<InteractiveMap {...mockProps} />);
    
    const webview = getByTestId('webview');
    const mockMessage = {
      nativeEvent: {
        data: 'invalid-json'
      }
    };
    
    // No debería lanzar error
    expect(() => {
      fireEvent(webview, 'onMessage', mockMessage);
    }).not.toThrow();
  });

  it('includes volcano coordinates in map HTML', () => {
    const { getByTestId } = render(<InteractiveMap {...mockProps} />);
    
    const webview = getByTestId('webview');
    expect(webview.props.source.html).toContain('-39.2904');
    expect(webview.props.source.html).toContain('-71.9048');
  });

  it('includes map controls in HTML', () => {
    const { getByTestId } = render(<InteractiveMap {...mockProps} />);
    
    const webview = getByTestId('webview');
    const html = webview.props.source.html;
    
    expect(html).toContain('L.map');
    expect(html).toContain('tileLayer');
    expect(html).toContain('marker');
  });

  it('handles map zoom controls', () => {
    const { getByTestId } = render(<InteractiveMap {...mockProps} />);
    
    const webview = getByTestId('webview');
    const html = webview.props.source.html;
    
    expect(html).toContain('zoomControl');
  });

  it('includes safety zone polygons when enabled', () => {
    const { getByTestId } = render(<InteractiveMap {...mockProps} />);
    
    const webview = getByTestId('webview');
    const html = webview.props.source.html;
    
    expect(html).toContain('polygon');
    expect(html).toContain('safetyZones');
  });

  it('handles location reporting to API', async () => {
    const { useLocationCheck } = require('@/hooks/useApi');
    const mockMutate = jest.fn();
    useLocationCheck.mockReturnValue({
      mutate: mockMutate,
      isLoading: false,
      error: null
    });
    
    render(<InteractiveMap {...mockProps} />);
    
    await waitFor(() => {
      expect(mockMutate).toHaveBeenCalledWith({
        latitude: -39.2904,
        longitude: -71.9048,
        accuracy: 10,
        timestamp: expect.any(String),
        app_version: '1.0.0',
        device_type: expect.any(String)
      });
    });
  });

  it('handles API reporting error gracefully', async () => {
    const { useLocationCheck } = require('@/hooks/useApi');
    const mockMutate = jest.fn().mockRejectedValue(new Error('API error'));
    useLocationCheck.mockReturnValue({
      mutate: mockMutate,
      isLoading: false,
      error: null
    });
    
    // No debería lanzar error
    expect(() => {
      render(<InteractiveMap {...mockProps} />);
    }).not.toThrow();
  });

  it('updates user location marker when location changes', async () => {
    const { getByTestId } = render(<InteractiveMap {...mockProps} />);
    
    const webview = getByTestId('webview');
    
    // Simular cambio de ubicación
    const newLocationMessage = {
      nativeEvent: {
        data: JSON.stringify({
          type: 'locationUpdate',
          data: {
            coords: { lat: -39.3000, lng: -71.9100 },
            distanceToVolcano: '6.0'
          }
        })
      }
    };
    
    fireEvent(webview, 'onMessage', newLocationMessage);
    
    expect(mockProps.onLocationUpdate).toHaveBeenCalledWith({
      lat: -39.3000,
      lng: -71.9100
    });
  });

  it('calculates distance to volcano correctly', () => {
    const { getByTestId } = render(<InteractiveMap {...mockProps} />);
    
    const webview = getByTestId('webview');
    const html = webview.props.source.html;
    
    expect(html).toContain('distance');
    expect(html).toContain('distanceToVolcano');
  });

  it('handles webview load error', () => {
    const { getByTestId } = render(<InteractiveMap {...mockProps} />);
    
    const webview = getByTestId('webview');
    
    // Simular error de carga
    fireEvent(webview, 'onError', { nativeEvent: { description: 'Load error' } });
    
    // Debería manejar el error sin crashear
    expect(webview).toBeTruthy();
  });

  it('includes proper map styling', () => {
    const { getByTestId } = render(<InteractiveMap {...mockProps} />);
    
    const webview = getByTestId('webview');
    const html = webview.props.source.html;
    
    expect(html).toContain('leaflet.css');
    expect(html).toContain('style');
  });

  it('handles component unmount gracefully', () => {
    const { unmount } = render(<InteractiveMap {...mockProps} />);
    
    expect(() => {
      unmount();
    }).not.toThrow();
  });
});
