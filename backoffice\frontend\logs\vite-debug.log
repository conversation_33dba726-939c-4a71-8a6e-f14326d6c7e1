[2025-06-02T19:09:22.417Z] [INFO] 
> volcano-app-frontend@1.0.0 dev
> vite

[2025-06-02T19:09:22.715Z] [ERROR] [33mThe CJS build of Vite's Node API is deprecated. See https://vite.dev/guide/troubleshooting.html#vite-cjs-node-api-deprecated for more details.[39m
[2025-06-02T19:09:22.724Z] [ERROR] 2025-06-02T19:09:22.714Z vite:config bundled config file loaded in 99.96ms
[2025-06-02T19:09:22.737Z] [ERROR] 2025-06-02T19:09:22.727Z vite:config using resolved config: {
  plugins: [
    'vite:optimized-deps',
    'vite:watch-package-data',
    'vite:pre-alias',
    'alias',
    'vite:react-babel',
    'vite:react-refresh',
    'vite:modulepreload-polyfill',
    'vite:resolve',
    'vite:html-inline-proxy',
    'vite:css',
    'vite:esbuild',
    'vite:json',
    'vite:wasm-helper',
    'vite:worker',
    'vite:asset',
    'vite:wasm-fallback',
    'vite:define',
    'vite:css-post',
    'vite:worker-import-meta-url',
    'vite:asset-import-meta-url',
    'vite:dynamic-import-vars',
    'vite:import-glob',
    'vite:client-inject',
    'vite:css-analysis',
    'vite:import-analysis'
  ],
  resolve: {
    mainFields: [ 'browser', 'module', 'jsnext:main', 'jsnext' ],
    conditions: [],
    extensions: [
      '.mjs',  '.js',
      '.mts',  '.ts',
      '.jsx',  '.tsx',
      '.json'
    ],
    dedupe: [ 'react', 'react-dom' ],
    preserveSymlinks: false,
    alias: [ [Object], [Object], [Object] ]
  },
  optimizeDeps: {
    holdUntilCrawlEnd: true,
    include: [
      'react',
      'react-dom',
      'react',
      'react-dom',
      'react/jsx-dev-runtime',
      'react/jsx-runtime'
    ],
    esbuildOptions: { preserveSymlinks: false, jsx: 'automatic' }
  },
  server: {
    preTransformRequests: true,
    port: 3000,
    host: true,
    proxy: { '/api': [Object] },
    sourcemapIgnoreList: [Function: isInNodeModules$1],
    middlewareMode: false,
    fs: {
      strict: true,
      allow: [Array],
      deny: [Array],
      cachedChecks: undefined
    }
  },
  build: {
    target: [ 'es2020', 'edge88', 'firefox78', 'chrome87', 'safari14' ],
    cssTarget: [ 'es2020', 'edge88', 'firefox78', 'chrome87', 'safari14' ],
    outDir: 'dist',
    assetsDir: 'assets',
    assetsInlineLimit: 4096,
    cssCodeSplit: true,
    sourcemap: true,
    rollupOptions: { output: [Object], onwarn: [Function: onwarn] },
    minify: 'esbuild',
    terserOptions: {},
    write: true,
    emptyOutDir: null,
    copyPublicDir: true,
    manifest: false,
    lib: false,
    ssr: false,
    ssrManifest: false,
    ssrEmitAssets: false,
    reportCompressedSize: true,
    chunkSizeWarningLimit: 500,
    watch: null,
    commonjsOptions: { include: [Array], extensions: [Array] },
    dynamicImportVarsOptions: { warnOnError: true, exclude: [Array] },
    modulePreload: { polyfill: true },
    cssMinify: true
  },
  define: { __APP_VERSION__: '"1.0.0"', __DEV__: 'true' },
  esbuild: { jsxDev: true, drop: [], jsx: 'automatic' },
  configFile: 'C:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/vite.config.ts',
  configFileDependencies: [
    'C:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/vite.config.ts'
  ],
  inlineConfig: {
    root: undefined,
    base: undefined,
    mode: undefined,
    configFile: undefined,
    logLevel: undefined,
    clearScreen: undefined,
    optimizeDeps: { force: undefined },
    server: {}
  },
  root: 'C:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend',
  base: '/',
  decodedBase: '/',
  rawBase: '/',
  publicDir: 'C:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/public',
  cacheDir: 'C:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite',
  command: 'serve',
  mode: 'development',
  ssr: {
    target: 'node',
    optimizeDeps: { noDiscovery: true, esbuildOptions: [Object] }
  },
  isWorker: false,
  mainConfig: null,
  bundleChain: [],
  isProduction: false,
  css: { lightningcss: undefined },
  preview: {
    port: undefined,
    strictPort: undefined,
    host: true,
    allowedHosts: undefined,
    https: undefined,
    open: undefined,
    proxy: { '/api': [Object] },
    cors: undefined,
    headers: undefined
  },
  envDir: 'C:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend',
  env: {
    VITE_API_BASE_URL: 'http://localhost:3001',
    VITE_API_TIMEOUT: '10000',
    VITE_APP_NAME: 'Volcano App Admin',
    VITE_APP_VERSION: '1.0.0',
    VITE_APP_DESCRIPTION: 'Panel administrativo para gestión de alertas volcánicas',
    VITE_DEFAULT_LAT: '-39.420000',
    VITE_DEFAULT_LNG: '-71.939167',
    VITE_DEFAULT_ZOOM: '10',
    VITE_ENABLE_DEVTOOLS: 'true',
    VITE_LOG_LEVEL: 'debug',
    VITE_DEBUG: 'true',
    BASE_URL: '/',
    MODE: 'development',
    DEV: true,
    PROD: false
  },
  assetsInclude: [Function: assetsInclude],
  logger: {
    hasWarned: false,
    info: [Function: info],
    warn: [Function: warn],
    warnOnce: [Function: warnOnce],
    error: [Function: error],
    clearScreen: [Function: clearScreen],
    hasErrorLogged: [Function: hasErrorLogged]
  },
  packageCache: Map(1) {
    'fnpd_C:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend' => {
      dir: 'C:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend',
      data: [Object],
      hasSideEffects: [Function: hasSideEffects],
      webResolvedImports: {},
      nodeResolvedImports: {},
      setResolvedCache: [Function: setResolvedCache],
      getResolvedCache: [Function: getResolvedCache]
    },
    set: [Function (anonymous)]
  },
  createResolver: [Function: createResolver],
  worker: { format: 'iife', plugins: '() => plugins', rollupOptions: {} },
  appType: 'spa',
  experimental: { importGlobRestoreExtension: false, hmrPartialAccept: false },
  webSocketToken: 's0gwEWQd0rfb',
  additionalAllowedHosts: [],
  getSortedPlugins: [Function: getSortedPlugins],
  getSortedPluginHooks: [Function: getSortedPluginHooks]
}
[2025-06-02T19:09:22.756Z] [ERROR] 2025-06-02T19:09:22.746Z vite:deps Hash is consistent. Skipping. Use --force to override.
[2025-06-02T19:09:22.764Z] [INFO] 
  [32m[1mVITE[22m v5.4.19[39m  [2mready in [0m[1m255[22m[2m[0m ms[22m

[2025-06-02T19:09:22.764Z] [INFO]   [32m➜[39m  [1mLocal[22m:   [36mhttp://localhost:[1m3000[22m/[39m
  [32m➜[39m  [1mNetwork[22m: [36mhttp://***********:[1m3000[22m/[39m
[2m[32m  ➜[39m[22m[2m  press [22m[1mh + enter[22m[2m to show help[22m
[2025-06-02T19:10:57.068Z] [ERROR] 2025-06-02T19:10:57.068Z vite:html-fallback Rewriting GET / to /index.html
[2025-06-02T19:10:57.071Z] [ERROR] 2025-06-02T19:10:57.071Z vite:hmr [file change] [2mlogs/vite-debug.log[22m
[2025-06-02T19:10:57.072Z] [ERROR] 2025-06-02T19:10:57.071Z vite:hmr [no modules matched] [2mlogs/vite-debug.log[22m
[2025-06-02T19:10:57.093Z] [ERROR] 2025-06-02T19:10:57.093Z vite:time [33m29.80ms[39m [2m/index.html[22m
[2025-06-02T19:10:57.097Z] [ERROR] 2025-06-02T19:10:57.097Z vite:resolve [32m5.46ms[39m [36m/src/main.tsx[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/main.tsx[22m
[2025-06-02T19:10:57.104Z] [ERROR] 2025-06-02T19:10:57.104Z vite:load [32m6.15ms[39m [fs] [2m/src/main.tsx[22m
[2025-06-02T19:10:57.407Z] [ERROR] 2025-06-02T19:10:57.407Z vite:resolve [32m0.56ms[39m [36m/@vite/client[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/vite/dist/client/client.mjs[22m
[2025-06-02T19:10:57.410Z] [ERROR] 2025-06-02T19:10:57.410Z vite:load [32m0.24ms[39m [plugin] [2m/@react-refresh[22m
[2025-06-02T19:10:57.416Z] [ERROR] 2025-06-02T19:10:57.416Z vite:import-analysis [32m1.65ms[39m [2m[0 imports rewritten] [2m/@react-refresh[22m[2m[22m
[2025-06-02T19:10:57.417Z] [ERROR] 2025-06-02T19:10:57.416Z vite:transform [32m6.53ms[39m [2m/@react-refresh[22m
2025-06-02T19:10:57.417Z vite:time [32m7.52ms[39m [2m/@react-refresh[22m
[2025-06-02T19:10:57.422Z] [ERROR] 2025-06-02T19:10:57.422Z vite:hmr [file change] [2mlogs/vite-debug.log[22m
[2025-06-02T19:10:57.423Z] [ERROR] 2025-06-02T19:10:57.422Z vite:hmr [no modules matched] [2mlogs/vite-debug.log[22m
[2025-06-02T19:10:57.428Z] [ERROR] 2025-06-02T19:10:57.428Z vite:load [33m20.93ms[39m [fs] [2m/@vite/client[22m
[2025-06-02T19:10:57.442Z] [ERROR] 2025-06-02T19:10:57.442Z vite:resolve [32m0.48ms[39m [36m@vite/env[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/vite/dist/client/env.mjs[22m
[2025-06-02T19:10:57.444Z] [ERROR] 2025-06-02T19:10:57.444Z vite:import-analysis [32m3.17ms[39m [2m[1 imports rewritten] [2mnode_modules/vite/dist/client/client.mjs[22m[2m[22m
2025-06-02T19:10:57.444Z vite:transform [33m15.28ms[39m [2m/@vite/client[22m
[2025-06-02T19:10:57.458Z] [ERROR] 2025-06-02T19:10:57.458Z vite:time [31m51.96ms[39m [2m/@vite/client[22m
[2025-06-02T19:10:57.471Z] [ERROR] 2025-06-02T19:10:57.471Z vite:load [33m27.77ms[39m [fs] [2m/node_modules/vite/dist/client/env.mjs[22m
[2025-06-02T19:10:57.472Z] [ERROR] 2025-06-02T19:10:57.472Z vite:import-analysis [32m0.06ms[39m [2m[no imports] [2mnode_modules/vite/dist/client/env.mjs[22m[2m[22m
2025-06-02T19:10:57.472Z vite:transform [32m0.36ms[39m [2m/node_modules/vite/dist/client/env.mjs[22m
[2025-06-02T19:10:57.478Z] [ERROR] 2025-06-02T19:10:57.478Z vite:hmr [file change] [2mlogs/vite-debug.log[22m
[2025-06-02T19:10:57.479Z] [ERROR] 2025-06-02T19:10:57.478Z vite:hmr [no modules matched] [2mlogs/vite-debug.log[22m
[2025-06-02T19:10:57.479Z] [ERROR] 2025-06-02T19:10:57.480Z vite:cache [memory] [2m/node_modules/vite/dist/client/env.mjs[22m
[2025-06-02T19:10:57.480Z] [ERROR] 2025-06-02T19:10:57.480Z vite:time [32m0.95ms[39m [2m/node_modules/vite/dist/client/env.mjs[22m
[2025-06-02T19:10:57.486Z] [ERROR] 2025-06-02T19:10:57.486Z vite:resolve [32m2.68ms[39m [36m./App.tsx[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/App.tsx[22m
2025-06-02T19:10:57.486Z vite:resolve [32m3.15ms[39m [36m./index.css[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/index.css[22m
[2025-06-02T19:10:57.487Z] [ERROR] 2025-06-02T19:10:57.486Z vite:resolve [32m3.24ms[39m [36mreact/jsx-dev-runtime[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite/deps/react_jsx-dev-runtime.js?v=cbff3303[22m
2025-06-02T19:10:57.486Z vite:resolve [32m3.27ms[39m [36mreact[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite/deps/react.js?v=cbff3303[22m
2025-06-02T19:10:57.486Z vite:resolve [32m3.30ms[39m [36mreact-dom/client[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite/deps/react-dom_client.js?v=cbff3303[22m
[2025-06-02T19:10:57.488Z] [ERROR] 2025-06-02T19:10:57.488Z vite:import-analysis /node_modules/.vite/deps/react_jsx-dev-runtime.js?v=cbff3303 needs interop
[2025-06-02T19:10:57.489Z] [ERROR] 2025-06-02T19:10:57.490Z vite:import-analysis /node_modules/.vite/deps/react.js?v=cbff3303 needs interop
[2025-06-02T19:10:57.490Z] [ERROR] 2025-06-02T19:10:57.490Z vite:import-analysis /node_modules/.vite/deps/react-dom_client.js?v=cbff3303 needs interop
[2025-06-02T19:10:57.491Z] [ERROR] 2025-06-02T19:10:57.491Z vite:optimize-deps load [36mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite/deps/react_jsx-dev-runtime.js[39m
[2025-06-02T19:10:57.492Z] [ERROR] 2025-06-02T19:10:57.491Z vite:optimize-deps load [36mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite/deps/react.js[39m
2025-06-02T19:10:57.492Z vite:optimize-deps load [36mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite/deps/react-dom_client.js[39m
2025-06-02T19:10:57.492Z vite:import-analysis [32m8.79ms[39m [2m[5 imports rewritten] [2msrc/main.tsx[22m[2m[22m
[2025-06-02T19:10:57.494Z] [ERROR] 2025-06-02T19:10:57.495Z vite:transform [31m389.58ms[39m [2m/src/main.tsx[22m
[2025-06-02T19:10:57.495Z] [ERROR] 2025-06-02T19:10:57.495Z vite:time [31m86.31ms[39m [2m/src/main.tsx[22m
[2025-06-02T19:10:57.508Z] [ERROR] 2025-06-02T19:10:57.508Z vite:load [33m16.87ms[39m [plugin] [2m/node_modules/.vite/deps/react-dom_client.js?v=cbff3303[22m
[2025-06-02T19:10:57.509Z] [ERROR] 2025-06-02T19:10:57.509Z vite:resolve [32m0.48ms[39m [36m./chunk-PJEEZAML.js[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite/deps/chunk-PJEEZAML.js?v=cbff3303[22m
[2025-06-02T19:10:57.510Z] [ERROR] 2025-06-02T19:10:57.509Z vite:resolve [32m0.77ms[39m [36m./chunk-DRWLMN53.js[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite/deps/chunk-DRWLMN53.js?v=cbff3303[22m
2025-06-02T19:10:57.510Z vite:resolve [32m0.86ms[39m [36m./chunk-G3PMV62Z.js[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite/deps/chunk-G3PMV62Z.js?v=cbff3303[22m
[2025-06-02T19:10:57.511Z] [ERROR] 2025-06-02T19:10:57.510Z vite:optimize-deps load [36mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite/deps/chunk-PJEEZAML.js[39m
2025-06-02T19:10:57.510Z vite:optimize-deps load [36mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite/deps/chunk-DRWLMN53.js[39m
2025-06-02T19:10:57.510Z vite:optimize-deps load [36mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite/deps/chunk-G3PMV62Z.js[39m
2025-06-02T19:10:57.511Z vite:import-analysis [32m2.15ms[39m [2m[3 imports rewritten] [2mnode_modules/.vite/deps/react-dom_client.js?v=cbff3303[22m[2m[22m
2025-06-02T19:10:57.511Z vite:transform [32m2.38ms[39m [2m/node_modules/.vite/deps/react-dom_client.js?v=cbff3303[22m
[2025-06-02T19:10:57.511Z] [ERROR] 2025-06-02T19:10:57.511Z vite:load [33m19.82ms[39m [plugin] [2m/node_modules/.vite/deps/react_jsx-dev-runtime.js?v=cbff3303[22m
[2025-06-02T19:10:57.512Z] [ERROR] 2025-06-02T19:10:57.513Z vite:import-analysis [32m1.31ms[39m [2m[2 imports rewritten] [2mnode_modules/.vite/deps/react_jsx-dev-runtime.js?v=cbff3303[22m[2m[22m
[2025-06-02T19:10:57.513Z] [ERROR] 2025-06-02T19:10:57.513Z vite:transform [32m1.69ms[39m [2m/node_modules/.vite/deps/react_jsx-dev-runtime.js?v=cbff3303[22m
2025-06-02T19:10:57.513Z vite:load [33m21.95ms[39m [fs] [2m/src/App.tsx[22m
[2025-06-02T19:10:57.603Z] [ERROR] 2025-06-02T19:10:57.603Z vite:load [31m111.35ms[39m [fs] [2m/src/index.css[22m
[2025-06-02T19:10:57.606Z] [ERROR] 2025-06-02T19:10:57.606Z vite:load [31m115.33ms[39m [plugin] [2m/node_modules/.vite/deps/react.js?v=cbff3303[22m
[2025-06-02T19:10:57.607Z] [ERROR] 2025-06-02T19:10:57.607Z vite:import-analysis [32m0.74ms[39m [2m[2 imports rewritten] [2mnode_modules/.vite/deps/react.js?v=cbff3303[22m[2m[22m
[2025-06-02T19:10:57.608Z] [ERROR] 2025-06-02T19:10:57.607Z vite:transform [32m0.93ms[39m [2m/node_modules/.vite/deps/react.js?v=cbff3303[22m
[2025-06-02T19:10:57.609Z] [ERROR] 2025-06-02T19:10:57.609Z vite:hmr [file change] [2mlogs/vite-debug.log[22m
2025-06-02T19:10:57.609Z vite:hmr [no modules matched] [2mlogs/vite-debug.log[22m
[2025-06-02T19:10:57.617Z] [ERROR] 2025-06-02T19:10:57.617Z vite:resolve [32m5.02ms[39m [36m./components/alerts/AlertDialog[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/components/alerts/AlertDialog.tsx[22m
[2025-06-02T19:10:57.617Z] [ERROR] 2025-06-02T19:10:57.617Z vite:resolve [32m5.14ms[39m [36m./components/alerts/AlertsTable[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/components/alerts/AlertsTable.tsx[22m
2025-06-02T19:10:57.617Z vite:resolve [32m5.20ms[39m [36m./components/dashboard/DashboardStats[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/components/dashboard/DashboardStats.tsx[22m
2025-06-02T19:10:57.617Z vite:resolve [32m5.23ms[39m [36m./components/debug/DebugPanel[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/components/debug/DebugPanel.tsx[22m
2025-06-02T19:10:57.617Z vite:resolve [32m5.25ms[39m [36m./components/layout/AppLayout[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/components/layout/AppLayout.tsx[22m
2025-06-02T19:10:57.617Z vite:resolve [32m5.28ms[39m [36m./components/ui/toaster[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/components/ui/toaster.tsx[22m
2025-06-02T19:10:57.617Z vite:resolve [32m5.30ms[39m [36m./components/ZoneModal[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/components/ZoneModal.tsx[22m
2025-06-02T19:10:57.617Z vite:resolve [32m5.32ms[39m [36m./components/zones/ZoneManagementClean[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/components/zones/ZoneManagementClean.tsx[22m
2025-06-02T19:10:57.617Z vite:resolve [32m5.34ms[39m [36m./components/zones/ZonesTable[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/components/zones/ZonesTable.tsx[22m
2025-06-02T19:10:57.617Z vite:resolve [32m5.35ms[39m [36m./hooks/use-toast[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/hooks/use-toast.ts[22m
2025-06-02T19:10:57.617Z vite:resolve [32m5.37ms[39m [36m./utils/debug[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/utils/debug.ts[22m
2025-06-02T19:10:57.617Z vite:cache [memory] [2m/@react-refresh[22m
2025-06-02T19:10:57.617Z vite:resolve [32m5.45ms[39m [36maxios[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite/deps/axios.js?v=cbff3303[22m
[2025-06-02T19:10:57.618Z] [ERROR] 2025-06-02T19:10:57.618Z vite:import-analysis /node_modules/.vite/deps/react_jsx-dev-runtime.js?v=cbff3303 needs interop
2025-06-02T19:10:57.618Z vite:import-analysis /node_modules/.vite/deps/react.js?v=cbff3303 needs interop
2025-06-02T19:10:57.618Z vite:cache [memory] [2m/node_modules/.vite/deps/react_jsx-dev-runtime.js?v=cbff3303[22m
2025-06-02T19:10:57.618Z vite:cache [memory] [2m/node_modules/.vite/deps/react.js?v=cbff3303[22m
[2025-06-02T19:10:57.619Z] [ERROR] 2025-06-02T19:10:57.619Z vite:hmr [self-accepts] [2msrc/App.tsx[22m
[2025-06-02T19:10:57.619Z] [ERROR] 2025-06-02T19:10:57.619Z vite:optimize-deps load [36mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite/deps/axios.js[39m
[2025-06-02T19:10:57.620Z] [ERROR] 2025-06-02T19:10:57.619Z vite:import-analysis [32m8.24ms[39m [2m[15 imports rewritten] [2msrc/App.tsx[22m[2m[22m
[2025-06-02T19:10:57.626Z] [ERROR] 2025-06-02T19:10:57.626Z vite:transform [31m112.51ms[39m [2m/src/App.tsx[22m
[2025-06-02T19:10:57.627Z] [ERROR] 2025-06-02T19:10:57.627Z vite:time [33m18.31ms[39m [2m/src/App.tsx[22m
[2025-06-02T19:10:57.645Z] [ERROR] 2025-06-02T19:10:57.645Z vite:load [31m134.24ms[39m [plugin] [2m/node_modules/.vite/deps/chunk-DRWLMN53.js?v=cbff3303[22m
[2025-06-02T19:10:57.647Z] [ERROR] 2025-06-02T19:10:57.647Z vite:import-analysis [32m1.34ms[39m [2m[1 imports rewritten] [2mnode_modules/.vite/deps/chunk-DRWLMN53.js?v=cbff3303[22m[2m[22m
[2025-06-02T19:10:57.647Z] [ERROR] 2025-06-02T19:10:57.647Z vite:transform [32m2.51ms[39m [2m/node_modules/.vite/deps/chunk-DRWLMN53.js?v=cbff3303[22m
2025-06-02T19:10:57.648Z vite:load [31m137.40ms[39m [plugin] [2m/node_modules/.vite/deps/chunk-G3PMV62Z.js?v=cbff3303[22m
[2025-06-02T19:10:57.648Z] [ERROR] 2025-06-02T19:10:57.648Z vite:import-analysis [32m0.08ms[39m [2m[no imports] [2mnode_modules/.vite/deps/chunk-G3PMV62Z.js?v=cbff3303[22m[2m[22m
2025-06-02T19:10:57.648Z vite:transform [32m0.27ms[39m [2m/node_modules/.vite/deps/chunk-G3PMV62Z.js?v=cbff3303[22m
[2025-06-02T19:10:57.657Z] [ERROR] 2025-06-02T19:10:57.658Z vite:load [31m147.31ms[39m [plugin] [2m/node_modules/.vite/deps/chunk-PJEEZAML.js?v=cbff3303[22m
[2025-06-02T19:10:57.668Z] [ERROR] 2025-06-02T19:10:57.668Z vite:cache [memory] [2m/node_modules/.vite/deps/chunk-DRWLMN53.js?v=cbff3303[22m
2025-06-02T19:10:57.668Z vite:cache [memory] [2m/node_modules/.vite/deps/chunk-G3PMV62Z.js?v=cbff3303[22m
[2025-06-02T19:10:57.669Z] [ERROR] 2025-06-02T19:10:57.668Z vite:import-analysis [32m7.99ms[39m [2m[2 imports rewritten] [2mnode_modules/.vite/deps/chunk-PJEEZAML.js?v=cbff3303[22m[2m[22m
2025-06-02T19:10:57.668Z vite:transform [33m10.57ms[39m [2m/node_modules/.vite/deps/chunk-PJEEZAML.js?v=cbff3303[22m
[2025-06-02T19:10:57.673Z] [ERROR] 2025-06-02T19:10:57.673Z vite:load [31m53.64ms[39m [plugin] [2m/node_modules/.vite/deps/axios.js?v=cbff3303[22m
[2025-06-02T19:10:57.675Z] [ERROR] 2025-06-02T19:10:57.675Z vite:cache [memory] [2m/node_modules/.vite/deps/chunk-G3PMV62Z.js?v=cbff3303[22m
[2025-06-02T19:10:57.675Z] [ERROR] 2025-06-02T19:10:57.675Z vite:import-analysis [32m1.65ms[39m [2m[1 imports rewritten] [2mnode_modules/.vite/deps/axios.js?v=cbff3303[22m[2m[22m
2025-06-02T19:10:57.675Z vite:transform [32m2.03ms[39m [2m/node_modules/.vite/deps/axios.js?v=cbff3303[22m
2025-06-02T19:10:57.675Z vite:load [31m56.34ms[39m [fs] [2m/src/components/alerts/AlertsTable.tsx[22m
[2025-06-02T19:10:57.707Z] [ERROR] 2025-06-02T19:10:57.707Z vite:load [31m87.77ms[39m [fs] [2m/src/components/alerts/AlertDialog.tsx[22m
[2025-06-02T19:10:57.741Z] [ERROR] 2025-06-02T19:10:57.741Z vite:load [31m122.08ms[39m [fs] [2m/src/components/dashboard/DashboardStats.tsx[22m
[2025-06-02T19:10:57.785Z] [ERROR] 2025-06-02T19:10:57.785Z vite:load [31m166.34ms[39m [fs] [2m/src/components/debug/DebugPanel.tsx[22m
[2025-06-02T19:10:57.801Z] [ERROR] 2025-06-02T19:10:57.801Z vite:load [31m181.53ms[39m [fs] [2m/src/components/layout/AppLayout.tsx[22m
[2025-06-02T19:10:57.810Z] [ERROR] 2025-06-02T19:10:57.810Z vite:load [31m190.70ms[39m [fs] [2m/src/components/ui/toaster.tsx[22m
[2025-06-02T19:10:57.816Z] [ERROR] 2025-06-02T19:10:57.816Z vite:load [31m196.84ms[39m [fs] [2m/src/components/ZoneModal.tsx[22m
[2025-06-02T19:10:57.846Z] [ERROR] 2025-06-02T19:10:57.846Z vite:load [31m226.54ms[39m [fs] [2m/src/hooks/use-toast.ts[22m
[2025-06-02T19:10:57.846Z] [ERROR] 2025-06-02T19:10:57.846Z vite:load [31m226.82ms[39m [fs] [2m/src/components/zones/ZonesTable.tsx[22m
[2025-06-02T19:10:57.856Z] [ERROR] 2025-06-02T19:10:57.856Z vite:load [31m236.94ms[39m [fs] [2m/src/components/zones/ZoneManagementClean.tsx[22m
[2025-06-02T19:10:57.884Z] [ERROR] 2025-06-02T19:10:57.884Z vite:load [31m264.76ms[39m [fs] [2m/src/utils/debug.ts[22m
[2025-06-02T19:10:57.885Z] [ERROR] 2025-06-02T19:10:57.884Z vite:hmr [file change] [2mlogs/vite-debug.log[22m
2025-06-02T19:10:57.884Z vite:hmr [no modules matched] [2mlogs/vite-debug.log[22m
[2025-06-02T19:10:57.910Z] [ERROR] 2025-06-02T19:10:57.910Z vite:resolve [32m0.86ms[39m [36mC:\Users\<USER>\Documents\augment-projects\volcanoApp\volcanoApp\backoffice\frontend\src/hooks/use-toast[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/hooks/use-toast.ts[22m
[2025-06-02T19:10:57.911Z] [ERROR] 2025-06-02T19:10:57.910Z vite:cache [memory] [2m/@react-refresh[22m
2025-06-02T19:10:57.910Z vite:resolve [32m1.44ms[39m [36mlucide-react[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite/deps/lucide-react.js?v=cbff3303[22m
2025-06-02T19:10:57.910Z vite:resolve [32m1.22ms[39m [36m@/hooks/use-toast[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/hooks/use-toast.ts[22m
2025-06-02T19:10:57.910Z vite:import-analysis /node_modules/.vite/deps/react_jsx-dev-runtime.js?v=cbff3303 needs interop
[2025-06-02T19:10:57.911Z] [ERROR] 2025-06-02T19:10:57.911Z vite:import-analysis /node_modules/.vite/deps/react.js?v=cbff3303 needs interop
2025-06-02T19:10:57.911Z vite:cache [memory] [2m/node_modules/.vite/deps/react_jsx-dev-runtime.js?v=cbff3303[22m
2025-06-02T19:10:57.911Z vite:cache [memory] [2m/node_modules/.vite/deps/react.js?v=cbff3303[22m
2025-06-02T19:10:57.911Z vite:optimize-deps load [36mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite/deps/lucide-react.js[39m
2025-06-02T19:10:57.911Z vite:hmr [self-accepts] [2msrc/components/ZoneModal.tsx[22m
[2025-06-02T19:10:57.912Z] [ERROR] 2025-06-02T19:10:57.912Z vite:import-analysis [32m3.31ms[39m [2m[5 imports rewritten] [2msrc/components/ZoneModal.tsx[22m[2m[22m
[2025-06-02T19:10:57.916Z] [ERROR] 2025-06-02T19:10:57.916Z vite:transform [31m99.51ms[39m [2m/src/components/ZoneModal.tsx[22m
[2025-06-02T19:10:58.254Z] [ERROR] 2025-06-02T19:10:58.254Z vite:esbuild esbuild error with options used:  {
  sourcemap: true,
  sourcefile: 'C:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/utils/debug.ts',
  target: 'esnext',
  charset: 'utf8',
  jsxDev: true,
  drop: [],
  jsx: 'automatic',
  minify: false,
  minifyIdentifiers: false,
  minifySyntax: false,
  minifyWhitespace: false,
  treeShaking: false,
  keepNames: false,
  supported: { 'dynamic-import': true, 'import-meta': true },
  loader: 'ts',
  tsconfigRaw: {
    compilerOptions: { jsx: undefined, target: 'ES2020', useDefineForClassFields: true }
  }
}
[2025-06-02T19:10:58.270Z] [ERROR] [2m3:10:58 p. m.[22m [31m[1m[vite][22m[39m Pre-transform error: Transform failed with 1 error:
C:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/utils/debug.ts:96:7: ERROR: Expected ";" but found ")"
[2025-06-02T19:10:58.274Z] [ERROR] 2025-06-02T19:10:58.274Z vite:resolve [32m3.32ms[39m [36m../../hooks/use-toast[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/hooks/use-toast.ts[22m
2025-06-02T19:10:58.274Z vite:resolve [32m3.43ms[39m [36m./toast[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/components/ui/toast.tsx[22m
[2025-06-02T19:10:58.274Z] [ERROR] 2025-06-02T19:10:58.274Z vite:resolve [32m3.19ms[39m [36m../../lib/utils[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/lib/utils.ts[22m
2025-06-02T19:10:58.274Z vite:resolve [32m3.20ms[39m [36m../ui/badge[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/components/ui/badge.tsx[22m
2025-06-02T19:10:58.274Z vite:resolve [32m3.21ms[39m [36m../ui/button[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/components/ui/button.tsx[22m
2025-06-02T19:10:58.274Z vite:resolve [32m3.23ms[39m [36m../ui/card[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/components/ui/card.tsx[22m
2025-06-02T19:10:58.274Z vite:resolve [32m3.23ms[39m [36m../ui/sidebar[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/components/ui/sidebar.tsx[22m
2025-06-02T19:10:58.274Z vite:cache [memory] [2m/@react-refresh[22m
2025-06-02T19:10:58.274Z vite:import-analysis /node_modules/.vite/deps/react_jsx-dev-runtime.js?v=cbff3303 needs interop
2025-06-02T19:10:58.275Z vite:import-analysis /node_modules/.vite/deps/react.js?v=cbff3303 needs interop
[2025-06-02T19:10:58.275Z] [ERROR] 2025-06-02T19:10:58.275Z vite:import-analysis /node_modules/.vite/deps/react_jsx-dev-runtime.js?v=cbff3303 needs interop
2025-06-02T19:10:58.275Z vite:import-analysis /node_modules/.vite/deps/react.js?v=cbff3303 needs interop
2025-06-02T19:10:58.275Z vite:cache [memory] [2m/node_modules/.vite/deps/react_jsx-dev-runtime.js?v=cbff3303[22m
2025-06-02T19:10:58.275Z vite:hmr [self-accepts] [2msrc/components/ui/toaster.tsx[22m
2025-06-02T19:10:58.275Z vite:cache [memory] [2m/node_modules/.vite/deps/react.js?v=cbff3303[22m
2025-06-02T19:10:58.275Z vite:hmr [self-accepts] [2msrc/components/layout/AppLayout.tsx[22m
[2025-06-02T19:10:58.275Z] [ERROR] 2025-06-02T19:10:58.275Z vite:import-analysis [32m5.10ms[39m [2m[4 imports rewritten] [2msrc/components/ui/toaster.tsx[22m[2m[22m
2025-06-02T19:10:58.275Z vite:import-analysis [32m5.13ms[39m [2m[1 imports rewritten] [2msrc/hooks/use-toast.ts[22m[2m[22m
2025-06-02T19:10:58.275Z vite:import-analysis [32m5.15ms[39m [2m[9 imports rewritten] [2msrc/components/layout/AppLayout.tsx[22m[2m[22m
[2025-06-02T19:10:58.276Z] [ERROR] 2025-06-02T19:10:58.277Z vite:transform [31m466.72ms[39m [2m/src/components/ui/toaster.tsx[22m
[2025-06-02T19:10:58.277Z] [ERROR] 2025-06-02T19:10:58.277Z vite:transform [31m430.95ms[39m [2m/src/hooks/use-toast.ts[22m
2025-06-02T19:10:58.277Z vite:transform [31m476.00ms[39m [2m/src/components/layout/AppLayout.tsx[22m
2025-06-02T19:10:58.277Z vite:time [31m604.64ms[39m [2m/src/components/layout/AppLayout.tsx[22m
[2025-06-02T19:10:58.283Z] [ERROR] 2025-06-02T19:10:58.283Z vite:resolve [32m2.71ms[39m [36m../ui/table[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/components/ui/table.tsx[22m
2025-06-02T19:10:58.283Z vite:cache [memory] [2m/@react-refresh[22m
[2025-06-02T19:10:58.284Z] [ERROR] 2025-06-02T19:10:58.283Z vite:import-analysis /node_modules/.vite/deps/react_jsx-dev-runtime.js?v=cbff3303 needs interop
2025-06-02T19:10:58.284Z vite:cache [memory] [2m/node_modules/.vite/deps/react_jsx-dev-runtime.js?v=cbff3303[22m
2025-06-02T19:10:58.284Z vite:hmr [self-accepts] [2msrc/components/alerts/AlertsTable.tsx[22m
[2025-06-02T19:10:58.284Z] [ERROR] 2025-06-02T19:10:58.284Z vite:import-analysis [32m4.38ms[39m [2m[8 imports rewritten] [2msrc/components/alerts/AlertsTable.tsx[22m[2m[22m
[2025-06-02T19:10:58.285Z] [ERROR] 2025-06-02T19:10:58.285Z vite:transform [31m610.00ms[39m [2m/src/components/alerts/AlertsTable.tsx[22m
[2025-06-02T19:10:58.286Z] [ERROR] 2025-06-02T19:10:58.286Z vite:time [31m629.87ms[39m [2m/src/components/alerts/AlertsTable.tsx[22m
[2025-06-02T19:10:58.290Z] [ERROR] 2025-06-02T19:10:58.290Z vite:resolve [32m2.02ms[39m [36m../ui/tabs[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/components/ui/tabs.tsx[22m
[2025-06-02T19:10:58.291Z] [ERROR] 2025-06-02T19:10:58.290Z vite:resolve [32m2.24ms[39m [36m../../utils/debug[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/utils/debug.ts[22m
2025-06-02T19:10:58.290Z vite:cache [memory] [2m/@react-refresh[22m
2025-06-02T19:10:58.290Z vite:import-analysis /node_modules/.vite/deps/react_jsx-dev-runtime.js?v=cbff3303 needs interop
2025-06-02T19:10:58.291Z vite:import-analysis /node_modules/.vite/deps/react.js?v=cbff3303 needs interop
2025-06-02T19:10:58.291Z vite:cache [memory] [2m/node_modules/.vite/deps/react_jsx-dev-runtime.js?v=cbff3303[22m
2025-06-02T19:10:58.291Z vite:cache [memory] [2m/node_modules/.vite/deps/react.js?v=cbff3303[22m
2025-06-02T19:10:58.291Z vite:hmr [self-accepts] [2msrc/components/debug/DebugPanel.tsx[22m
2025-06-02T19:10:58.291Z vite:import-analysis [32m3.76ms[39m [2m[9 imports rewritten] [2msrc/components/debug/DebugPanel.tsx[22m[2m[22m
[2025-06-02T19:10:58.292Z] [ERROR] 2025-06-02T19:10:58.292Z vite:transform [31m506.14ms[39m [2m/src/components/debug/DebugPanel.tsx[22m
[2025-06-02T19:10:58.292Z] [ERROR] 2025-06-02T19:10:58.292Z vite:time [31m620.03ms[39m [2m/src/components/debug/DebugPanel.tsx[22m
[2025-06-02T19:10:58.297Z] [ERROR] 2025-06-02T19:10:58.297Z vite:cache [memory] [2m/@react-refresh[22m
[2025-06-02T19:10:58.298Z] [ERROR] 2025-06-02T19:10:58.298Z vite:import-analysis /node_modules/.vite/deps/react_jsx-dev-runtime.js?v=cbff3303 needs interop
2025-06-02T19:10:58.298Z vite:cache [memory] [2m/node_modules/.vite/deps/react_jsx-dev-runtime.js?v=cbff3303[22m
2025-06-02T19:10:58.298Z vite:hmr [self-accepts] [2msrc/components/zones/ZonesTable.tsx[22m
2025-06-02T19:10:58.298Z vite:import-analysis [32m3.67ms[39m [2m[8 imports rewritten] [2msrc/components/zones/ZonesTable.tsx[22m[2m[22m
[2025-06-02T19:10:58.299Z] [ERROR] 2025-06-02T19:10:58.299Z vite:transform [31m453.45ms[39m [2m/src/components/zones/ZonesTable.tsx[22m
[2025-06-02T19:10:58.304Z] [ERROR] 2025-06-02T19:10:58.304Z vite:resolve [32m3.07ms[39m [36m../ui/dialog[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/components/ui/dialog.tsx[22m
[2025-06-02T19:10:58.305Z] [ERROR] 2025-06-02T19:10:58.304Z vite:resolve [32m3.32ms[39m [36m../ui/input[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/components/ui/input.tsx[22m
2025-06-02T19:10:58.304Z vite:resolve [32m3.37ms[39m [36m../ui/label[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/components/ui/label.tsx[22m
2025-06-02T19:10:58.304Z vite:resolve [32m3.39ms[39m [36m../ui/select[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/components/ui/select.tsx[22m
2025-06-02T19:10:58.304Z vite:resolve [32m3.41ms[39m [36m../ui/textarea[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/components/ui/textarea.tsx[22m
2025-06-02T19:10:58.304Z vite:cache [memory] [2m/@react-refresh[22m
2025-06-02T19:10:58.305Z vite:import-analysis /node_modules/.vite/deps/react_jsx-dev-runtime.js?v=cbff3303 needs interop
2025-06-02T19:10:58.305Z vite:import-analysis /node_modules/.vite/deps/react.js?v=cbff3303 needs interop
2025-06-02T19:10:58.305Z vite:cache [memory] [2m/node_modules/.vite/deps/react_jsx-dev-runtime.js?v=cbff3303[22m
2025-06-02T19:10:58.305Z vite:cache [memory] [2m/node_modules/.vite/deps/react.js?v=cbff3303[22m
2025-06-02T19:10:58.305Z vite:hmr [self-accepts] [2msrc/components/alerts/AlertDialog.tsx[22m
[2025-06-02T19:10:58.306Z] [ERROR] 2025-06-02T19:10:58.305Z vite:import-analysis [32m5.03ms[39m [2m[12 imports rewritten] [2msrc/components/alerts/AlertDialog.tsx[22m[2m[22m
[2025-06-02T19:10:58.306Z] [ERROR] 2025-06-02T19:10:58.306Z vite:transform [31m599.52ms[39m [2m/src/components/alerts/AlertDialog.tsx[22m
[2025-06-02T19:10:58.307Z] [ERROR] 2025-06-02T19:10:58.307Z vite:time [31m651.75ms[39m [2m/src/components/alerts/AlertDialog.tsx[22m
[2025-06-02T19:10:58.311Z] [ERROR] 2025-06-02T19:10:58.311Z vite:resolve [32m2.46ms[39m [36m../ui/progress[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/components/ui/progress.tsx[22m
2025-06-02T19:10:58.311Z vite:cache [memory] [2m/@react-refresh[22m
[2025-06-02T19:10:58.312Z] [ERROR] 2025-06-02T19:10:58.312Z vite:import-analysis /node_modules/.vite/deps/react_jsx-dev-runtime.js?v=cbff3303 needs interop
2025-06-02T19:10:58.312Z vite:cache [memory] [2m/node_modules/.vite/deps/react_jsx-dev-runtime.js?v=cbff3303[22m
2025-06-02T19:10:58.312Z vite:hmr [self-accepts] [2msrc/components/dashboard/DashboardStats.tsx[22m
2025-06-02T19:10:58.312Z vite:import-analysis [32m3.93ms[39m [2m[7 imports rewritten] [2msrc/components/dashboard/DashboardStats.tsx[22m[2m[22m
[2025-06-02T19:10:58.314Z] [ERROR] 2025-06-02T19:10:58.314Z vite:transform [31m572.86ms[39m [2m/src/components/dashboard/DashboardStats.tsx[22m
[2025-06-02T19:10:58.315Z] [ERROR] 2025-06-02T19:10:58.315Z vite:time [31m642.42ms[39m [2m/src/components/dashboard/DashboardStats.tsx[22m
[2025-06-02T19:10:58.318Z] [ERROR] 2025-06-02T19:10:58.318Z vite:resolve [32m1.84ms[39m [36mC:\Users\<USER>\Documents\augment-projects\volcanoApp\volcanoApp\backoffice\frontend\src/components/ui/button[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/components/ui/button.tsx[22m
2025-06-02T19:10:58.319Z vite:resolve [32m1.94ms[39m [36mC:\Users\<USER>\Documents\augment-projects\volcanoApp\volcanoApp\backoffice\frontend\src/components/ui/card[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/components/ui/card.tsx[22m
[2025-06-02T19:10:58.319Z] [ERROR] 2025-06-02T19:10:58.319Z vite:resolve [32m1.96ms[39m [36mC:\Users\<USER>\Documents\augment-projects\volcanoApp\volcanoApp\backoffice\frontend\src/components/ui/tabs[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/components/ui/tabs.tsx[22m
2025-06-02T19:10:58.319Z vite:resolve [32m1.97ms[39m [36mC:\Users\<USER>\Documents\augment-projects\volcanoApp\volcanoApp\backoffice\frontend\src/components/ui/input[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/components/ui/input.tsx[22m
2025-06-02T19:10:58.319Z vite:resolve [32m2.01ms[39m [36mC:\Users\<USER>\Documents\augment-projects\volcanoApp\volcanoApp\backoffice\frontend\src/components/ui/select[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/components/ui/select.tsx[22m
2025-06-02T19:10:58.319Z vite:cache [memory] [2m/@react-refresh[22m
2025-06-02T19:10:58.319Z vite:resolve [32m2.13ms[39m [36m@/components/ui/button[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/components/ui/button.tsx[22m
2025-06-02T19:10:58.319Z vite:resolve [32m2.16ms[39m [36m@/components/ui/card[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/components/ui/card.tsx[22m
2025-06-02T19:10:58.319Z vite:resolve [32m2.17ms[39m [36m@/components/ui/tabs[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/components/ui/tabs.tsx[22m
2025-06-02T19:10:58.319Z vite:resolve [32m2.19ms[39m [36m@/components/ui/input[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/components/ui/input.tsx[22m
2025-06-02T19:10:58.319Z vite:resolve [32m2.20ms[39m [36m@/components/ui/select[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/components/ui/select.tsx[22m
2025-06-02T19:10:58.319Z vite:import-analysis /node_modules/.vite/deps/react_jsx-dev-runtime.js?v=cbff3303 needs interop
2025-06-02T19:10:58.319Z vite:import-analysis /node_modules/.vite/deps/react.js?v=cbff3303 needs interop
[2025-06-02T19:10:58.320Z] [ERROR] 2025-06-02T19:10:58.319Z vite:cache [memory] [2m/node_modules/.vite/deps/react_jsx-dev-runtime.js?v=cbff3303[22m
2025-06-02T19:10:58.319Z vite:cache [memory] [2m/node_modules/.vite/deps/react.js?v=cbff3303[22m
2025-06-02T19:10:58.320Z vite:cache [memory] [2m/src/hooks/use-toast.ts[22m
2025-06-02T19:10:58.320Z vite:hmr [self-accepts] [2msrc/components/zones/ZoneManagementClean.tsx[22m
2025-06-02T19:10:58.320Z vite:import-analysis [32m3.58ms[39m [2m[10 imports rewritten] [2msrc/components/zones/ZoneManagementClean.tsx[22m[2m[22m
[2025-06-02T19:10:58.320Z] [ERROR] 2025-06-02T19:10:58.321Z vite:transform [31m464.43ms[39m [2m/src/components/zones/ZoneManagementClean.tsx[22m
[2025-06-02T19:10:58.326Z] [ERROR] 2025-06-02T19:10:58.326Z vite:hmr [file change] [2mlogs/vite-debug.log[22m
[2025-06-02T19:10:58.327Z] [ERROR] 2025-06-02T19:10:58.326Z vite:hmr [no modules matched] [2mlogs/vite-debug.log[22m
[2025-06-02T19:10:58.329Z] [ERROR] 2025-06-02T19:10:58.329Z vite:cache [304] [2m/src/components/ui/toaster.tsx[22m
2025-06-02T19:10:58.329Z vite:time [32m0.18ms[39m [2m/src/components/ui/toaster.tsx[22m
[2025-06-02T19:10:58.330Z] [ERROR] 2025-06-02T19:10:58.330Z vite:cache [304] [2m/src/components/ZoneModal.tsx[22m
2025-06-02T19:10:58.330Z vite:time [32m0.11ms[39m [2m/src/components/ZoneModal.tsx[22m
[2025-06-02T19:10:58.330Z] [ERROR] 2025-06-02T19:10:58.330Z vite:cache [304] [2m/src/components/zones/ZoneManagementClean.tsx[22m
2025-06-02T19:10:58.330Z vite:time [32m0.05ms[39m [2m/src/components/zones/ZoneManagementClean.tsx[22m
[2025-06-02T19:10:58.331Z] [ERROR] 2025-06-02T19:10:58.330Z vite:cache [304] [2m/src/components/zones/ZonesTable.tsx[22m
2025-06-02T19:10:58.330Z vite:time [32m0.05ms[39m [2m/src/components/zones/ZonesTable.tsx[22m
2025-06-02T19:10:58.331Z vite:cache [304] [2m/src/hooks/use-toast.ts[22m
2025-06-02T19:10:58.331Z vite:time [32m0.05ms[39m [2m/src/hooks/use-toast.ts[22m
[2025-06-02T19:10:58.350Z] [ERROR] 2025-06-02T19:10:58.350Z vite:hmr [file change] [2mlogs/vite-errors.log[22m
[2025-06-02T19:10:58.351Z] [ERROR] 2025-06-02T19:10:58.350Z vite:hmr [no modules matched] [2mlogs/vite-errors.log[22m
[2025-06-02T19:10:58.640Z] [ERROR] 2025-06-02T19:10:58.641Z vite:resolve [32m0.30ms[39m [36m/index.html[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/index.html[22m
[2025-06-02T19:10:58.641Z] [ERROR] 2025-06-02T19:10:58.641Z vite:resolve [32m0.18ms[39m [36m/src/vite-env.d.ts[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/vite-env.d.ts[22m
2025-06-02T19:10:58.641Z vite:resolve [32m0.22ms[39m [36m/src/hooks/useAuth.ts[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/hooks/useAuth.ts[22m
[2025-06-02T19:10:58.642Z] [ERROR] 2025-06-02T19:10:58.641Z vite:resolve [32m0.23ms[39m [36m/src/hooks/useMapDrawing.ts[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/hooks/useMapDrawing.ts[22m
2025-06-02T19:10:58.642Z vite:resolve [32m0.27ms[39m [36m/src/services/authService.ts[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/services/authService.ts[22m
[2025-06-02T19:10:58.642Z] [ERROR] 2025-06-02T19:10:58.642Z vite:resolve [32m0.29ms[39m [36m/src/services/geometry.ts[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/services/geometry.ts[22m
[2025-06-02T19:10:58.643Z] [ERROR] 2025-06-02T19:10:58.643Z vite:resolve [32m0.18ms[39m [36m/src/types/alerts.ts[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/types/alerts.ts[22m
2025-06-02T19:10:58.643Z vite:resolve [32m0.18ms[39m [36m/src/types/auth.ts[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/types/auth.ts[22m
[2025-06-02T19:10:58.643Z] [ERROR] 2025-06-02T19:10:58.643Z vite:resolve [32m0.30ms[39m [36m/src/components/AlertModal.tsx[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/components/AlertModal.tsx[22m
[2025-06-02T19:10:58.643Z] [ERROR] 2025-06-02T19:10:58.644Z vite:resolve [32m0.18ms[39m [36m/src/components/InteractiveMap.tsx[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/components/InteractiveMap.tsx[22m
[2025-06-02T19:10:58.644Z] [ERROR] 2025-06-02T19:10:58.644Z vite:resolve [32m0.22ms[39m [36m/src/contexts/AuthContext.tsx[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/contexts/AuthContext.tsx[22m
[2025-06-02T19:10:58.644Z] [ERROR] 2025-06-02T19:10:58.644Z vite:resolve [32m0.19ms[39m [36m/src/components/alerts/AlertManagement.tsx[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/components/alerts/AlertManagement.tsx[22m
[2025-06-02T19:10:58.644Z] [ERROR] 2025-06-02T19:10:58.644Z vite:resolve [32m0.18ms[39m [36m/src/components/auth/LoginForm.tsx[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/components/auth/LoginForm.tsx[22m
[2025-06-02T19:10:58.645Z] [ERROR] 2025-06-02T19:10:58.645Z vite:resolve [32m0.18ms[39m [36m/src/components/debug/ErrorBoundary.tsx[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/components/debug/ErrorBoundary.tsx[22m
[2025-06-02T19:10:58.645Z] [ERROR] 2025-06-02T19:10:58.645Z vite:resolve [32m0.18ms[39m [36m/src/components/ui/accordion.tsx[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/components/ui/accordion.tsx[22m
[2025-06-02T19:10:58.645Z] [ERROR] 2025-06-02T19:10:58.645Z vite:resolve [32m0.19ms[39m [36m/src/components/ui/alert-dialog.tsx[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/components/ui/alert-dialog.tsx[22m
[2025-06-02T19:10:58.646Z] [ERROR] 2025-06-02T19:10:58.645Z vite:resolve [32m0.17ms[39m [36m/src/components/ui/avatar.tsx[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/components/ui/avatar.tsx[22m
2025-06-02T19:10:58.646Z vite:resolve [32m0.16ms[39m [36m/src/components/ui/checkbox.tsx[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/components/ui/checkbox.tsx[22m
[2025-06-02T19:10:58.646Z] [ERROR] 2025-06-02T19:10:58.646Z vite:resolve [32m0.17ms[39m [36m/src/components/ui/dropdown-menu.tsx[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/components/ui/dropdown-menu.tsx[22m
[2025-06-02T19:10:58.646Z] [ERROR] 2025-06-02T19:10:58.646Z vite:resolve [32m0.24ms[39m [36m/src/components/ui/popover.tsx[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/components/ui/popover.tsx[22m
2025-06-02T19:10:58.647Z vite:resolve [32m0.18ms[39m [36m/src/components/ui/scroll-area.tsx[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/components/ui/scroll-area.tsx[22m
[2025-06-02T19:10:58.648Z] [ERROR] 2025-06-02T19:10:58.647Z vite:resolve [32m0.25ms[39m [36m/src/components/ui/separator.tsx[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/components/ui/separator.tsx[22m
2025-06-02T19:10:58.647Z vite:resolve [32m0.19ms[39m [36m/src/components/ui/switch.tsx[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/components/ui/switch.tsx[22m
2025-06-02T19:10:58.648Z vite:resolve [32m0.30ms[39m [36m/src/components/ui/tooltip.tsx[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/components/ui/tooltip.tsx[22m
[2025-06-02T19:10:58.649Z] [ERROR] 2025-06-02T19:10:58.648Z vite:resolve [32m0.34ms[39m [36m/src/components/zones/InteractiveZoneMap.tsx[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/components/zones/InteractiveZoneMap.tsx[22m
2025-06-02T19:10:58.648Z vite:resolve [32m0.22ms[39m [36m/src/components/zones/MapZoneStats.tsx[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/components/zones/MapZoneStats.tsx[22m
2025-06-02T19:10:58.649Z vite:resolve [32m0.27ms[39m [36m/src/components/zones/ZoneDrawingMap.tsx[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/components/zones/ZoneDrawingMap.tsx[22m
[2025-06-02T19:10:58.649Z] [ERROR] 2025-06-02T19:10:58.649Z vite:resolve [32m0.22ms[39m [36m/src/components/zones/ZoneManagement.tsx[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/components/zones/ZoneManagement.tsx[22m
2025-06-02T19:10:58.649Z vite:resolve [32m0.19ms[39m [36m/src/components/zones/ZoneManagementFixed.tsx[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/components/zones/ZoneManagementFixed.tsx[22m
[2025-06-02T19:10:58.649Z] [ERROR] 2025-06-02T19:10:58.650Z vite:resolve [32m0.23ms[39m [36m/src/components/zones/ZoneManagementSimple.tsx[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/components/zones/ZoneManagementSimple.tsx[22m
[2025-06-02T19:10:58.650Z] [ERROR] 2025-06-02T19:10:58.650Z vite:resolve [32m0.20ms[39m [36m/tailwind.config.js[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/tailwind.config.js[22m
[2025-06-02T19:10:58.650Z] [ERROR] 2025-06-02T19:10:58.650Z vite:hmr [self-accepts] [2msrc/index.css[22m
2025-06-02T19:10:58.650Z vite:import-analysis [32m0.47ms[39m [2m[0 imports rewritten] [2msrc/index.css[22m[2m[22m
[2025-06-02T19:10:58.651Z] [ERROR] 2025-06-02T19:10:58.650Z vite:transform [31m1047.64ms[39m [2m/src/index.css[22m
2025-06-02T19:10:58.651Z vite:time [31m1041.92ms[39m [2m/src/index.css[22m
[2025-06-02T19:10:58.651Z] [ERROR] 2025-06-02T19:10:58.651Z vite:load [31m376.13ms[39m [fs] [2m/src/lib/utils.ts[22m
[2025-06-02T19:10:58.652Z] [ERROR] 2025-06-02T19:10:58.652Z vite:load [31m376.48ms[39m [fs] [2m/src/components/ui/toast.tsx[22m
[2025-06-02T19:10:58.664Z] [ERROR] 2025-06-02T19:10:58.664Z vite:load [31m388.29ms[39m [fs] [2m/src/components/ui/sidebar.tsx[22m
[2025-06-02T19:10:58.672Z] [ERROR] 2025-06-02T19:10:58.672Z vite:load [31m396.89ms[39m [fs] [2m/src/components/ui/badge.tsx[22m
[2025-06-02T19:10:58.676Z] [ERROR] 2025-06-02T19:10:58.676Z vite:load [31m401.02ms[39m [fs] [2m/src/components/ui/button.tsx[22m
[2025-06-02T19:10:58.681Z] [ERROR] 2025-06-02T19:10:58.681Z vite:load [31m405.62ms[39m [fs] [2m/src/components/ui/card.tsx[22m
[2025-06-02T19:10:58.690Z] [ERROR] 2025-06-02T19:10:58.690Z vite:load [31m406.19ms[39m [fs] [2m/src/components/ui/table.tsx[22m
[2025-06-02T19:10:58.698Z] [ERROR] 2025-06-02T19:10:58.698Z vite:load [31m407.16ms[39m [fs] [2m/src/utils/debug.ts[22m
[2025-06-02T19:10:58.699Z] [ERROR] 2025-06-02T19:10:58.699Z vite:load [31m407.87ms[39m [fs] [2m/src/components/ui/tabs.tsx[22m
[2025-06-02T19:10:58.704Z] [ERROR] 2025-06-02T19:10:58.704Z vite:load [31m398.98ms[39m [fs] [2m/src/components/ui/label.tsx[22m
[2025-06-02T19:10:58.707Z] [ERROR] 2025-06-02T19:10:58.707Z vite:load [31m401.70ms[39m [fs] [2m/src/components/ui/dialog.tsx[22m
[2025-06-02T19:10:58.713Z] [ERROR] 2025-06-02T19:10:58.713Z vite:load [31m407.60ms[39m [fs] [2m/src/components/ui/select.tsx[22m
[2025-06-02T19:10:58.722Z] [ERROR] 2025-06-02T19:10:58.722Z vite:load [31m810.37ms[39m [plugin] [2m/node_modules/.vite/deps/lucide-react.js?v=cbff3303[22m
[2025-06-02T19:10:58.733Z] [ERROR] 2025-06-02T19:10:58.733Z vite:cache [memory] [2m/node_modules/.vite/deps/chunk-DRWLMN53.js?v=cbff3303[22m
2025-06-02T19:10:58.733Z vite:cache [memory] [2m/node_modules/.vite/deps/chunk-G3PMV62Z.js?v=cbff3303[22m
[2025-06-02T19:10:58.734Z] [ERROR] 2025-06-02T19:10:58.733Z vite:import-analysis [32m7.93ms[39m [2m[2 imports rewritten] [2mnode_modules/.vite/deps/lucide-react.js?v=cbff3303[22m[2m[22m
2025-06-02T19:10:58.733Z vite:transform [33m11.27ms[39m [2m/node_modules/.vite/deps/lucide-react.js?v=cbff3303[22m
[2025-06-02T19:10:58.735Z] [ERROR] 2025-06-02T19:10:58.735Z vite:load [31m429.43ms[39m [fs] [2m/src/components/ui/textarea.tsx[22m
[2025-06-02T19:10:58.737Z] [ERROR] 2025-06-02T19:10:58.738Z vite:load [31m425.35ms[39m [fs] [2m/src/components/ui/progress.tsx[22m
[2025-06-02T19:10:58.740Z] [ERROR] 2025-06-02T19:10:58.741Z vite:load [31m435.18ms[39m [fs] [2m/src/components/ui/input.tsx[22m
[2025-06-02T19:10:58.751Z] [ERROR] 2025-06-02T19:10:58.751Z vite:cache [memory] [2m/@react-refresh[22m
[2025-06-02T19:10:58.752Z] [ERROR] 2025-06-02T19:10:58.751Z vite:resolve [32m0.86ms[39m [36m@radix-ui/react-toast[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite/deps/@radix-ui_react-toast.js?v=cbff3303[22m
2025-06-02T19:10:58.751Z vite:resolve [32m0.92ms[39m [36mclass-variance-authority[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite/deps/class-variance-authority.js?v=cbff3303[22m
2025-06-02T19:10:58.752Z vite:import-analysis /node_modules/.vite/deps/react_jsx-dev-runtime.js?v=cbff3303 needs interop
2025-06-02T19:10:58.752Z vite:import-analysis /node_modules/.vite/deps/react.js?v=cbff3303 needs interop
2025-06-02T19:10:58.752Z vite:cache [memory] [2m/node_modules/.vite/deps/react_jsx-dev-runtime.js?v=cbff3303[22m
2025-06-02T19:10:58.752Z vite:cache [memory] [2m/node_modules/.vite/deps/react.js?v=cbff3303[22m
2025-06-02T19:10:58.752Z vite:cache [memory] [2m/node_modules/.vite/deps/lucide-react.js?v=cbff3303[22m
2025-06-02T19:10:58.752Z vite:hmr [self-accepts] [2msrc/components/ui/toast.tsx[22m
2025-06-02T19:10:58.752Z vite:optimize-deps load [36mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite/deps/@radix-ui_react-toast.js[39m
2025-06-02T19:10:58.752Z vite:optimize-deps load [36mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite/deps/class-variance-authority.js[39m
[2025-06-02T19:10:58.753Z] [ERROR] 2025-06-02T19:10:58.752Z vite:import-analysis [32m1.97ms[39m [2m[7 imports rewritten] [2msrc/components/ui/toast.tsx[22m[2m[22m
2025-06-02T19:10:58.752Z vite:transform [31m100.61ms[39m [2m/src/components/ui/toast.tsx[22m
[2025-06-02T19:10:58.771Z] [ERROR] 2025-06-02T19:10:58.772Z vite:hmr [file change] [2mlogs/vite-debug.log[22m
[2025-06-02T19:10:58.772Z] [ERROR] 2025-06-02T19:10:58.772Z vite:hmr [no modules matched] [2mlogs/vite-debug.log[22m
2025-06-02T19:10:58.772Z vite:esbuild esbuild error with options used:  {
  sourcemap: true,
  sourcefile: 'C:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/utils/debug.ts',
  target: 'esnext',
  charset: 'utf8',
  jsxDev: true,
  drop: [],
  jsx: 'automatic',
  minify: false,
  minifyIdentifiers: false,
  minifySyntax: false,
  minifyWhitespace: false,
  treeShaking: false,
  keepNames: false,
  supported: { 'dynamic-import': true, 'import-meta': true },
  loader: 'ts',
  tsconfigRaw: {
    compilerOptions: { jsx: undefined, target: 'ES2020', useDefineForClassFields: true }
  }
}
[2025-06-02T19:10:58.774Z] [ERROR] [2m3:10:58 p. m.[22m [31m[1m[vite][22m[39m [31mInternal server error: Transform failed with 1 error:
C:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/utils/debug.ts:96:7: ERROR: Expected ";" but found ")"[39m
  Plugin: [35mvite:esbuild[39m
  File: [36mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/utils/debug.ts[39m:96:7
[33m  
  [33mExpected ";" but found ")"[33m
  94 |            data: args.length === 1 && typeof args[0] === 'object' ? args[0] : args,
  95 |          });
  96 |        });
     |         ^
  97 |      });
  98 |    }
  [39m
      at failureErrorWithLog (C:\Users\<USER>\Documents\augment-projects\volcanoApp\volcanoApp\backoffice\frontend\node_modules\esbuild\lib\main.js:1472:15)
      at C:\Users\<USER>\Documents\augment-projects\volcanoApp\volcanoApp\backoffice\frontend\node_modules\esbuild\lib\main.js:755:50
      at responseCallbacks.<computed> (C:\Users\<USER>\Documents\augment-projects\volcanoApp\volcanoApp\backoffice\frontend\node_modules\esbuild\lib\main.js:622:9)
      at handleIncomingPacket (C:\Users\<USER>\Documents\augment-projects\volcanoApp\volcanoApp\backoffice\frontend\node_modules\esbuild\lib\main.js:677:12)
      at Socket.readFromStdout (C:\Users\<USER>\Documents\augment-projects\volcanoApp\volcanoApp\backoffice\frontend\node_modules\esbuild\lib\main.js:600:7)
      at Socket.emit (node:events:518:28)
      at addChunk (node:internal/streams/readable:561:12)
      at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
      at Readable.push (node:internal/streams/readable:392:5)
      at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
[2025-06-02T19:10:58.776Z] [ERROR] 2025-06-02T19:10:58.775Z vite:time [31m428.29ms[39m [2m/src/utils/debug.ts[22m
[2m3:10:58 p. m.[22m [31m[1m[vite][22m[39m Pre-transform error: Transform failed with 1 error:
C:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/utils/debug.ts:96:7: ERROR: Expected ";" but found ")"
[2025-06-02T19:10:58.779Z] [ERROR] 2025-06-02T19:10:58.779Z vite:cache [memory] [2m/@react-refresh[22m
[2025-06-02T19:10:58.780Z] [ERROR] 2025-06-02T19:10:58.779Z vite:resolve [32m3.31ms[39m [36m@radix-ui/react-slot[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite/deps/@radix-ui_react-slot.js?v=cbff3303[22m
2025-06-02T19:10:58.779Z vite:resolve [32m3.29ms[39m [36mclsx[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite/deps/clsx.js?v=cbff3303[22m
2025-06-02T19:10:58.779Z vite:resolve [32m3.31ms[39m [36mtailwind-merge[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite/deps/tailwind-merge.js?v=cbff3303[22m
2025-06-02T19:10:58.779Z vite:resolve [32m3.25ms[39m [36m@radix-ui/react-label[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite/deps/@radix-ui_react-label.js?v=cbff3303[22m
2025-06-02T19:10:58.779Z vite:resolve [32m3.20ms[39m [36m@radix-ui/react-tabs[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite/deps/@radix-ui_react-tabs.js?v=cbff3303[22m
[2025-06-02T19:10:58.780Z] [ERROR] 2025-06-02T19:10:58.780Z vite:import-analysis /node_modules/.vite/deps/react_jsx-dev-runtime.js?v=cbff3303 needs interop
2025-06-02T19:10:58.780Z vite:import-analysis /node_modules/.vite/deps/react_jsx-dev-runtime.js?v=cbff3303 needs interop
2025-06-02T19:10:58.780Z vite:import-analysis /node_modules/.vite/deps/react.js?v=cbff3303 needs interop
2025-06-02T19:10:58.780Z vite:import-analysis /node_modules/.vite/deps/react_jsx-dev-runtime.js?v=cbff3303 needs interop
2025-06-02T19:10:58.780Z vite:import-analysis /node_modules/.vite/deps/react.js?v=cbff3303 needs interop
2025-06-02T19:10:58.780Z vite:import-analysis /node_modules/.vite/deps/react_jsx-dev-runtime.js?v=cbff3303 needs interop
2025-06-02T19:10:58.780Z vite:import-analysis /node_modules/.vite/deps/react.js?v=cbff3303 needs interop
2025-06-02T19:10:58.780Z vite:import-analysis /node_modules/.vite/deps/react_jsx-dev-runtime.js?v=cbff3303 needs interop
2025-06-02T19:10:58.780Z vite:import-analysis /node_modules/.vite/deps/react.js?v=cbff3303 needs interop
[2025-06-02T19:10:58.780Z] [ERROR] 2025-06-02T19:10:58.780Z vite:import-analysis /node_modules/.vite/deps/react_jsx-dev-runtime.js?v=cbff3303 needs interop
2025-06-02T19:10:58.780Z vite:import-analysis /node_modules/.vite/deps/react.js?v=cbff3303 needs interop
2025-06-02T19:10:58.780Z vite:import-analysis /node_modules/.vite/deps/react_jsx-dev-runtime.js?v=cbff3303 needs interop
2025-06-02T19:10:58.780Z vite:import-analysis /node_modules/.vite/deps/react.js?v=cbff3303 needs interop
2025-06-02T19:10:58.781Z vite:cache [memory] [2m/node_modules/.vite/deps/react_jsx-dev-runtime.js?v=cbff3303[22m
2025-06-02T19:10:58.781Z vite:hmr [self-accepts] [2msrc/components/ui/badge.tsx[22m
2025-06-02T19:10:58.781Z vite:cache [memory] [2m/node_modules/.vite/deps/react.js?v=cbff3303[22m
2025-06-02T19:10:58.781Z vite:hmr [self-accepts] [2msrc/components/ui/input.tsx[22m
2025-06-02T19:10:58.781Z vite:hmr [self-accepts] [2msrc/components/ui/textarea.tsx[22m
2025-06-02T19:10:58.781Z vite:hmr [self-accepts] [2msrc/components/ui/sidebar.tsx[22m
[2025-06-02T19:10:58.781Z] [ERROR] 2025-06-02T19:10:58.781Z vite:hmr [self-accepts] [2msrc/components/ui/button.tsx[22m
2025-06-02T19:10:58.781Z vite:hmr [self-accepts] [2msrc/components/ui/label.tsx[22m
2025-06-02T19:10:58.781Z vite:hmr [self-accepts] [2msrc/components/ui/tabs.tsx[22m
2025-06-02T19:10:58.781Z vite:import-analysis [32m5.59ms[39m [2m[4 imports rewritten] [2msrc/components/ui/badge.tsx[22m[2m[22m
2025-06-02T19:10:58.781Z vite:import-analysis [32m5.68ms[39m [2m[4 imports rewritten] [2msrc/components/ui/input.tsx[22m[2m[22m
2025-06-02T19:10:58.781Z vite:import-analysis [32m5.72ms[39m [2m[4 imports rewritten] [2msrc/components/ui/textarea.tsx[22m[2m[22m
[2025-06-02T19:10:58.782Z] [ERROR] 2025-06-02T19:10:58.781Z vite:import-analysis [32m5.76ms[39m [2m[4 imports rewritten] [2msrc/components/ui/sidebar.tsx[22m[2m[22m
2025-06-02T19:10:58.781Z vite:optimize-deps load [36mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite/deps/@radix-ui_react-slot.js[39m
2025-06-02T19:10:58.782Z vite:optimize-deps load [36mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite/deps/clsx.js[39m
2025-06-02T19:10:58.782Z vite:optimize-deps load [36mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite/deps/tailwind-merge.js[39m
2025-06-02T19:10:58.782Z vite:optimize-deps load [36mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite/deps/@radix-ui_react-label.js[39m
2025-06-02T19:10:58.782Z vite:optimize-deps load [36mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite/deps/@radix-ui_react-tabs.js[39m
[2025-06-02T19:10:58.782Z] [ERROR] 2025-06-02T19:10:58.782Z vite:import-analysis [32m6.27ms[39m [2m[6 imports rewritten] [2msrc/components/ui/button.tsx[22m[2m[22m
2025-06-02T19:10:58.782Z vite:import-analysis [32m6.38ms[39m [2m[2 imports rewritten] [2msrc/lib/utils.ts[22m[2m[22m
2025-06-02T19:10:58.782Z vite:import-analysis [32m6.42ms[39m [2m[6 imports rewritten] [2msrc/components/ui/label.tsx[22m[2m[22m
2025-06-02T19:10:58.782Z vite:import-analysis [32m6.46ms[39m [2m[5 imports rewritten] [2msrc/components/ui/tabs.tsx[22m[2m[22m
[2025-06-02T19:10:58.783Z] [ERROR] 2025-06-02T19:10:58.783Z vite:transform [31m110.39ms[39m [2m/src/components/ui/badge.tsx[22m
2025-06-02T19:10:58.783Z vite:transform [33m42.17ms[39m [2m/src/components/ui/input.tsx[22m
2025-06-02T19:10:58.783Z vite:transform [33m47.82ms[39m [2m/src/components/ui/textarea.tsx[22m
2025-06-02T19:10:58.783Z vite:transform [31m119.10ms[39m [2m/src/components/ui/sidebar.tsx[22m
[2025-06-02T19:10:58.784Z] [ERROR] 2025-06-02T19:10:58.783Z vite:transform [31m106.79ms[39m [2m/src/components/ui/button.tsx[22m
2025-06-02T19:10:58.783Z vite:transform [31m131.82ms[39m [2m/src/lib/utils.ts[22m
2025-06-02T19:10:58.783Z vite:transform [31m78.93ms[39m [2m/src/components/ui/label.tsx[22m
2025-06-02T19:10:58.783Z vite:transform [31m84.60ms[39m [2m/src/components/ui/tabs.tsx[22m
2025-06-02T19:10:58.784Z vite:time [31m436.86ms[39m [2m/src/components/ui/badge.tsx[22m
[2025-06-02T19:10:58.784Z] [ERROR] 2025-06-02T19:10:58.784Z vite:time [33m30.98ms[39m [2m/src/components/ui/sidebar.tsx[22m
2025-06-02T19:10:58.784Z vite:time [31m437.18ms[39m [2m/src/components/ui/button.tsx[22m
2025-06-02T19:10:58.784Z vite:time [31m437.49ms[39m [2m/src/lib/utils.ts[22m
[2025-06-02T19:10:58.789Z] [ERROR] 2025-06-02T19:10:58.789Z vite:cache [memory] [2m/@react-refresh[22m
[2025-06-02T19:10:58.790Z] [ERROR] 2025-06-02T19:10:58.789Z vite:resolve [32m2.16ms[39m [36m@radix-ui/react-progress[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite/deps/@radix-ui_react-progress.js?v=cbff3303[22m
2025-06-02T19:10:58.789Z vite:resolve [32m2.03ms[39m [36m@radix-ui/react-dialog[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite/deps/@radix-ui_react-dialog.js?v=cbff3303[22m
2025-06-02T19:10:58.789Z vite:cache [memory] [2m/src/lib/utils.ts[22m
2025-06-02T19:10:58.789Z vite:import-analysis /node_modules/.vite/deps/react_jsx-dev-runtime.js?v=cbff3303 needs interop
2025-06-02T19:10:58.790Z vite:import-analysis /node_modules/.vite/deps/react.js?v=cbff3303 needs interop
[2025-06-02T19:10:58.790Z] [ERROR] 2025-06-02T19:10:58.790Z vite:import-analysis /node_modules/.vite/deps/react_jsx-dev-runtime.js?v=cbff3303 needs interop
2025-06-02T19:10:58.790Z vite:import-analysis /node_modules/.vite/deps/react.js?v=cbff3303 needs interop
2025-06-02T19:10:58.790Z vite:import-analysis /node_modules/.vite/deps/react_jsx-dev-runtime.js?v=cbff3303 needs interop
2025-06-02T19:10:58.790Z vite:import-analysis /node_modules/.vite/deps/react.js?v=cbff3303 needs interop
2025-06-02T19:10:58.790Z vite:import-analysis /node_modules/.vite/deps/react_jsx-dev-runtime.js?v=cbff3303 needs interop
2025-06-02T19:10:58.790Z vite:import-analysis /node_modules/.vite/deps/react.js?v=cbff3303 needs interop
[2025-06-02T19:10:58.791Z] [ERROR] 2025-06-02T19:10:58.791Z vite:cache [memory] [2m/node_modules/.vite/deps/react_jsx-dev-runtime.js?v=cbff3303[22m
2025-06-02T19:10:58.791Z vite:cache [memory] [2m/node_modules/.vite/deps/react.js?v=cbff3303[22m
2025-06-02T19:10:58.791Z vite:hmr [self-accepts] [2msrc/components/ui/table.tsx[22m
2025-06-02T19:10:58.791Z vite:hmr [self-accepts] [2msrc/components/ui/card.tsx[22m
2025-06-02T19:10:58.791Z vite:cache [memory] [2m/node_modules/.vite/deps/lucide-react.js?v=cbff3303[22m
2025-06-02T19:10:58.791Z vite:hmr [self-accepts] [2msrc/components/ui/progress.tsx[22m
2025-06-02T19:10:58.791Z vite:hmr [self-accepts] [2msrc/components/ui/dialog.tsx[22m
[2025-06-02T19:10:58.791Z] [ERROR] 2025-06-02T19:10:58.791Z vite:import-analysis [32m5.47ms[39m [2m[4 imports rewritten] [2msrc/components/ui/table.tsx[22m[2m[22m
2025-06-02T19:10:58.791Z vite:import-analysis [32m5.57ms[39m [2m[4 imports rewritten] [2msrc/components/ui/card.tsx[22m[2m[22m
2025-06-02T19:10:58.791Z vite:optimize-deps load [36mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite/deps/@radix-ui_react-progress.js[39m
2025-06-02T19:10:58.791Z vite:optimize-deps load [36mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite/deps/@radix-ui_react-dialog.js[39m
2025-06-02T19:10:58.792Z vite:import-analysis [32m5.90ms[39m [2m[5 imports rewritten] [2msrc/components/ui/progress.tsx[22m[2m[22m
2025-06-02T19:10:58.792Z vite:import-analysis [32m5.94ms[39m [2m[6 imports rewritten] [2msrc/components/ui/dialog.tsx[22m[2m[22m
[2025-06-02T19:10:58.792Z] [ERROR] 2025-06-02T19:10:58.792Z vite:transform [31m102.06ms[39m [2m/src/components/ui/table.tsx[22m
[2025-06-02T19:10:58.792Z] [ERROR] 2025-06-02T19:10:58.792Z vite:transform [31m111.24ms[39m [2m/src/components/ui/card.tsx[22m
[2025-06-02T19:10:58.793Z] [ERROR] 2025-06-02T19:10:58.793Z vite:transform [31m55.25ms[39m [2m/src/components/ui/progress.tsx[22m
2025-06-02T19:10:58.793Z vite:transform [31m85.82ms[39m [2m/src/components/ui/dialog.tsx[22m
2025-06-02T19:10:58.793Z vite:time [31m462.09ms[39m [2m/src/components/ui/card.tsx[22m
[2025-06-02T19:10:58.795Z] [ERROR] 2025-06-02T19:10:58.795Z vite:cache [memory] [2m/@react-refresh[22m
[2025-06-02T19:10:58.796Z] [ERROR] 2025-06-02T19:10:58.795Z vite:resolve [32m0.80ms[39m [36m@radix-ui/react-select[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite/deps/@radix-ui_react-select.js?v=cbff3303[22m
2025-06-02T19:10:58.795Z vite:cache [memory] [2m/src/lib/utils.ts[22m
2025-06-02T19:10:58.795Z vite:import-analysis /node_modules/.vite/deps/react_jsx-dev-runtime.js?v=cbff3303 needs interop
2025-06-02T19:10:58.795Z vite:import-analysis /node_modules/.vite/deps/react.js?v=cbff3303 needs interop
2025-06-02T19:10:58.796Z vite:cache [memory] [2m/node_modules/.vite/deps/react_jsx-dev-runtime.js?v=cbff3303[22m
2025-06-02T19:10:58.796Z vite:cache [memory] [2m/node_modules/.vite/deps/react.js?v=cbff3303[22m
2025-06-02T19:10:58.796Z vite:cache [memory] [2m/node_modules/.vite/deps/lucide-react.js?v=cbff3303[22m
2025-06-02T19:10:58.796Z vite:hmr [self-accepts] [2msrc/components/ui/select.tsx[22m
2025-06-02T19:10:58.796Z vite:optimize-deps load [36mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite/deps/@radix-ui_react-select.js[39m
[2025-06-02T19:10:58.796Z] [ERROR] 2025-06-02T19:10:58.796Z vite:import-analysis [32m2.06ms[39m [2m[6 imports rewritten] [2msrc/components/ui/select.tsx[22m[2m[22m
2025-06-02T19:10:58.796Z vite:transform [31m83.09ms[39m [2m/src/components/ui/select.tsx[22m
[2025-06-02T19:10:58.800Z] [ERROR] 2025-06-02T19:10:58.800Z vite:cache [304] [2m/src/components/ui/table.tsx[22m
[2025-06-02T19:10:58.800Z] [ERROR] 2025-06-02T19:10:58.800Z vite:time [32m0.35ms[39m [2m/src/components/ui/table.tsx[22m
2025-06-02T19:10:58.800Z vite:cache [304] [2m/src/components/ui/tabs.tsx[22m
2025-06-02T19:10:58.801Z vite:time [32m0.16ms[39m [2m/src/components/ui/tabs.tsx[22m
[2025-06-02T19:10:58.801Z] [ERROR] 2025-06-02T19:10:58.801Z vite:cache [304] [2m/src/components/ui/dialog.tsx[22m
2025-06-02T19:10:58.801Z vite:time [32m0.13ms[39m [2m/src/components/ui/dialog.tsx[22m
[2025-06-02T19:10:58.802Z] [ERROR] 2025-06-02T19:10:58.802Z vite:cache [304] [2m/src/components/ui/input.tsx[22m
2025-06-02T19:10:58.802Z vite:time [32m0.09ms[39m [2m/src/components/ui/input.tsx[22m
[2025-06-02T19:10:58.803Z] [ERROR] 2025-06-02T19:10:58.803Z vite:cache [304] [2m/src/components/ui/label.tsx[22m
2025-06-02T19:10:58.803Z vite:time [32m0.22ms[39m [2m/src/components/ui/label.tsx[22m
[2025-06-02T19:10:58.804Z] [ERROR] 2025-06-02T19:10:58.804Z vite:cache [304] [2m/src/components/ui/select.tsx[22m
2025-06-02T19:10:58.804Z vite:time [32m0.15ms[39m [2m/src/components/ui/select.tsx[22m
[2025-06-02T19:10:58.817Z] [ERROR] 2025-06-02T19:10:58.817Z vite:cache [304] [2m/src/components/ui/progress.tsx[22m
2025-06-02T19:10:58.817Z vite:time [32m0.24ms[39m [2m/src/components/ui/progress.tsx[22m
[2025-06-02T19:10:58.818Z] [ERROR] 2025-06-02T19:10:58.818Z vite:load [31m66.04ms[39m [plugin] [2m/node_modules/.vite/deps/class-variance-authority.js?v=cbff3303[22m
[2025-06-02T19:10:58.819Z] [ERROR] 2025-06-02T19:10:58.819Z vite:resolve [32m0.21ms[39m [36m./chunk-U7P2NEEE.js[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite/deps/chunk-U7P2NEEE.js?v=cbff3303[22m
[2025-06-02T19:10:58.820Z] [ERROR] 2025-06-02T19:10:58.819Z vite:cache [memory] [2m/node_modules/.vite/deps/chunk-G3PMV62Z.js?v=cbff3303[22m
2025-06-02T19:10:58.819Z vite:optimize-deps load [36mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite/deps/chunk-U7P2NEEE.js[39m
2025-06-02T19:10:58.820Z vite:import-analysis [32m1.33ms[39m [2m[2 imports rewritten] [2mnode_modules/.vite/deps/class-variance-authority.js?v=cbff3303[22m[2m[22m
2025-06-02T19:10:58.820Z vite:transform [32m1.60ms[39m [2m/node_modules/.vite/deps/class-variance-authority.js?v=cbff3303[22m
2025-06-02T19:10:58.820Z vite:load [31m67.88ms[39m [plugin] [2m/node_modules/.vite/deps/@radix-ui_react-toast.js?v=cbff3303[22m
[2025-06-02T19:10:58.821Z] [ERROR] 2025-06-02T19:10:58.821Z vite:resolve [32m0.35ms[39m [36m./chunk-RI5HKWN3.js[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite/deps/chunk-RI5HKWN3.js?v=cbff3303[22m
2025-06-02T19:10:58.821Z vite:resolve [32m0.44ms[39m [36m./chunk-JNXNBBFZ.js[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite/deps/chunk-JNXNBBFZ.js?v=cbff3303[22m
[2025-06-02T19:10:58.821Z] [ERROR] 2025-06-02T19:10:58.821Z vite:resolve [32m0.46ms[39m [36m./chunk-CHI5YKDN.js[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite/deps/chunk-CHI5YKDN.js?v=cbff3303[22m
2025-06-02T19:10:58.821Z vite:resolve [32m0.49ms[39m [36m./chunk-UPJR475S.js[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite/deps/chunk-UPJR475S.js?v=cbff3303[22m
2025-06-02T19:10:58.821Z vite:resolve [32m0.51ms[39m [36m./chunk-M7W2FPBU.js[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite/deps/chunk-M7W2FPBU.js?v=cbff3303[22m
2025-06-02T19:10:58.821Z vite:resolve [32m0.52ms[39m [36m./chunk-QNBT3DDS.js[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite/deps/chunk-QNBT3DDS.js?v=cbff3303[22m
2025-06-02T19:10:58.821Z vite:resolve [32m0.53ms[39m [36m./chunk-QVMDSMUE.js[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite/deps/chunk-QVMDSMUE.js?v=cbff3303[22m
2025-06-02T19:10:58.821Z vite:resolve [32m0.54ms[39m [36m./chunk-TLMPZXCM.js[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite/deps/chunk-TLMPZXCM.js?v=cbff3303[22m
2025-06-02T19:10:58.821Z vite:resolve [32m0.55ms[39m [36m./chunk-6PXSGDAH.js[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite/deps/chunk-6PXSGDAH.js?v=cbff3303[22m
[2025-06-02T19:10:58.822Z] [ERROR] 2025-06-02T19:10:58.822Z vite:cache [memory] [2m/node_modules/.vite/deps/chunk-PJEEZAML.js?v=cbff3303[22m
2025-06-02T19:10:58.822Z vite:cache [memory] [2m/node_modules/.vite/deps/chunk-DRWLMN53.js?v=cbff3303[22m
2025-06-02T19:10:58.822Z vite:cache [memory] [2m/node_modules/.vite/deps/chunk-G3PMV62Z.js?v=cbff3303[22m
2025-06-02T19:10:58.822Z vite:optimize-deps load [36mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite/deps/chunk-RI5HKWN3.js[39m
2025-06-02T19:10:58.822Z vite:optimize-deps load [36mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite/deps/chunk-JNXNBBFZ.js[39m
2025-06-02T19:10:58.822Z vite:optimize-deps load [36mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite/deps/chunk-CHI5YKDN.js[39m
2025-06-02T19:10:58.822Z vite:optimize-deps load [36mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite/deps/chunk-UPJR475S.js[39m
2025-06-02T19:10:58.822Z vite:optimize-deps load [36mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite/deps/chunk-M7W2FPBU.js[39m
2025-06-02T19:10:58.822Z vite:optimize-deps load [36mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite/deps/chunk-QNBT3DDS.js[39m
2025-06-02T19:10:58.822Z vite:optimize-deps load [36mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite/deps/chunk-QVMDSMUE.js[39m
2025-06-02T19:10:58.822Z vite:optimize-deps load [36mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite/deps/chunk-TLMPZXCM.js[39m
[2025-06-02T19:10:58.822Z] [ERROR] 2025-06-02T19:10:58.822Z vite:optimize-deps load [36mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite/deps/chunk-6PXSGDAH.js[39m
2025-06-02T19:10:58.822Z vite:import-analysis [32m2.17ms[39m [2m[12 imports rewritten] [2mnode_modules/.vite/deps/@radix-ui_react-toast.js?v=cbff3303[22m[2m[22m
2025-06-02T19:10:58.822Z vite:transform [32m2.38ms[39m [2m/node_modules/.vite/deps/@radix-ui_react-toast.js?v=cbff3303[22m
[2025-06-02T19:10:58.823Z] [ERROR] 2025-06-02T19:10:58.823Z vite:hmr [file change] [2mlogs/vite-errors.log[22m
[2025-06-02T19:10:58.823Z] [ERROR] 2025-06-02T19:10:58.823Z vite:hmr [no modules matched] [2mlogs/vite-errors.log[22m
[2025-06-02T19:10:58.824Z] [ERROR] 2025-06-02T19:10:58.824Z vite:cache [304] [2m/src/components/ui/textarea.tsx[22m
[2025-06-02T19:10:58.824Z] [ERROR] 2025-06-02T19:10:58.824Z vite:time [32m0.12ms[39m [2m/src/components/ui/textarea.tsx[22m
[2025-06-02T19:10:58.825Z] [ERROR] 2025-06-02T19:10:58.825Z vite:cache [304] [2m/src/components/ui/toast.tsx[22m
2025-06-02T19:10:58.825Z vite:time [32m0.09ms[39m [2m/src/components/ui/toast.tsx[22m
[2025-06-02T19:10:58.828Z] [ERROR] 2025-06-02T19:10:58.828Z vite:hmr [file change] [2mlogs/vite-debug.log[22m
[2025-06-02T19:10:58.829Z] [ERROR] 2025-06-02T19:10:58.828Z vite:hmr [no modules matched] [2mlogs/vite-debug.log[22m
[2025-06-02T19:10:58.835Z] [ERROR] 2025-06-02T19:10:58.836Z vite:load [31m54.16ms[39m [plugin] [2m/node_modules/.vite/deps/clsx.js?v=cbff3303[22m
[2025-06-02T19:10:58.837Z] [ERROR] 2025-06-02T19:10:58.837Z vite:cache [memory] [2m/node_modules/.vite/deps/chunk-G3PMV62Z.js?v=cbff3303[22m
[2025-06-02T19:10:58.837Z] [ERROR] 2025-06-02T19:10:58.837Z vite:import-analysis [32m1.06ms[39m [2m[2 imports rewritten] [2mnode_modules/.vite/deps/clsx.js?v=cbff3303[22m[2m[22m
2025-06-02T19:10:58.837Z vite:transform [32m1.22ms[39m [2m/node_modules/.vite/deps/clsx.js?v=cbff3303[22m
2025-06-02T19:10:58.837Z vite:load [31m55.76ms[39m [plugin] [2m/node_modules/.vite/deps/@radix-ui_react-slot.js?v=cbff3303[22m
[2025-06-02T19:10:58.837Z] [ERROR] 2025-06-02T19:10:58.838Z vite:cache [memory] [2m/node_modules/.vite/deps/chunk-DRWLMN53.js?v=cbff3303[22m
2025-06-02T19:10:58.838Z vite:cache [memory] [2m/node_modules/.vite/deps/chunk-G3PMV62Z.js?v=cbff3303[22m
[2025-06-02T19:10:58.838Z] [ERROR] 2025-06-02T19:10:58.838Z vite:import-analysis [32m0.52ms[39m [2m[4 imports rewritten] [2mnode_modules/.vite/deps/@radix-ui_react-slot.js?v=cbff3303[22m[2m[22m
2025-06-02T19:10:58.838Z vite:transform [32m0.82ms[39m [2m/node_modules/.vite/deps/@radix-ui_react-slot.js?v=cbff3303[22m
2025-06-02T19:10:58.838Z vite:load [31m56.65ms[39m [plugin] [2m/node_modules/.vite/deps/@radix-ui_react-label.js?v=cbff3303[22m
[2025-06-02T19:10:58.838Z] [ERROR] 2025-06-02T19:10:58.838Z vite:cache [memory] [2m/node_modules/.vite/deps/chunk-PJEEZAML.js?v=cbff3303[22m
2025-06-02T19:10:58.838Z vite:cache [memory] [2m/node_modules/.vite/deps/chunk-DRWLMN53.js?v=cbff3303[22m
2025-06-02T19:10:58.838Z vite:cache [memory] [2m/node_modules/.vite/deps/chunk-G3PMV62Z.js?v=cbff3303[22m
2025-06-02T19:10:58.838Z vite:import-analysis [32m0.44ms[39m [2m[6 imports rewritten] [2mnode_modules/.vite/deps/@radix-ui_react-label.js?v=cbff3303[22m[2m[22m
2025-06-02T19:10:58.838Z vite:transform [32m0.52ms[39m [2m/node_modules/.vite/deps/@radix-ui_react-label.js?v=cbff3303[22m
2025-06-02T19:10:58.839Z vite:load [33m47.30ms[39m [plugin] [2m/node_modules/.vite/deps/@radix-ui_react-dialog.js?v=cbff3303[22m
[2025-06-02T19:10:58.839Z] [ERROR] 2025-06-02T19:10:58.839Z vite:resolve [32m0.20ms[39m [36m./chunk-PTKRTGFL.js[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite/deps/chunk-PTKRTGFL.js?v=cbff3303[22m
[2025-06-02T19:10:58.839Z] [ERROR] 2025-06-02T19:10:58.839Z vite:resolve [32m0.23ms[39m [36m./chunk-UE3I4JV6.js[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite/deps/chunk-UE3I4JV6.js?v=cbff3303[22m
2025-06-02T19:10:58.839Z vite:cache [memory] [2m/node_modules/.vite/deps/chunk-PJEEZAML.js?v=cbff3303[22m
2025-06-02T19:10:58.839Z vite:cache [memory] [2m/node_modules/.vite/deps/chunk-DRWLMN53.js?v=cbff3303[22m
2025-06-02T19:10:58.839Z vite:cache [memory] [2m/node_modules/.vite/deps/chunk-G3PMV62Z.js?v=cbff3303[22m
[2025-06-02T19:10:58.839Z] [ERROR] 2025-06-02T19:10:58.839Z vite:optimize-deps load [36mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite/deps/chunk-PTKRTGFL.js[39m
2025-06-02T19:10:58.839Z vite:optimize-deps load [36mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite/deps/chunk-UE3I4JV6.js[39m
2025-06-02T19:10:58.839Z vite:import-analysis [32m0.78ms[39m [2m[12 imports rewritten] [2mnode_modules/.vite/deps/@radix-ui_react-dialog.js?v=cbff3303[22m[2m[22m
2025-06-02T19:10:58.839Z vite:transform [32m0.88ms[39m [2m/node_modules/.vite/deps/@radix-ui_react-dialog.js?v=cbff3303[22m
2025-06-02T19:10:58.839Z vite:load [33m48.34ms[39m [plugin] [2m/node_modules/.vite/deps/@radix-ui_react-progress.js?v=cbff3303[22m
[2025-06-02T19:10:58.840Z] [ERROR] 2025-06-02T19:10:58.840Z vite:cache [memory] [2m/node_modules/.vite/deps/chunk-PJEEZAML.js?v=cbff3303[22m
2025-06-02T19:10:58.840Z vite:cache [memory] [2m/node_modules/.vite/deps/chunk-DRWLMN53.js?v=cbff3303[22m
2025-06-02T19:10:58.840Z vite:cache [memory] [2m/node_modules/.vite/deps/chunk-G3PMV62Z.js?v=cbff3303[22m
2025-06-02T19:10:58.840Z vite:import-analysis [32m0.34ms[39m [2m[7 imports rewritten] [2mnode_modules/.vite/deps/@radix-ui_react-progress.js?v=cbff3303[22m[2m[22m
2025-06-02T19:10:58.840Z vite:transform [32m0.41ms[39m [2m/node_modules/.vite/deps/@radix-ui_react-progress.js?v=cbff3303[22m
2025-06-02T19:10:58.840Z vite:load [31m58.72ms[39m [plugin] [2m/node_modules/.vite/deps/@radix-ui_react-tabs.js?v=cbff3303[22m
[2025-06-02T19:10:58.840Z] [ERROR] 2025-06-02T19:10:58.840Z vite:resolve [32m0.23ms[39m [36m./chunk-BLD7VZ7P.js[39m -> [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite/deps/chunk-BLD7VZ7P.js?v=cbff3303[22m
[2025-06-02T19:10:58.840Z] [ERROR] 2025-06-02T19:10:58.841Z vite:cache [memory] [2m/node_modules/.vite/deps/chunk-PJEEZAML.js?v=cbff3303[22m
2025-06-02T19:10:58.841Z vite:cache [memory] [2m/node_modules/.vite/deps/chunk-DRWLMN53.js?v=cbff3303[22m
2025-06-02T19:10:58.841Z vite:cache [memory] [2m/node_modules/.vite/deps/chunk-G3PMV62Z.js?v=cbff3303[22m
2025-06-02T19:10:58.841Z vite:optimize-deps load [36mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/node_modules/.vite/deps/chunk-BLD7VZ7P.js[39m
2025-06-02T19:10:58.841Z vite:import-analysis [32m0.71ms[39m [2m[12 imports rewritten] [2mnode_modules/.vite/deps/@radix-ui_react-tabs.js?v=cbff3303[22m[2m[22m
[2025-06-02T19:10:58.841Z] [ERROR] 2025-06-02T19:10:58.841Z vite:transform [32m0.79ms[39m [2m/node_modules/.vite/deps/@radix-ui_react-tabs.js?v=cbff3303[22m
2025-06-02T19:10:58.841Z vite:load [31m59.59ms[39m [plugin] [2m/node_modules/.vite/deps/tailwind-merge.js?v=cbff3303[22m
[2025-06-02T19:10:58.841Z] [ERROR] 2025-06-02T19:10:58.841Z vite:cache [memory] [2m/node_modules/.vite/deps/chunk-G3PMV62Z.js?v=cbff3303[22m
[2025-06-02T19:10:58.841Z] [ERROR] 2025-06-02T19:10:58.841Z vite:import-analysis [32m0.42ms[39m [2m[1 imports rewritten] [2mnode_modules/.vite/deps/tailwind-merge.js?v=cbff3303[22m[2m[22m
2025-06-02T19:10:58.841Z vite:transform [32m0.62ms[39m [2m/node_modules/.vite/deps/tailwind-merge.js?v=cbff3303[22m
2025-06-02T19:10:58.842Z vite:load [33m45.77ms[39m [plugin] [2m/node_modules/.vite/deps/@radix-ui_react-select.js?v=cbff3303[22m
[2025-06-02T19:10:58.843Z] [ERROR] 2025-06-02T19:10:58.843Z vite:cache [memory] [2m/node_modules/.vite/deps/chunk-PJEEZAML.js?v=cbff3303[22m
2025-06-02T19:10:58.843Z vite:cache [memory] [2m/node_modules/.vite/deps/chunk-DRWLMN53.js?v=cbff3303[22m
[2025-06-02T19:10:58.844Z] [ERROR] 2025-06-02T19:10:58.843Z vite:cache [memory] [2m/node_modules/.vite/deps/chunk-G3PMV62Z.js?v=cbff3303[22m
2025-06-02T19:10:58.843Z vite:import-analysis [32m1.59ms[39m [2m[14 imports rewritten] [2mnode_modules/.vite/deps/@radix-ui_react-select.js?v=cbff3303[22m[2m[22m
2025-06-02T19:10:58.843Z vite:transform [32m1.90ms[39m [2m/node_modules/.vite/deps/@radix-ui_react-select.js?v=cbff3303[22m
[2025-06-02T19:10:58.853Z] [ERROR] 2025-06-02T19:10:58.853Z vite:load [33m33.45ms[39m [plugin] [2m/node_modules/.vite/deps/chunk-U7P2NEEE.js?v=cbff3303[22m
[2025-06-02T19:10:58.853Z] [ERROR] 2025-06-02T19:10:58.853Z vite:import-analysis [32m0.03ms[39m [2m[no imports] [2mnode_modules/.vite/deps/chunk-U7P2NEEE.js?v=cbff3303[22m[2m[22m
2025-06-02T19:10:58.853Z vite:transform [32m0.14ms[39m [2m/node_modules/.vite/deps/chunk-U7P2NEEE.js?v=cbff3303[22m
2025-06-02T19:10:58.853Z vite:load [33m31.36ms[39m [plugin] [2m/node_modules/.vite/deps/chunk-RI5HKWN3.js?v=cbff3303[22m
2025-06-02T19:10:58.853Z vite:cache [memory] [2m/node_modules/.vite/deps/chunk-DRWLMN53.js?v=cbff3303[22m
2025-06-02T19:10:58.853Z vite:cache [memory] [2m/node_modules/.vite/deps/chunk-G3PMV62Z.js?v=cbff3303[22m
2025-06-02T19:10:58.853Z vite:import-analysis [32m0.34ms[39m [2m[4 imports rewritten] [2mnode_modules/.vite/deps/chunk-RI5HKWN3.js?v=cbff3303[22m[2m[22m
2025-06-02T19:10:58.853Z vite:transform [32m0.40ms[39m [2m/node_modules/.vite/deps/chunk-RI5HKWN3.js?v=cbff3303[22m
2025-06-02T19:10:58.854Z vite:load [33m31.77ms[39m [plugin] [2m/node_modules/.vite/deps/chunk-M7W2FPBU.js?v=cbff3303[22m
[2025-06-02T19:10:58.854Z] [ERROR] 2025-06-02T19:10:58.854Z vite:cache [memory] [2m/node_modules/.vite/deps/chunk-DRWLMN53.js?v=cbff3303[22m
2025-06-02T19:10:58.854Z vite:cache [memory] [2m/node_modules/.vite/deps/chunk-G3PMV62Z.js?v=cbff3303[22m
2025-06-02T19:10:58.854Z vite:import-analysis [32m0.34ms[39m [2m[2 imports rewritten] [2mnode_modules/.vite/deps/chunk-M7W2FPBU.js?v=cbff3303[22m[2m[22m
2025-06-02T19:10:58.854Z vite:transform [32m0.41ms[39m [2m/node_modules/.vite/deps/chunk-M7W2FPBU.js?v=cbff3303[22m
2025-06-02T19:10:58.854Z vite:load [33m32.30ms[39m [plugin] [2m/node_modules/.vite/deps/chunk-JNXNBBFZ.js?v=cbff3303[22m
[2025-06-02T19:10:58.854Z] [ERROR] 2025-06-02T19:10:58.855Z vite:cache [memory] [2m/node_modules/.vite/deps/chunk-M7W2FPBU.js?v=cbff3303[22m
2025-06-02T19:10:58.855Z vite:cache [memory] [2m/node_modules/.vite/deps/chunk-PJEEZAML.js?v=cbff3303[22m
2025-06-02T19:10:58.855Z vite:cache [memory] [2m/node_modules/.vite/deps/chunk-DRWLMN53.js?v=cbff3303[22m
2025-06-02T19:10:58.855Z vite:cache [memory] [2m/node_modules/.vite/deps/chunk-G3PMV62Z.js?v=cbff3303[22m
2025-06-02T19:10:58.855Z vite:import-analysis [32m0.56ms[39m [2m[7 imports rewritten] [2mnode_modules/.vite/deps/chunk-JNXNBBFZ.js?v=cbff3303[22m[2m[22m
[2025-06-02T19:10:58.855Z] [ERROR] 2025-06-02T19:10:58.855Z vite:transform [32m0.65ms[39m [2m/node_modules/.vite/deps/chunk-JNXNBBFZ.js?v=cbff3303[22m
2025-06-02T19:10:58.855Z vite:load [33m33.02ms[39m [plugin] [2m/node_modules/.vite/deps/chunk-CHI5YKDN.js?v=cbff3303[22m
2025-06-02T19:10:58.855Z vite:cache [memory] [2m/node_modules/.vite/deps/chunk-M7W2FPBU.js?v=cbff3303[22m
2025-06-02T19:10:58.855Z vite:cache [memory] [2m/node_modules/.vite/deps/chunk-DRWLMN53.js?v=cbff3303[22m
2025-06-02T19:10:58.855Z vite:cache [memory] [2m/node_modules/.vite/deps/chunk-G3PMV62Z.js?v=cbff3303[22m
[2025-06-02T19:10:58.855Z] [ERROR] 2025-06-02T19:10:58.855Z vite:import-analysis [32m0.27ms[39m [2m[4 imports rewritten] [2mnode_modules/.vite/deps/chunk-CHI5YKDN.js?v=cbff3303[22m[2m[22m
2025-06-02T19:10:58.855Z vite:transform [32m0.34ms[39m [2m/node_modules/.vite/deps/chunk-CHI5YKDN.js?v=cbff3303[22m
2025-06-02T19:10:58.855Z vite:load [33m33.41ms[39m [plugin] [2m/node_modules/.vite/deps/chunk-UPJR475S.js?v=cbff3303[22m
[2025-06-02T19:10:58.856Z] [ERROR] 2025-06-02T19:10:58.855Z vite:cache [memory] [2m/node_modules/.vite/deps/chunk-DRWLMN53.js?v=cbff3303[22m
2025-06-02T19:10:58.855Z vite:cache [memory] [2m/node_modules/.vite/deps/chunk-G3PMV62Z.js?v=cbff3303[22m
2025-06-02T19:10:58.856Z vite:import-analysis [32m0.35ms[39m [2m[5 imports rewritten] [2mnode_modules/.vite/deps/chunk-UPJR475S.js?v=cbff3303[22m[2m[22m
2025-06-02T19:10:58.856Z vite:transform [32m0.42ms[39m [2m/node_modules/.vite/deps/chunk-UPJR475S.js?v=cbff3303[22m
2025-06-02T19:10:58.856Z vite:load [33m33.87ms[39m [plugin] [2m/node_modules/.vite/deps/chunk-QVMDSMUE.js?v=cbff3303[22m
[2025-06-02T19:10:58.856Z] [ERROR] 2025-06-02T19:10:58.856Z vite:cache [memory] [2m/node_modules/.vite/deps/chunk-PJEEZAML.js?v=cbff3303[22m
2025-06-02T19:10:58.856Z vite:cache [memory] [2m/node_modules/.vite/deps/chunk-DRWLMN53.js?v=cbff3303[22m
2025-06-02T19:10:58.856Z vite:cache [memory] [2m/node_modules/.vite/deps/chunk-G3PMV62Z.js?v=cbff3303[22m
2025-06-02T19:10:58.856Z vite:import-analysis [32m0.46ms[39m [2m[5 imports rewritten] [2mnode_modules/.vite/deps/chunk-QVMDSMUE.js?v=cbff3303[22m[2m[22m
2025-06-02T19:10:58.856Z vite:transform [32m0.54ms[39m [2m/node_modules/.vite/deps/chunk-QVMDSMUE.js?v=cbff3303[22m
[2025-06-02T19:10:58.856Z] [ERROR] 2025-06-02T19:10:58.856Z vite:load [33m34.51ms[39m [plugin] [2m/node_modules/.vite/deps/chunk-TLMPZXCM.js?v=cbff3303[22m
[2025-06-02T19:10:58.857Z] [ERROR] 2025-06-02T19:10:58.857Z vite:cache [memory] [2m/node_modules/.vite/deps/chunk-DRWLMN53.js?v=cbff3303[22m
2025-06-02T19:10:58.857Z vite:cache [memory] [2m/node_modules/.vite/deps/chunk-G3PMV62Z.js?v=cbff3303[22m
2025-06-02T19:10:58.857Z vite:import-analysis [32m0.31ms[39m [2m[3 imports rewritten] [2mnode_modules/.vite/deps/chunk-TLMPZXCM.js?v=cbff3303[22m[2m[22m
2025-06-02T19:10:58.857Z vite:transform [32m0.43ms[39m [2m/node_modules/.vite/deps/chunk-TLMPZXCM.js?v=cbff3303[22m
2025-06-02T19:10:58.857Z vite:load [33m35.05ms[39m [plugin] [2m/node_modules/.vite/deps/chunk-QNBT3DDS.js?v=cbff3303[22m
[2025-06-02T19:10:58.857Z] [ERROR] 2025-06-02T19:10:58.857Z vite:cache [memory] [2m/node_modules/.vite/deps/chunk-DRWLMN53.js?v=cbff3303[22m
2025-06-02T19:10:58.857Z vite:cache [memory] [2m/node_modules/.vite/deps/chunk-G3PMV62Z.js?v=cbff3303[22m
2025-06-02T19:10:58.857Z vite:import-analysis [32m0.26ms[39m [2m[3 imports rewritten] [2mnode_modules/.vite/deps/chunk-QNBT3DDS.js?v=cbff3303[22m[2m[22m
2025-06-02T19:10:58.857Z vite:transform [32m0.39ms[39m [2m/node_modules/.vite/deps/chunk-QNBT3DDS.js?v=cbff3303[22m
2025-06-02T19:10:58.857Z vite:load [33m35.49ms[39m [plugin] [2m/node_modules/.vite/deps/chunk-6PXSGDAH.js?v=cbff3303[22m
[2025-06-02T19:10:58.858Z] [ERROR] 2025-06-02T19:10:58.858Z vite:cache [memory] [2m/node_modules/.vite/deps/chunk-DRWLMN53.js?v=cbff3303[22m
2025-06-02T19:10:58.858Z vite:cache [memory] [2m/node_modules/.vite/deps/chunk-G3PMV62Z.js?v=cbff3303[22m
2025-06-02T19:10:58.858Z vite:import-analysis [32m0.37ms[39m [2m[2 imports rewritten] [2mnode_modules/.vite/deps/chunk-6PXSGDAH.js?v=cbff3303[22m[2m[22m
2025-06-02T19:10:58.858Z vite:transform [32m0.49ms[39m [2m/node_modules/.vite/deps/chunk-6PXSGDAH.js?v=cbff3303[22m
[2025-06-02T19:10:58.867Z] [ERROR] 2025-06-02T19:10:58.867Z vite:load [33m27.97ms[39m [plugin] [2m/node_modules/.vite/deps/chunk-UE3I4JV6.js?v=cbff3303[22m
[2025-06-02T19:10:58.868Z] [ERROR] 2025-06-02T19:10:58.868Z vite:cache [memory] [2m/node_modules/.vite/deps/chunk-M7W2FPBU.js?v=cbff3303[22m
2025-06-02T19:10:58.868Z vite:cache [memory] [2m/node_modules/.vite/deps/chunk-DRWLMN53.js?v=cbff3303[22m
2025-06-02T19:10:58.868Z vite:cache [memory] [2m/node_modules/.vite/deps/chunk-G3PMV62Z.js?v=cbff3303[22m
2025-06-02T19:10:58.868Z vite:import-analysis [32m0.44ms[39m [2m[3 imports rewritten] [2mnode_modules/.vite/deps/chunk-UE3I4JV6.js?v=cbff3303[22m[2m[22m
2025-06-02T19:10:58.868Z vite:transform [32m0.69ms[39m [2m/node_modules/.vite/deps/chunk-UE3I4JV6.js?v=cbff3303[22m
[2025-06-02T19:10:58.868Z] [ERROR] 2025-06-02T19:10:58.868Z vite:load [33m28.88ms[39m [plugin] [2m/node_modules/.vite/deps/chunk-PTKRTGFL.js?v=cbff3303[22m
[2025-06-02T19:10:58.869Z] [ERROR] 2025-06-02T19:10:58.869Z vite:cache [memory] [2m/node_modules/.vite/deps/chunk-M7W2FPBU.js?v=cbff3303[22m
2025-06-02T19:10:58.869Z vite:cache [memory] [2m/node_modules/.vite/deps/chunk-QVMDSMUE.js?v=cbff3303[22m
2025-06-02T19:10:58.869Z vite:cache [memory] [2m/node_modules/.vite/deps/chunk-TLMPZXCM.js?v=cbff3303[22m
2025-06-02T19:10:58.869Z vite:cache [memory] [2m/node_modules/.vite/deps/chunk-6PXSGDAH.js?v=cbff3303[22m
2025-06-02T19:10:58.869Z vite:cache [memory] [2m/node_modules/.vite/deps/chunk-DRWLMN53.js?v=cbff3303[22m
2025-06-02T19:10:58.869Z vite:cache [memory] [2m/node_modules/.vite/deps/chunk-G3PMV62Z.js?v=cbff3303[22m
[2025-06-02T19:10:58.869Z] [ERROR] 2025-06-02T19:10:58.869Z vite:import-analysis [32m0.78ms[39m [2m[6 imports rewritten] [2mnode_modules/.vite/deps/chunk-PTKRTGFL.js?v=cbff3303[22m[2m[22m
2025-06-02T19:10:58.869Z vite:transform [32m0.97ms[39m [2m/node_modules/.vite/deps/chunk-PTKRTGFL.js?v=cbff3303[22m
2025-06-02T19:10:58.869Z vite:load [33m28.62ms[39m [plugin] [2m/node_modules/.vite/deps/chunk-BLD7VZ7P.js?v=cbff3303[22m
[2025-06-02T19:10:58.870Z] [ERROR] 2025-06-02T19:10:58.870Z vite:cache [memory] [2m/node_modules/.vite/deps/chunk-6PXSGDAH.js?v=cbff3303[22m
2025-06-02T19:10:58.870Z vite:cache [memory] [2m/node_modules/.vite/deps/chunk-DRWLMN53.js?v=cbff3303[22m
2025-06-02T19:10:58.870Z vite:cache [memory] [2m/node_modules/.vite/deps/chunk-G3PMV62Z.js?v=cbff3303[22m
2025-06-02T19:10:58.870Z vite:import-analysis [32m0.24ms[39m [2m[3 imports rewritten] [2mnode_modules/.vite/deps/chunk-BLD7VZ7P.js?v=cbff3303[22m[2m[22m
2025-06-02T19:10:58.870Z vite:transform [32m0.31ms[39m [2m/node_modules/.vite/deps/chunk-BLD7VZ7P.js?v=cbff3303[22m
[2025-06-02T19:11:14.533Z] [ERROR] 2025-06-02T19:11:14.533Z vite:time [31m1071.12ms[39m [2m/?file=%2FUsers%2Fblack%2FDocuments%2Faugment-projects%2FvolcanoApp%2FvolcanoApp%2Fbackoffice%2Ffrontend%2Fsrc%2Futils%2Fdebug.ts%3A96%3A7[22m
[2025-06-02T19:11:14.537Z] [ERROR] 2025-06-02T19:11:14.537Z vite:hmr [file change] [2mlogs/vite-debug.log[22m
2025-06-02T19:11:14.537Z vite:hmr [no modules matched] [2mlogs/vite-debug.log[22m
[2025-06-02T19:12:19.170Z] [ERROR] 2025-06-02T19:12:19.170Z vite:hmr [file change] [2msrc/utils/debug.ts[22m
[2025-06-02T19:12:19.171Z] [ERROR] 2025-06-02T19:12:19.171Z vite:hmr [propagate update] stop propagation because not analyzed: [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/utils/debug.ts[22m
2025-06-02T19:12:19.171Z vite:hmr [33mno update happened [39m[2msrc/utils/debug.ts[22m
[2025-06-02T19:12:19.175Z] [ERROR] 2025-06-02T19:12:19.175Z vite:hmr [file change] [2mlogs/vite-debug.log[22m
2025-06-02T19:12:19.175Z vite:hmr [no modules matched] [2mlogs/vite-debug.log[22m
[2025-06-02T19:12:29.220Z] [ERROR] 2025-06-02T19:12:29.220Z vite:hmr [file change] [2msrc/utils/debug.ts[22m
[2025-06-02T19:12:29.221Z] [ERROR] 2025-06-02T19:12:29.220Z vite:hmr [propagate update] stop propagation because not analyzed: [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/utils/debug.ts[22m
2025-06-02T19:12:29.220Z vite:hmr [33mno update happened [39m[2msrc/utils/debug.ts[22m
[2025-06-02T19:12:29.225Z] [ERROR] 2025-06-02T19:12:29.225Z vite:hmr [file change] [2mlogs/vite-debug.log[22m
[2025-06-02T19:12:29.226Z] [ERROR] 2025-06-02T19:12:29.225Z vite:hmr [no modules matched] [2mlogs/vite-debug.log[22m
[2025-06-02T19:12:37.293Z] [ERROR] 2025-06-02T19:12:37.293Z vite:hmr [file change] [2msrc/utils/debug.ts[22m
[2025-06-02T19:12:37.293Z] [ERROR] 2025-06-02T19:12:37.293Z vite:hmr [propagate update] stop propagation because not analyzed: [2mC:/Users/<USER>/Documents/augment-projects/volcanoApp/volcanoApp/backoffice/frontend/src/utils/debug.ts[22m
2025-06-02T19:12:37.293Z vite:hmr [33mno update happened [39m[2msrc/utils/debug.ts[22m
[2025-06-02T19:12:37.296Z] [ERROR] 2025-06-02T19:12:37.296Z vite:hmr [file change] [2mlogs/vite-debug.log[22m
[2025-06-02T19:12:37.297Z] [ERROR] 2025-06-02T19:12:37.297Z vite:hmr [no modules matched] [2mlogs/vite-debug.log[22m
[2025-06-02T19:12:56.579Z] [ERROR] 2025-06-02T19:12:56.580Z vite:hmr [file change] [2mscripts/debug-monitor.js[22m
2025-06-02T19:12:56.580Z vite:hmr [no modules matched] [2mscripts/debug-monitor.js[22m
[2025-06-02T19:12:56.581Z] [ERROR] 2025-06-02T19:12:56.581Z vite:hmr [file change] [2mlogs/vite-debug.log[22m
2025-06-02T19:12:56.581Z vite:hmr [no modules matched] [2mlogs/vite-debug.log[22m
[2025-06-02T19:13:15.157Z] [ERROR] 2025-06-02T19:13:15.157Z vite:html-fallback Rewriting GET / to /index.html
[2025-06-02T19:13:15.163Z] [ERROR] 2025-06-02T19:13:15.163Z vite:cache [memory-hmr] [2m/src/main.tsx[22m
[2025-06-02T19:13:15.164Z] [ERROR] 2025-06-02T19:13:15.163Z vite:cache [memory] [2m/node_modules/.vite/deps/react_jsx-dev-runtime.js?v=cbff3303[22m
2025-06-02T19:13:15.163Z vite:cache [memory] [2m/node_modules/.vite/deps/react.js?v=cbff3303[22m
2025-06-02T19:13:15.163Z vite:cache [memory] [2m/node_modules/.vite/deps/react-dom_client.js?v=cbff3303[22m
[2025-06-02T19:13:15.165Z] [ERROR] 2025-06-02T19:13:15.165Z vite:cache [memory-hmr] [2m/src/App.tsx[22m
[2025-06-02T19:13:15.165Z] [ERROR] 2025-06-02T19:13:15.166Z vite:cache [memory] [2m/@react-refresh[22m
2025-06-02T19:13:15.166Z vite:cache [memory] [2m/node_modules/.vite/deps/axios.js?v=cbff3303[22m
2025-06-02T19:13:15.166Z vite:cache [memory] [2m/src/components/alerts/AlertDialog.tsx[22m
2025-06-02T19:13:15.166Z vite:cache [memory] [2m/src/components/alerts/AlertsTable.tsx[22m
2025-06-02T19:13:15.166Z vite:cache [memory] [2m/src/components/dashboard/DashboardStats.tsx[22m
[2025-06-02T19:13:15.166Z] [ERROR] 2025-06-02T19:13:15.167Z vite:cache [memory] [2m/src/components/layout/AppLayout.tsx[22m
[2025-06-02T19:13:15.167Z] [ERROR] 2025-06-02T19:13:15.167Z vite:cache [memory] [2m/src/components/ui/toaster.tsx[22m
2025-06-02T19:13:15.167Z vite:cache [memory] [2m/src/components/ZoneModal.tsx[22m
2025-06-02T19:13:15.167Z vite:cache [memory] [2m/src/components/zones/ZoneManagementClean.tsx[22m
2025-06-02T19:13:15.167Z vite:cache [memory] [2m/src/components/zones/ZonesTable.tsx[22m
2025-06-02T19:13:15.167Z vite:cache [memory] [2m/src/hooks/use-toast.ts[22m
2025-06-02T19:13:15.167Z vite:cache [memory-hmr] [2m/src/components/debug/DebugPanel.tsx[22m
2025-06-02T19:13:15.167Z vite:time [33m11.64ms[39m [2m/index.html[22m
[2025-06-02T19:13:15.168Z] [ERROR] 2025-06-02T19:13:15.168Z vite:cache [memory] [2m/node_modules/.vite/deps/react_jsx-dev-runtime.js?v=cbff3303[22m
[2025-06-02T19:13:15.169Z] [ERROR] 2025-06-02T19:13:15.168Z vite:cache [memory] [2m/node_modules/.vite/deps/react.js?v=cbff3303[22m
2025-06-02T19:13:15.168Z vite:cache [memory] [2m/node_modules/.vite/deps/lucide-react.js?v=cbff3303[22m
2025-06-02T19:13:15.168Z vite:cache [memory] [2m/src/components/ui/button.tsx[22m
2025-06-02T19:13:15.168Z vite:cache [memory] [2m/src/components/ui/card.tsx[22m
2025-06-02T19:13:15.168Z vite:cache [memory] [2m/src/components/ui/badge.tsx[22m
2025-06-02T19:13:15.168Z vite:cache [memory] [2m/src/components/ui/tabs.tsx[22m
[2025-06-02T19:13:15.171Z] [ERROR] 2025-06-02T19:13:15.172Z vite:hmr [file change] [2mlogs/vite-debug.log[22m
[2025-06-02T19:13:15.172Z] [ERROR] 2025-06-02T19:13:15.172Z vite:hmr [no modules matched] [2mlogs/vite-debug.log[22m
[2025-06-02T19:13:15.182Z] [ERROR] 2025-06-02T19:13:15.182Z vite:load [33m13.27ms[39m [fs] [2m/src/index.css[22m
[2025-06-02T19:13:15.202Z] [ERROR] 2025-06-02T19:13:15.202Z vite:load [33m33.14ms[39m [fs] [2m/src/utils/debug.ts[22m
[2025-06-02T19:13:15.208Z] [ERROR] 2025-06-02T19:13:15.209Z vite:import-analysis [32m0.35ms[39m [2m[0 imports rewritten] [2msrc/utils/debug.ts[22m[2m[22m
[2025-06-02T19:13:15.210Z] [ERROR] 2025-06-02T19:13:15.209Z vite:transform [32m6.71ms[39m [2m/src/utils/debug.ts[22m
[2025-06-02T19:13:15.381Z] [ERROR] 2025-06-02T19:13:15.381Z vite:hmr [self-accepts] [2msrc/index.css[22m
[2025-06-02T19:13:15.382Z] [ERROR] 2025-06-02T19:13:15.382Z vite:import-analysis [32m0.76ms[39m [2m[0 imports rewritten] [2msrc/index.css[22m[2m[22m
2025-06-02T19:13:15.382Z vite:transform [31m199.74ms[39m [2m/src/index.css[22m
[2025-06-02T19:13:15.384Z] [ERROR] 2025-06-02T19:13:15.384Z vite:cache [memory] [2m/src/main.tsx[22m
[2025-06-02T19:13:15.384Z] [ERROR] 2025-06-02T19:13:15.384Z vite:time [32m0.51ms[39m [2m/src/main.tsx[22m
[2025-06-02T19:13:15.386Z] [ERROR] 2025-06-02T19:13:15.386Z vite:hmr [file change] [2mlogs/vite-debug.log[22m
[2025-06-02T19:13:15.387Z] [ERROR] 2025-06-02T19:13:15.386Z vite:hmr [no modules matched] [2mlogs/vite-debug.log[22m
[2025-06-02T19:13:15.389Z] [ERROR] 2025-06-02T19:13:15.390Z vite:cache [memory] [2m/@vite/client[22m
[2025-06-02T19:13:15.391Z] [ERROR] 2025-06-02T19:13:15.390Z vite:time [32m0.53ms[39m [2m/@vite/client[22m
[2025-06-02T19:13:15.391Z] [ERROR] 2025-06-02T19:13:15.391Z vite:cache [304] [2m/@react-refresh[22m
2025-06-02T19:13:15.391Z vite:time [32m0.18ms[39m [2m/@react-refresh[22m
[2025-06-02T19:13:15.405Z] [ERROR] 2025-06-02T19:13:15.405Z vite:cache [memory] [2m/src/App.tsx[22m
[2025-06-02T19:13:15.406Z] [ERROR] 2025-06-02T19:13:15.406Z vite:time [32m0.95ms[39m [2m/src/App.tsx[22m
[2025-06-02T19:13:15.409Z] [ERROR] 2025-06-02T19:13:15.409Z vite:cache [memory] [2m/src/index.css[22m
[2025-06-02T19:13:15.411Z] [ERROR] 2025-06-02T19:13:15.409Z vite:time [32m0.71ms[39m [2m/src/index.css[22m
[2025-06-02T19:13:15.437Z] [ERROR] 2025-06-02T19:13:15.437Z vite:cache [memory] [2m/src/components/debug/DebugPanel.tsx[22m
[2025-06-02T19:13:15.438Z] [ERROR] 2025-06-02T19:13:15.438Z vite:time [32m0.88ms[39m [2m/src/components/debug/DebugPanel.tsx[22m
[2025-06-02T19:13:15.440Z] [ERROR] 2025-06-02T19:13:15.440Z vite:cache [memory] [2m/src/utils/debug.ts[22m
[2025-06-02T19:13:15.441Z] [ERROR] 2025-06-02T19:13:15.441Z vite:time [32m0.59ms[39m [2m/src/utils/debug.ts[22m
[2025-06-02T19:13:15.442Z] [ERROR] 2025-06-02T19:13:15.442Z vite:cache [304] [2m/node_modules/vite/dist/client/env.mjs[22m
2025-06-02T19:13:15.442Z vite:time [32m0.17ms[39m [2m/node_modules/vite/dist/client/env.mjs[22m
2025-06-02T19:13:15.442Z vite:hmr [file change] [2mlogs/vite-debug.log[22m
2025-06-02T19:13:15.442Z vite:hmr [no modules matched] [2mlogs/vite-debug.log[22m
[2025-06-02T19:13:15.444Z] [ERROR] 2025-06-02T19:13:15.444Z vite:cache [304] [2m/src/components/alerts/AlertDialog.tsx[22m
[2025-06-02T19:13:15.446Z] [ERROR] 2025-06-02T19:13:15.445Z vite:time [32m0.31ms[39m [2m/src/components/alerts/AlertDialog.tsx[22m
2025-06-02T19:13:15.445Z vite:cache [304] [2m/src/components/alerts/AlertsTable.tsx[22m
2025-06-02T19:13:15.445Z vite:time [32m0.11ms[39m [2m/src/components/alerts/AlertsTable.tsx[22m
[2025-06-02T19:13:15.448Z] [ERROR] 2025-06-02T19:13:15.446Z vite:cache [304] [2m/src/components/ZoneModal.tsx[22m
2025-06-02T19:13:15.446Z vite:time [32m0.24ms[39m [2m/src/components/ZoneModal.tsx[22m
2025-06-02T19:13:15.447Z vite:cache [304] [2m/src/components/zones/ZonesTable.tsx[22m
2025-06-02T19:13:15.447Z vite:time [32m0.10ms[39m [2m/src/components/zones/ZonesTable.tsx[22m
2025-06-02T19:13:15.447Z vite:cache [304] [2m/src/components/dashboard/DashboardStats.tsx[22m
2025-06-02T19:13:15.448Z vite:time [32m0.07ms[39m [2m/src/components/dashboard/DashboardStats.tsx[22m
[2025-06-02T19:13:15.451Z] [ERROR] 2025-06-02T19:13:15.451Z vite:cache [304] [2m/src/hooks/use-toast.ts[22m
2025-06-02T19:13:15.451Z vite:time [32m0.29ms[39m [2m/src/hooks/use-toast.ts[22m
[2025-06-02T19:13:15.452Z] [ERROR] 2025-06-02T19:13:15.452Z vite:cache [304] [2m/src/components/layout/AppLayout.tsx[22m
2025-06-02T19:13:15.452Z vite:time [32m0.12ms[39m [2m/src/components/layout/AppLayout.tsx[22m
2025-06-02T19:13:15.452Z vite:cache [304] [2m/src/components/ui/toaster.tsx[22m
2025-06-02T19:13:15.452Z vite:time [32m0.10ms[39m [2m/src/components/ui/toaster.tsx[22m
[2025-06-02T19:13:15.453Z] [ERROR] 2025-06-02T19:13:15.453Z vite:cache [304] [2m/src/components/zones/ZoneManagementClean.tsx[22m
2025-06-02T19:13:15.453Z vite:time [32m0.09ms[39m [2m/src/components/zones/ZoneManagementClean.tsx[22m
[2025-06-02T19:13:15.492Z] [ERROR] 2025-06-02T19:13:15.492Z vite:cache [304] [2m/src/components/ui/button.tsx[22m
2025-06-02T19:13:15.492Z vite:time [32m0.26ms[39m [2m/src/components/ui/button.tsx[22m
[2025-06-02T19:13:15.494Z] [ERROR] 2025-06-02T19:13:15.493Z vite:cache [304] [2m/src/components/ui/card.tsx[22m
2025-06-02T19:13:15.493Z vite:time [32m0.35ms[39m [2m/src/components/ui/card.tsx[22m
[2025-06-02T19:13:15.494Z] [ERROR] 2025-06-02T19:13:15.494Z vite:cache [304] [2m/src/components/ui/badge.tsx[22m
2025-06-02T19:13:15.494Z vite:time [32m0.12ms[39m [2m/src/components/ui/badge.tsx[22m
[2025-06-02T19:13:15.495Z] [ERROR] 2025-06-02T19:13:15.495Z vite:cache [304] [2m/src/components/ui/tabs.tsx[22m
2025-06-02T19:13:15.495Z vite:time [32m0.07ms[39m [2m/src/components/ui/tabs.tsx[22m
[2025-06-02T19:13:15.498Z] [ERROR] 2025-06-02T19:13:15.498Z vite:hmr [file change] [2mlogs/vite-debug.log[22m
2025-06-02T19:13:15.498Z vite:hmr [no modules matched] [2mlogs/vite-debug.log[22m
[2025-06-02T19:13:15.515Z] [ERROR] 2025-06-02T19:13:15.516Z vite:cache [304] [2m/src/components/ui/dialog.tsx[22m
2025-06-02T19:13:15.516Z vite:time [32m0.18ms[39m [2m/src/components/ui/dialog.tsx[22m
[2025-06-02T19:13:15.517Z] [ERROR] 2025-06-02T19:13:15.516Z vite:cache [304] [2m/src/components/ui/input.tsx[22m
2025-06-02T19:13:15.516Z vite:time [32m0.09ms[39m [2m/src/components/ui/input.tsx[22m
2025-06-02T19:13:15.517Z vite:cache [304] [2m/src/components/ui/label.tsx[22m
2025-06-02T19:13:15.517Z vite:time [32m0.11ms[39m [2m/src/components/ui/label.tsx[22m
[2025-06-02T19:13:15.517Z] [ERROR] 2025-06-02T19:13:15.517Z vite:cache [304] [2m/src/components/ui/select.tsx[22m
2025-06-02T19:13:15.517Z vite:time [32m0.07ms[39m [2m/src/components/ui/select.tsx[22m
2025-06-02T19:13:15.518Z vite:cache [304] [2m/src/components/ui/textarea.tsx[22m
2025-06-02T19:13:15.518Z vite:time [32m0.04ms[39m [2m/src/components/ui/textarea.tsx[22m
[2025-06-02T19:13:15.518Z] [ERROR] 2025-06-02T19:13:15.518Z vite:cache [304] [2m/src/lib/utils.ts[22m
2025-06-02T19:13:15.518Z vite:time [32m0.05ms[39m [2m/src/lib/utils.ts[22m
2025-06-02T19:13:15.518Z vite:cache [304] [2m/src/components/ui/table.tsx[22m
2025-06-02T19:13:15.518Z vite:time [32m0.05ms[39m [2m/src/components/ui/table.tsx[22m
[2025-06-02T19:13:15.522Z] [ERROR] 2025-06-02T19:13:15.522Z vite:cache [304] [2m/src/components/ui/progress.tsx[22m
2025-06-02T19:13:15.522Z vite:time [32m0.25ms[39m [2m/src/components/ui/progress.tsx[22m
[2025-06-02T19:13:15.523Z] [ERROR] 2025-06-02T19:13:15.523Z vite:cache [304] [2m/src/components/ui/sidebar.tsx[22m
2025-06-02T19:13:15.523Z vite:time [32m0.16ms[39m [2m/src/components/ui/sidebar.tsx[22m
[2025-06-02T19:13:15.524Z] [ERROR] 2025-06-02T19:13:15.524Z vite:cache [304] [2m/src/components/ui/toast.tsx[22m
2025-06-02T19:13:15.524Z vite:time [32m0.12ms[39m [2m/src/components/ui/toast.tsx[22m
[2025-06-02T19:13:57.643Z] [ERROR] 2025-06-02T19:13:57.643Z vite:html-fallback Rewriting GET / to /index.html
[2025-06-02T19:13:57.645Z] [ERROR] 2025-06-02T19:13:57.645Z vite:cache [memory] [2m/src/main.tsx[22m
[2025-06-02T19:13:57.646Z] [ERROR] 2025-06-02T19:13:57.646Z vite:time [32m3.16ms[39m [2m/index.html[22m
[2025-06-02T19:13:57.648Z] [ERROR] 2025-06-02T19:13:57.648Z vite:hmr [file change] [2mlogs/vite-debug.log[22m
[2025-06-02T19:13:57.649Z] [ERROR] 2025-06-02T19:13:57.648Z vite:hmr [no modules matched] [2mlogs/vite-debug.log[22m
[2025-06-02T19:13:57.673Z] [ERROR] 2025-06-02T19:13:57.673Z vite:cache [memory] [2m/@vite/client[22m
[2025-06-02T19:13:57.673Z] [ERROR] 2025-06-02T19:13:57.673Z vite:time [32m0.45ms[39m [2m/@vite/client[22m
[2025-06-02T19:13:57.675Z] [ERROR] 2025-06-02T19:13:57.675Z vite:cache [memory] [2m/src/main.tsx[22m
[2025-06-02T19:13:57.676Z] [ERROR] 2025-06-02T19:13:57.675Z vite:time [32m0.27ms[39m [2m/src/main.tsx[22m
[2025-06-02T19:13:57.677Z] [ERROR] 2025-06-02T19:13:57.677Z vite:cache [304] [2m/@react-refresh[22m
2025-06-02T19:13:57.677Z vite:time [32m0.16ms[39m [2m/@react-refresh[22m
[2025-06-02T19:13:57.692Z] [ERROR] 2025-06-02T19:13:57.692Z vite:cache [memory] [2m/src/App.tsx[22m
[2025-06-02T19:13:57.693Z] [ERROR] 2025-06-02T19:13:57.692Z vite:time [32m0.28ms[39m [2m/src/App.tsx[22m
2025-06-02T19:13:57.693Z vite:cache [304] [2m/node_modules/vite/dist/client/env.mjs[22m
2025-06-02T19:13:57.693Z vite:time [32m0.08ms[39m [2m/node_modules/vite/dist/client/env.mjs[22m
[2025-06-02T19:13:57.694Z] [ERROR] 2025-06-02T19:13:57.693Z vite:cache [304] [2m/src/index.css[22m
2025-06-02T19:13:57.693Z vite:time [32m0.21ms[39m [2m/src/index.css[22m
[2025-06-02T19:13:57.713Z] [ERROR] 2025-06-02T19:13:57.713Z vite:cache [304] [2m/src/components/alerts/AlertDialog.tsx[22m
2025-06-02T19:13:57.713Z vite:time [32m0.21ms[39m [2m/src/components/alerts/AlertDialog.tsx[22m
[2025-06-02T19:13:57.714Z] [ERROR] 2025-06-02T19:13:57.714Z vite:cache [memory] [2m/src/components/debug/DebugPanel.tsx[22m
2025-06-02T19:13:57.714Z vite:time [32m0.25ms[39m [2m/src/components/debug/DebugPanel.tsx[22m
[2025-06-02T19:13:57.715Z] [ERROR] 2025-06-02T19:13:57.715Z vite:cache [304] [2m/src/components/alerts/AlertsTable.tsx[22m
2025-06-02T19:13:57.715Z vite:time [32m0.09ms[39m [2m/src/components/alerts/AlertsTable.tsx[22m
[2025-06-02T19:13:57.715Z] [ERROR] 2025-06-02T19:13:57.715Z vite:cache [304] [2m/src/components/dashboard/DashboardStats.tsx[22m
2025-06-02T19:13:57.715Z vite:time [32m0.21ms[39m [2m/src/components/dashboard/DashboardStats.tsx[22m
[2025-06-02T19:13:57.717Z] [ERROR] 2025-06-02T19:13:57.717Z vite:cache [304] [2m/src/components/layout/AppLayout.tsx[22m
[2025-06-02T19:13:57.718Z] [ERROR] 2025-06-02T19:13:57.717Z vite:time [32m0.20ms[39m [2m/src/components/layout/AppLayout.tsx[22m
2025-06-02T19:13:57.718Z vite:cache [memory] [2m/src/utils/debug.ts[22m
2025-06-02T19:13:57.718Z vite:time [32m0.26ms[39m [2m/src/utils/debug.ts[22m
[2025-06-02T19:13:57.719Z] [ERROR] 2025-06-02T19:13:57.719Z vite:cache [304] [2m/src/components/ui/toaster.tsx[22m
2025-06-02T19:13:57.719Z vite:time [32m0.07ms[39m [2m/src/components/ui/toaster.tsx[22m
[2025-06-02T19:13:57.719Z] [ERROR] 2025-06-02T19:13:57.719Z vite:cache [304] [2m/src/components/ZoneModal.tsx[22m
2025-06-02T19:13:57.719Z vite:time [32m0.09ms[39m [2m/src/components/ZoneModal.tsx[22m
[2025-06-02T19:13:57.720Z] [ERROR] 2025-06-02T19:13:57.720Z vite:cache [304] [2m/src/components/zones/ZonesTable.tsx[22m
2025-06-02T19:13:57.720Z vite:time [32m0.08ms[39m [2m/src/components/zones/ZonesTable.tsx[22m
[2025-06-02T19:13:57.720Z] [ERROR] 2025-06-02T19:13:57.721Z vite:hmr [file change] [2mlogs/vite-debug.log[22m
2025-06-02T19:13:57.721Z vite:hmr [no modules matched] [2mlogs/vite-debug.log[22m
[2025-06-02T19:13:57.724Z] [ERROR] 2025-06-02T19:13:57.724Z vite:cache [304] [2m/src/components/zones/ZoneManagementClean.tsx[22m
[2025-06-02T19:13:57.725Z] [ERROR] 2025-06-02T19:13:57.724Z vite:time [32m0.61ms[39m [2m/src/components/zones/ZoneManagementClean.tsx[22m
2025-06-02T19:13:57.725Z vite:cache [304] [2m/src/hooks/use-toast.ts[22m
2025-06-02T19:13:57.725Z vite:time [32m0.09ms[39m [2m/src/hooks/use-toast.ts[22m
[2025-06-02T19:13:57.750Z] [ERROR] 2025-06-02T19:13:57.750Z vite:cache [304] [2m/src/components/ui/badge.tsx[22m
[2025-06-02T19:13:57.750Z] [ERROR] 2025-06-02T19:13:57.750Z vite:time [32m0.21ms[39m [2m/src/components/ui/badge.tsx[22m
2025-06-02T19:13:57.750Z vite:cache [304] [2m/src/components/ui/button.tsx[22m
2025-06-02T19:13:57.750Z vite:time [32m0.06ms[39m [2m/src/components/ui/button.tsx[22m
[2025-06-02T19:13:57.751Z] [ERROR] 2025-06-02T19:13:57.751Z vite:cache [304] [2m/src/components/ui/card.tsx[22m
2025-06-02T19:13:57.751Z vite:time [32m0.06ms[39m [2m/src/components/ui/card.tsx[22m
2025-06-02T19:13:57.751Z vite:cache [304] [2m/src/components/ui/dialog.tsx[22m
2025-06-02T19:13:57.751Z vite:time [32m0.04ms[39m [2m/src/components/ui/dialog.tsx[22m
[2025-06-02T19:13:57.752Z] [ERROR] 2025-06-02T19:13:57.752Z vite:cache [304] [2m/src/components/ui/input.tsx[22m
[2025-06-02T19:13:57.752Z] [ERROR] 2025-06-02T19:13:57.752Z vite:time [32m0.11ms[39m [2m/src/components/ui/input.tsx[22m
2025-06-02T19:13:57.753Z vite:cache [304] [2m/src/components/ui/label.tsx[22m
2025-06-02T19:13:57.753Z vite:time [32m0.08ms[39m [2m/src/components/ui/label.tsx[22m
[2025-06-02T19:13:57.753Z] [ERROR] 2025-06-02T19:13:57.753Z vite:cache [304] [2m/src/components/ui/select.tsx[22m
2025-06-02T19:13:57.753Z vite:time [32m0.06ms[39m [2m/src/components/ui/select.tsx[22m
[2025-06-02T19:13:57.753Z] [ERROR] 2025-06-02T19:13:57.753Z vite:cache [304] [2m/src/components/ui/textarea.tsx[22m
2025-06-02T19:13:57.753Z vite:time [32m0.05ms[39m [2m/src/components/ui/textarea.tsx[22m
[2025-06-02T19:13:57.754Z] [ERROR] 2025-06-02T19:13:57.754Z vite:cache [304] [2m/src/components/ui/tabs.tsx[22m
2025-06-02T19:13:57.754Z vite:time [32m0.05ms[39m [2m/src/components/ui/tabs.tsx[22m
[2025-06-02T19:13:57.758Z] [ERROR] 2025-06-02T19:13:57.758Z vite:cache [304] [2m/src/lib/utils.ts[22m
2025-06-02T19:13:57.758Z vite:time [32m0.15ms[39m [2m/src/lib/utils.ts[22m
[2025-06-02T19:13:57.759Z] [ERROR] 2025-06-02T19:13:57.759Z vite:cache [304] [2m/src/components/ui/table.tsx[22m
2025-06-02T19:13:57.759Z vite:time [32m0.07ms[39m [2m/src/components/ui/table.tsx[22m
2025-06-02T19:13:57.759Z vite:cache [304] [2m/src/components/ui/progress.tsx[22m
[2025-06-02T19:13:57.760Z] [ERROR] 2025-06-02T19:13:57.759Z vite:time [32m0.06ms[39m [2m/src/components/ui/progress.tsx[22m
[2025-06-02T19:13:57.760Z] [ERROR] 2025-06-02T19:13:57.760Z vite:cache [304] [2m/src/components/ui/sidebar.tsx[22m
2025-06-02T19:13:57.760Z vite:time [32m0.08ms[39m [2m/src/components/ui/sidebar.tsx[22m
[2025-06-02T19:13:57.762Z] [ERROR] 2025-06-02T19:13:57.762Z vite:cache [304] [2m/src/components/ui/toast.tsx[22m
[2025-06-02T19:13:57.762Z] [ERROR] 2025-06-02T19:13:57.762Z vite:time [32m0.12ms[39m [2m/src/components/ui/toast.tsx[22m
[2025-06-02T19:14:22.593Z] [ERROR] 2025-06-02T19:14:22.593Z vite:html-fallback Rewriting GET / to /index.html
[2025-06-02T19:14:22.597Z] [ERROR] 2025-06-02T19:14:22.597Z vite:cache [memory] [2m/src/main.tsx[22m
[2025-06-02T19:14:22.598Z] [ERROR] 2025-06-02T19:14:22.597Z vite:time [32m4.11ms[39m [2m/index.html[22m
[2025-06-02T19:14:22.599Z] [ERROR] 2025-06-02T19:14:22.599Z vite:hmr [file change] [2mlogs/vite-debug.log[22m
[2025-06-02T19:14:22.600Z] [ERROR] 2025-06-02T19:14:22.600Z vite:hmr [no modules matched] [2mlogs/vite-debug.log[22m
[2025-06-02T19:14:22.659Z] [ERROR] 2025-06-02T19:14:22.659Z vite:cache [memory] [2m/@vite/client[22m
[2025-06-02T19:14:22.660Z] [ERROR] 2025-06-02T19:14:22.659Z vite:time [32m0.59ms[39m [2m/@vite/client[22m
[2025-06-02T19:14:22.661Z] [ERROR] 2025-06-02T19:14:22.661Z vite:hmr [file change] [2mlogs/vite-debug.log[22m
2025-06-02T19:14:22.661Z vite:hmr [no modules matched] [2mlogs/vite-debug.log[22m
[2025-06-02T19:14:22.665Z] [ERROR] 2025-06-02T19:14:22.665Z vite:cache [memory] [2m/src/main.tsx[22m
[2025-06-02T19:14:22.666Z] [ERROR] 2025-06-02T19:14:22.665Z vite:time [32m0.42ms[39m [2m/src/main.tsx[22m
[2025-06-02T19:14:22.683Z] [ERROR] 2025-06-02T19:14:22.683Z vite:cache [304] [2m/@react-refresh[22m
2025-06-02T19:14:22.683Z vite:time [32m0.25ms[39m [2m/@react-refresh[22m
[2025-06-02T19:14:22.718Z] [ERROR] 2025-06-02T19:14:22.718Z vite:cache [304] [2m/node_modules/vite/dist/client/env.mjs[22m
2025-06-02T19:14:22.718Z vite:time [32m0.26ms[39m [2m/node_modules/vite/dist/client/env.mjs[22m
[2025-06-02T19:14:22.722Z] [ERROR] 2025-06-02T19:14:22.722Z vite:cache [memory] [2m/src/App.tsx[22m
[2025-06-02T19:14:22.722Z] [ERROR] 2025-06-02T19:14:22.722Z vite:time [32m0.38ms[39m [2m/src/App.tsx[22m
[2025-06-02T19:14:22.723Z] [ERROR] 2025-06-02T19:14:22.723Z vite:hmr [file change] [2mlogs/vite-debug.log[22m
2025-06-02T19:14:22.723Z vite:hmr [no modules matched] [2mlogs/vite-debug.log[22m
[2025-06-02T19:14:22.723Z] [ERROR] 2025-06-02T19:14:22.723Z vite:cache [304] [2m/src/index.css[22m
2025-06-02T19:14:22.723Z vite:time [32m0.27ms[39m [2m/src/index.css[22m
[2025-06-02T19:14:22.758Z] [ERROR] 2025-06-02T19:14:22.758Z vite:cache [304] [2m/src/components/alerts/AlertDialog.tsx[22m
[2025-06-02T19:14:22.758Z] [ERROR] 2025-06-02T19:14:22.758Z vite:time [32m0.23ms[39m [2m/src/components/alerts/AlertDialog.tsx[22m
2025-06-02T19:14:22.758Z vite:cache [304] [2m/src/components/alerts/AlertsTable.tsx[22m
2025-06-02T19:14:22.758Z vite:time [32m0.08ms[39m [2m/src/components/alerts/AlertsTable.tsx[22m
[2025-06-02T19:14:22.759Z] [ERROR] 2025-06-02T19:14:22.759Z vite:cache [304] [2m/src/components/dashboard/DashboardStats.tsx[22m
2025-06-02T19:14:22.759Z vite:time [32m0.07ms[39m [2m/src/components/dashboard/DashboardStats.tsx[22m
[2025-06-02T19:14:22.761Z] [ERROR] 2025-06-02T19:14:22.761Z vite:cache [memory] [2m/src/components/debug/DebugPanel.tsx[22m
[2025-06-02T19:14:22.762Z] [ERROR] 2025-06-02T19:14:22.762Z vite:time [32m0.48ms[39m [2m/src/components/debug/DebugPanel.tsx[22m
2025-06-02T19:14:22.762Z vite:cache [304] [2m/src/components/layout/AppLayout.tsx[22m
2025-06-02T19:14:22.762Z vite:time [32m0.17ms[39m [2m/src/components/layout/AppLayout.tsx[22m
[2025-06-02T19:14:22.763Z] [ERROR] 2025-06-02T19:14:22.763Z vite:cache [304] [2m/src/components/ui/toaster.tsx[22m
2025-06-02T19:14:22.763Z vite:time [32m0.13ms[39m [2m/src/components/ui/toaster.tsx[22m
[2025-06-02T19:14:22.764Z] [ERROR] 2025-06-02T19:14:22.764Z vite:cache [memory] [2m/src/utils/debug.ts[22m
2025-06-02T19:14:22.764Z vite:time [32m0.32ms[39m [2m/src/utils/debug.ts[22m
[2025-06-02T19:14:22.765Z] [ERROR] 2025-06-02T19:14:22.765Z vite:cache [304] [2m/src/components/ZoneModal.tsx[22m
2025-06-02T19:14:22.765Z vite:time [32m0.19ms[39m [2m/src/components/ZoneModal.tsx[22m
[2025-06-02T19:14:22.770Z] [ERROR] 2025-06-02T19:14:22.770Z vite:cache [304] [2m/src/hooks/use-toast.ts[22m
2025-06-02T19:14:22.770Z vite:time [32m0.18ms[39m [2m/src/hooks/use-toast.ts[22m
[2025-06-02T19:14:22.771Z] [ERROR] 2025-06-02T19:14:22.771Z vite:cache [304] [2m/src/components/zones/ZoneManagementClean.tsx[22m
2025-06-02T19:14:22.771Z vite:time [32m0.17ms[39m [2m/src/components/zones/ZoneManagementClean.tsx[22m
[2025-06-02T19:14:22.771Z] [ERROR] 2025-06-02T19:14:22.771Z vite:cache [304] [2m/src/components/zones/ZonesTable.tsx[22m
2025-06-02T19:14:22.771Z vite:time [32m0.08ms[39m [2m/src/components/zones/ZonesTable.tsx[22m
[2025-06-02T19:14:22.777Z] [ERROR] 2025-06-02T19:14:22.777Z vite:hmr [file change] [2mlogs/vite-debug.log[22m
2025-06-02T19:14:22.777Z vite:hmr [no modules matched] [2mlogs/vite-debug.log[22m
[2025-06-02T19:14:22.797Z] [ERROR] 2025-06-02T19:14:22.798Z vite:cache [304] [2m/src/lib/utils.ts[22m
2025-06-02T19:14:22.798Z vite:time [32m0.18ms[39m [2m/src/lib/utils.ts[22m
[2025-06-02T19:14:22.798Z] [ERROR] 2025-06-02T19:14:22.798Z vite:cache [304] [2m/src/components/ui/badge.tsx[22m
2025-06-02T19:14:22.798Z vite:time [32m0.08ms[39m [2m/src/components/ui/badge.tsx[22m
2025-06-02T19:14:22.799Z vite:cache [304] [2m/src/components/ui/button.tsx[22m
2025-06-02T19:14:22.799Z vite:time [32m0.08ms[39m [2m/src/components/ui/button.tsx[22m
[2025-06-02T19:14:22.799Z] [ERROR] 2025-06-02T19:14:22.799Z vite:cache [304] [2m/src/components/ui/card.tsx[22m
2025-06-02T19:14:22.799Z vite:time [32m0.08ms[39m [2m/src/components/ui/card.tsx[22m
[2025-06-02T19:14:22.800Z] [ERROR] 2025-06-02T19:14:22.799Z vite:cache [304] [2m/src/components/ui/table.tsx[22m
2025-06-02T19:14:22.799Z vite:time [32m0.09ms[39m [2m/src/components/ui/table.tsx[22m
[2025-06-02T19:14:22.801Z] [ERROR] 2025-06-02T19:14:22.801Z vite:cache [304] [2m/src/components/ui/dialog.tsx[22m
[2025-06-02T19:14:22.802Z] [ERROR] 2025-06-02T19:14:22.801Z vite:time [32m0.19ms[39m [2m/src/components/ui/dialog.tsx[22m
2025-06-02T19:14:22.802Z vite:cache [304] [2m/src/components/ui/input.tsx[22m
2025-06-02T19:14:22.802Z vite:time [32m0.09ms[39m [2m/src/components/ui/input.tsx[22m
[2025-06-02T19:14:22.802Z] [ERROR] 2025-06-02T19:14:22.802Z vite:cache [304] [2m/src/components/ui/label.tsx[22m
2025-06-02T19:14:22.802Z vite:time [32m0.06ms[39m [2m/src/components/ui/label.tsx[22m
[2025-06-02T19:14:22.803Z] [ERROR] 2025-06-02T19:14:22.802Z vite:cache [304] [2m/src/components/ui/select.tsx[22m
2025-06-02T19:14:22.802Z vite:time [32m0.06ms[39m [2m/src/components/ui/select.tsx[22m
2025-06-02T19:14:22.803Z vite:cache [304] [2m/src/components/ui/textarea.tsx[22m
2025-06-02T19:14:22.803Z vite:time [32m0.04ms[39m [2m/src/components/ui/textarea.tsx[22m
[2025-06-02T19:14:22.803Z] [ERROR] 2025-06-02T19:14:22.803Z vite:cache [304] [2m/src/components/ui/progress.tsx[22m
2025-06-02T19:14:22.803Z vite:time [32m0.04ms[39m [2m/src/components/ui/progress.tsx[22m
[2025-06-02T19:14:22.805Z] [ERROR] 2025-06-02T19:14:22.805Z vite:cache [304] [2m/src/components/ui/tabs.tsx[22m
[2025-06-02T19:14:22.806Z] [ERROR] 2025-06-02T19:14:22.805Z vite:time [32m0.18ms[39m [2m/src/components/ui/tabs.tsx[22m
2025-06-02T19:14:22.806Z vite:cache [304] [2m/src/components/ui/sidebar.tsx[22m
2025-06-02T19:14:22.806Z vite:time [32m0.08ms[39m [2m/src/components/ui/sidebar.tsx[22m
[2025-06-02T19:14:22.807Z] [ERROR] 2025-06-02T19:14:22.806Z vite:cache [304] [2m/src/components/ui/toast.tsx[22m
2025-06-02T19:14:22.806Z vite:time [32m0.06ms[39m [2m/src/components/ui/toast.tsx[22m
[2025-06-02T19:14:29.554Z] [ERROR] 2025-06-02T19:14:29.554Z vite:time [32m4.06ms[39m [2m/node_modules/.vite/deps/react.js.map[22m
[2025-06-02T19:14:29.555Z] [ERROR] 2025-06-02T19:14:29.555Z vite:time [32m7.22ms[39m [2m/node_modules/.vite/deps/chunk-QVMDSMUE.js.map[22m
[2025-06-02T19:14:29.555Z] [ERROR] 2025-06-02T19:14:29.555Z vite:time [32m3.49ms[39m [2m/node_modules/.vite/deps/@radix-ui_react-slot.js.map[22m
[2025-06-02T19:14:29.558Z] [ERROR] 2025-06-02T19:14:29.559Z vite:time [32m6.67ms[39m [2m/node_modules/.vite/deps/chunk-CHI5YKDN.js.map[22m
[2025-06-02T19:14:29.560Z] [ERROR] 2025-06-02T19:14:29.560Z vite:time [32m9.77ms[39m [2m/node_modules/.vite/deps/chunk-TLMPZXCM.js.map[22m
[2025-06-02T19:14:29.565Z] [ERROR] 2025-06-02T19:14:29.565Z vite:time [33m12.48ms[39m [2m/node_modules/.vite/deps/@radix-ui_react-select.js.map[22m
[2025-06-02T19:14:29.566Z] [ERROR] 2025-06-02T19:14:29.565Z vite:hmr [file change] [2mlogs/vite-debug.log[22m
2025-06-02T19:14:29.566Z vite:hmr [no modules matched] [2mlogs/vite-debug.log[22m
[2025-06-02T19:14:29.574Z] [ERROR] 2025-06-02T19:14:29.574Z vite:resolve [32m5.00ms[39m [36m/installHook.js[39m -> [2mnull[22m
[2025-06-02T19:14:29.577Z] [ERROR] 2025-06-02T19:14:29.577Z vite:html-fallback Rewriting GET /installHook.js.map to /index.html
[2025-06-02T19:14:29.583Z] [ERROR] 2025-06-02T19:14:29.583Z vite:time [33m26.49ms[39m [2m/node_modules/.vite/deps/chunk-UPJR475S.js.map[22m
[2025-06-02T19:14:29.584Z] [ERROR] 2025-06-02T19:14:29.584Z vite:time [33m26.41ms[39m [2m/node_modules/.vite/deps/react-dom_client.js.map[22m
[2025-06-02T19:14:29.586Z] [ERROR] 2025-06-02T19:14:29.586Z vite:time [33m27.48ms[39m [2m/node_modules/.vite/deps/clsx.js.map[22m
[2025-06-02T19:14:29.591Z] [ERROR] 2025-06-02T19:14:29.591Z vite:cache [memory] [2m/src/main.tsx[22m
[2025-06-02T19:14:29.592Z] [ERROR] 2025-06-02T19:14:29.591Z vite:time [33m22.88ms[39m [2m/index.html[22m
[2025-06-02T19:14:29.593Z] [ERROR] 2025-06-02T19:14:29.592Z vite:time [33m14.48ms[39m [2m/node_modules/.vite/deps/chunk-G3PMV62Z.js.map[22m
[2025-06-02T19:14:29.594Z] [ERROR] 2025-06-02T19:14:29.594Z vite:time [33m25.89ms[39m [2m/node_modules/.vite/deps/chunk-PTKRTGFL.js.map[22m
[2025-06-02T19:14:29.600Z] [ERROR] 2025-06-02T19:14:29.600Z vite:time [33m15.29ms[39m [2m/node_modules/.vite/deps/@radix-ui_react-label.js.map[22m
[2025-06-02T19:14:29.601Z] [ERROR] 2025-06-02T19:14:29.601Z vite:time [33m13.06ms[39m [2m/node_modules/.vite/deps/chunk-QNBT3DDS.js.map[22m
[2025-06-02T19:14:29.603Z] [ERROR] 2025-06-02T19:14:29.603Z vite:time [32m8.48ms[39m [2m/node_modules/.vite/deps/chunk-U7P2NEEE.js.map[22m
[2025-06-02T19:14:29.606Z] [ERROR] 2025-06-02T19:14:29.606Z vite:time [32m9.26ms[39m [2m/node_modules/.vite/deps/chunk-UE3I4JV6.js.map[22m
[2025-06-02T19:14:29.608Z] [ERROR] 2025-06-02T19:14:29.608Z vite:time [33m11.55ms[39m [2m/node_modules/.vite/deps/tailwind-merge.js.map[22m
[2025-06-02T19:14:29.611Z] [ERROR] 2025-06-02T19:14:29.611Z vite:time [33m13.49ms[39m [2m/node_modules/.vite/deps/axios.js.map[22m
[2025-06-02T19:14:29.616Z] [ERROR] 2025-06-02T19:14:29.616Z vite:time [33m13.99ms[39m [2m/node_modules/.vite/deps/@radix-ui_react-tabs.js.map[22m
[2025-06-02T19:14:29.618Z] [ERROR] 2025-06-02T19:14:29.618Z vite:time [33m12.91ms[39m [2m/node_modules/.vite/deps/chunk-BLD7VZ7P.js.map[22m
[2025-06-02T19:14:29.620Z] [ERROR] 2025-06-02T19:14:29.620Z vite:time [33m14.16ms[39m [2m/node_modules/.vite/deps/react_jsx-dev-runtime.js.map[22m
[2025-06-02T19:14:29.623Z] [ERROR] 2025-06-02T19:14:29.623Z vite:hmr [file change] [2mlogs/vite-debug.log[22m
[2025-06-02T19:14:29.624Z] [ERROR] 2025-06-02T19:14:29.623Z vite:hmr [no modules matched] [2mlogs/vite-debug.log[22m
[2025-06-02T19:14:29.626Z] [ERROR] 2025-06-02T19:14:29.626Z vite:time [33m12.74ms[39m [2m/node_modules/.vite/deps/chunk-6PXSGDAH.js.map[22m
[2025-06-02T19:14:29.627Z] [ERROR] 2025-06-02T19:14:29.627Z vite:time [33m13.77ms[39m [2m/node_modules/.vite/deps/chunk-DRWLMN53.js.map[22m
[2025-06-02T19:14:29.628Z] [ERROR] 2025-06-02T19:14:29.628Z vite:time [33m14.48ms[39m [2m/node_modules/.vite/deps/@radix-ui_react-dialog.js.map[22m
[2025-06-02T19:14:29.636Z] [ERROR] 2025-06-02T19:14:29.636Z vite:time [33m17.38ms[39m [2m/node_modules/.vite/deps/chunk-M7W2FPBU.js.map[22m
[2025-06-02T19:14:29.638Z] [ERROR] 2025-06-02T19:14:29.638Z vite:time [33m14.65ms[39m [2m/node_modules/.vite/deps/@radix-ui_react-toast.js.map[22m
[2025-06-02T19:14:29.644Z] [ERROR] 2025-06-02T19:14:29.644Z vite:time [33m12.20ms[39m [2m/node_modules/.vite/deps/class-variance-authority.js.map[22m
[2025-06-02T19:14:29.645Z] [ERROR] 2025-06-02T19:14:29.645Z vite:time [33m12.90ms[39m [2m/node_modules/.vite/deps/chunk-RI5HKWN3.js.map[22m
[2025-06-02T19:14:29.647Z] [ERROR] 2025-06-02T19:14:29.647Z vite:time [33m15.00ms[39m [2m/node_modules/.vite/deps/chunk-JNXNBBFZ.js.map[22m
[2025-06-02T19:14:29.711Z] [ERROR] 2025-06-02T19:14:29.711Z vite:time [31m85.63ms[39m [2m/node_modules/.vite/deps/lucide-react.js.map[22m
[2025-06-02T19:14:29.712Z] [ERROR] 2025-06-02T19:14:29.712Z vite:time [31m68.27ms[39m [2m/node_modules/.vite/deps/@radix-ui_react-progress.js.map[22m
[2025-06-02T19:14:29.738Z] [ERROR] 2025-06-02T19:14:29.738Z vite:time [31m97.85ms[39m [2m/node_modules/.vite/deps/chunk-PJEEZAML.js.map[22m
[2025-06-02T19:14:29.739Z] [ERROR] 2025-06-02T19:14:29.739Z vite:hmr [file change] [2mlogs/vite-debug.log[22m
[2025-06-02T19:14:29.740Z] [ERROR] 2025-06-02T19:14:29.739Z vite:hmr [no modules matched] [2mlogs/vite-debug.log[22m
[2025-06-02T19:14:37.333Z] [ERROR] 2025-06-02T19:14:37.333Z vite:time [32m4.22ms[39m [2m/node_modules/.vite/deps/@radix-ui_react-select.js.map[22m
[2025-06-02T19:14:37.336Z] [ERROR] 2025-06-02T19:14:37.336Z vite:html-fallback Rewriting GET /installHook.js.map to /index.html
[2025-06-02T19:14:37.338Z] [ERROR] 2025-06-02T19:14:37.338Z vite:hmr [file change] [2mlogs/vite-debug.log[22m
2025-06-02T19:14:37.338Z vite:hmr [no modules matched] [2mlogs/vite-debug.log[22m
[2025-06-02T19:14:37.340Z] [ERROR] 2025-06-02T19:14:37.340Z vite:time [33m10.55ms[39m [2m/node_modules/.vite/deps/chunk-QVMDSMUE.js.map[22m
[2025-06-02T19:14:37.341Z] [ERROR] 2025-06-02T19:14:37.341Z vite:time [33m11.69ms[39m [2m/node_modules/.vite/deps/chunk-TLMPZXCM.js.map[22m
[2025-06-02T19:14:37.342Z] [ERROR] 2025-06-02T19:14:37.342Z vite:time [32m8.54ms[39m [2m/node_modules/.vite/deps/chunk-UPJR475S.js.map[22m
[2025-06-02T19:14:37.343Z] [ERROR] 2025-06-02T19:14:37.343Z vite:time [32m8.68ms[39m [2m/node_modules/.vite/deps/chunk-QNBT3DDS.js.map[22m
[2025-06-02T19:14:37.346Z] [ERROR] 2025-06-02T19:14:37.346Z vite:cache [memory] [2m/src/main.tsx[22m
[2025-06-02T19:14:37.348Z] [ERROR] 2025-06-02T19:14:37.347Z vite:time [33m11.95ms[39m [2m/index.html[22m
[2025-06-02T19:14:37.351Z] [ERROR] 2025-06-02T19:14:37.351Z vite:time [33m13.90ms[39m [2m/node_modules/.vite/deps/chunk-JNXNBBFZ.js.map[22m
[2025-06-02T19:14:54.119Z] [ERROR] ^C[2025-06-02T19:14:54.133Z] [ERROR] ^C