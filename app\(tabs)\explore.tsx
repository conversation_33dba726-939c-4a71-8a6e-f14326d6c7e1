/**
 * Volcano App - Mapa Interactivo
 * Pantalla del mapa con Volcán Villarrica, zonas de seguridad y ubicación del usuario
 */

import { InteractiveMap } from '@/components/InteractiveMap';
import { PrimaryButton, SecondaryButton } from '@/components/ui/AccessibleButton';
import { AccessibleText } from '@/components/ui/AccessibleText';
import {
    InfoIcon,
    MapIcon,
    RouteIcon
} from '@/components/ui/ModernIcon';
import { Colors } from '@/constants/Colors';
import { Spacing } from '@/constants/Layout';
import { useColorScheme } from '@/hooks/useColorScheme';
import React, { useState } from 'react';
import {
    Alert,
    SafeAreaView,
    StyleSheet,
    View,
} from 'react-native';

export default function MapScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const [userLocation, setUserLocation] = useState<{lat: number; lng: number} | null>(null);
  const [distanceToVolcano, setDistanceToVolcano] = useState<string | null>(null);

  const handleLocationUpdate = (location: { lat: number; lng: number }) => {
    setUserLocation(location);

    // Calcular distancia al volcán
    const volcanoLat = -39.420000;
    const volcanoLng = -71.939167;

    const distance = calculateDistance(
      location.lat, location.lng,
      volcanoLat, volcanoLng
    );

    setDistanceToVolcano(distance.toFixed(2));
  };

  // Funcionalidad de creación de zonas removida para usuarios móviles
  // Los usuarios solo pueden ver zonas oficiales creadas desde el backoffice

  // Función para calcular distancia entre dos puntos (fórmula de Haversine)
  const calculateDistance = (lat1: number, lng1: number, lat2: number, lng2: number): number => {
    const R = 6371; // Radio de la Tierra en km
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLng = (lng2 - lng1) * Math.PI / 180;
    const a =
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
      Math.sin(dLng/2) * Math.sin(dLng/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  };

  const showEvacuationInfo = () => {
    Alert.alert(
      'Rutas de Evacuación',
      'En caso de emergencia volcánica:\n\n' +
      '🚗 Ruta Principal: Hacia Temuco por Ruta 199\n' +
      '🚗 Ruta Alternativa: Hacia Villarrica por Ruta 199\n' +
      '🏠 Refugios: Centro de Pucón, Hospital, Escuelas\n\n' +
      'Mantén siempre el tanque de combustible lleno.',
      [{ text: 'Entendido' }]
    );
  };

  const showSafetyTips = () => {
    Alert.alert(
      'Consejos de Seguridad',
      '📍 Tu ubicación actual está siendo monitoreada\n\n' +
      '✅ Mantente alejado del volcán\n' +
      '✅ Ten preparado un kit de emergencia\n' +
      '✅ Conoce las rutas de evacuación\n' +
      '✅ Mantente informado de las alertas oficiales\n\n' +
      (distanceToVolcano ? `Distancia al volcán: ${distanceToVolcano} km` : ''),
      [{ text: 'OK' }]
    );
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },

    header: {
      backgroundColor: colors.background,
      paddingHorizontal: Spacing.md,
      paddingVertical: Spacing.sm,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },

    headerTitle: {
      textAlign: 'center',
    },

    mapContainer: {
      flex: 1,
    },

    bottomPanel: {
      backgroundColor: colors.background,
      padding: Spacing.md,
      borderTopWidth: 1,
      borderTopColor: colors.border,
    },

    infoRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: Spacing.sm,
    },

    buttonRow: {
      flexDirection: 'row',
      gap: Spacing.sm,
    },

    button: {
      flex: 1,
    },

    locationInfo: {
      backgroundColor: colors.backgroundSecondary,
      padding: Spacing.sm,
      borderRadius: 8,
      marginBottom: Spacing.sm,
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'center', gap: 8 }}>
          <MapIcon size={24} strokeWidth={2.5} />
          <AccessibleText variant="h3" style={styles.headerTitle}>
            Mapa de Seguridad Volcánica
          </AccessibleText>
        </View>
      </View>

      {/* Mapa */}
      <View style={styles.mapContainer}>
        <InteractiveMap
          showUserLocation={true}
          showSafetyZones={true}
          onLocationUpdate={handleLocationUpdate}
        />
      </View>

      {/* Panel inferior con información y controles */}
      <View style={styles.bottomPanel}>
        {/* Información de ubicación */}
        {userLocation && (
          <View style={styles.locationInfo}>
            <View style={styles.infoRow}>
              <AccessibleText variant="bodySmall" color="secondary">
                📍 Tu ubicación
              </AccessibleText>
              <AccessibleText variant="bodySmall" color="primary">
                {userLocation.lat.toFixed(4)}, {userLocation.lng.toFixed(4)}
              </AccessibleText>
            </View>
            {distanceToVolcano && (
              <View style={styles.infoRow}>
                <AccessibleText variant="bodySmall" color="secondary">
                  🌋 Distancia al volcán
                </AccessibleText>
                <AccessibleText
                  variant="bodySmall"
                  color={parseFloat(distanceToVolcano) < 10 ? "error" : "primary"}
                >
                  {distanceToVolcano} km
                </AccessibleText>
              </View>
            )}
          </View>
        )}

        {/* Botones de acción */}
        <View style={styles.buttonRow}>
          <PrimaryButton
            style={styles.button}
            onPress={showEvacuationInfo}
            accessibilityLabel="Ver rutas de evacuación"
            icon={<RouteIcon size={20} color="white" strokeWidth={2.5} />}
            iconPosition="left"
          >
            Evacuación
          </PrimaryButton>

          <SecondaryButton
            style={styles.button}
            onPress={showSafetyTips}
            accessibilityLabel="Ver consejos de seguridad"
            icon={<InfoIcon size={20} strokeWidth={2.5} />}
            iconPosition="left"
          >
            Consejos
          </SecondaryButton>
        </View>
      </View>
    </SafeAreaView>
  );
}
