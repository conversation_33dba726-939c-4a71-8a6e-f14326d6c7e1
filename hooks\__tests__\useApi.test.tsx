/**
 * 🌋 Volcano App Mobile - Tests para hooks de API
 * Tests de integración para los hooks de React Query
 */

import { renderHook, waitFor } from '@testing-library/react-native';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import React from 'react';
import {
  useHealthCheck,
  useMobileConfig,
  useCurrentAlert,
  useSafetyZones,
  useLocationCheck,
  useAppVersionCheck
} from '../useApi';

// Mock para apiService
jest.mock('@/services/api', () => ({
  apiService: {
    healthCheck: jest.fn(),
    getMobileConfig: jest.fn(),
    getCurrentAlert: jest.fn(),
    getSafetyZones: jest.fn(),
    reportLocation: jest.fn(),
    checkLocationInZones: jest.fn(),
    checkAppVersion: jest.fn(),
    bulkSync: jest.fn()
  }
}));

// Wrapper para React Query
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        cacheTime: 0
      }
    }
  });

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('API Hooks', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('useHealthCheck', () => {
    it('fetches health status successfully', async () => {
      const { apiService } = require('@/services/api');
      const mockHealthData = {
        success: true,
        status: 'healthy',
        timestamp: new Date().toISOString()
      };

      apiService.healthCheck.mockResolvedValue(mockHealthData);

      const { result } = renderHook(() => useHealthCheck(), {
        wrapper: createWrapper()
      });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.data).toEqual(mockHealthData);
      expect(apiService.healthCheck).toHaveBeenCalled();
    });

    it('handles health check error', async () => {
      const { apiService } = require('@/services/api');
      apiService.healthCheck.mockRejectedValue(new Error('Health check failed'));

      const { result } = renderHook(() => useHealthCheck(), {
        wrapper: createWrapper()
      });

      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      expect(result.current.error).toBeTruthy();
    });

    it('retries health check on failure', async () => {
      const { apiService } = require('@/services/api');
      apiService.healthCheck
        .mockRejectedValueOnce(new Error('Network error'))
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValue({ success: true, status: 'healthy' });

      const queryClient = new QueryClient({
        defaultOptions: {
          queries: {
            retry: 3,
            retryDelay: 0
          }
        }
      });

      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <QueryClientProvider client={queryClient}>
          {children}
        </QueryClientProvider>
      );

      const { result } = renderHook(() => useHealthCheck(), { wrapper });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(apiService.healthCheck).toHaveBeenCalledTimes(3);
    });
  });

  describe('useMobileConfig', () => {
    it('fetches mobile config successfully', async () => {
      const { apiService } = require('@/services/api');
      const mockConfig = {
        mobile_app_version: '1.0.0',
        emergency_contact: '+56912345678',
        update_interval: 60000
      };

      apiService.getMobileConfig.mockResolvedValue(mockConfig);

      const { result } = renderHook(() => useMobileConfig(), {
        wrapper: createWrapper()
      });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.data).toEqual(mockConfig);
    });

    it('caches config data correctly', async () => {
      const { apiService } = require('@/services/api');
      const mockConfig = {
        mobile_app_version: '1.0.0',
        emergency_contact: '+56912345678'
      };

      apiService.getMobileConfig.mockResolvedValue(mockConfig);

      const { result, rerender } = renderHook(() => useMobileConfig(), {
        wrapper: createWrapper()
      });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      // Re-render debería usar cache
      rerender();

      expect(apiService.getMobileConfig).toHaveBeenCalledTimes(1);
    });
  });

  describe('useCurrentAlert', () => {
    it('fetches current alert successfully', async () => {
      const { apiService } = require('@/services/api');
      const mockAlert = {
        id: '1',
        title: 'Alerta Volcánica Amarilla',
        alert_level: 'YELLOW',
        is_active: true
      };

      apiService.getCurrentAlert.mockResolvedValue(mockAlert);

      const { result } = renderHook(() => useCurrentAlert(), {
        wrapper: createWrapper()
      });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.data).toEqual(mockAlert);
    });

    it('handles no current alert', async () => {
      const { apiService } = require('@/services/api');
      apiService.getCurrentAlert.mockResolvedValue(null);

      const { result } = renderHook(() => useCurrentAlert(), {
        wrapper: createWrapper()
      });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.data).toBeNull();
    });

    it('refetches alert data periodically', async () => {
      const { apiService } = require('@/services/api');
      apiService.getCurrentAlert.mockResolvedValue({
        id: '1',
        title: 'Alert',
        alert_level: 'YELLOW'
      });

      const { result } = renderHook(() => useCurrentAlert(), {
        wrapper: createWrapper()
      });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      // Simular refetch automático
      await waitFor(() => {
        expect(apiService.getCurrentAlert).toHaveBeenCalled();
      });
    });
  });

  describe('useSafetyZones', () => {
    it('fetches safety zones successfully', async () => {
      const { apiService } = require('@/services/api');
      const mockZones = [
        {
          id: '1',
          name: 'Zona Segura Centro',
          zone_type: 'SAFE',
          is_active: true
        },
        {
          id: '2',
          name: 'Zona de Peligro Norte',
          zone_type: 'DANGER',
          is_active: true
        }
      ];

      apiService.getSafetyZones.mockResolvedValue(mockZones);

      const { result } = renderHook(() => useSafetyZones(), {
        wrapper: createWrapper()
      });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.data).toEqual(mockZones);
    });

    it('filters active zones only', async () => {
      const { apiService } = require('@/services/api');
      const mockZones = [
        {
          id: '1',
          name: 'Zona Activa',
          zone_type: 'SAFE',
          is_active: true
        },
        {
          id: '2',
          name: 'Zona Inactiva',
          zone_type: 'SAFE',
          is_active: false
        }
      ];

      apiService.getSafetyZones.mockResolvedValue(mockZones);

      const { result } = renderHook(() => useSafetyZones(), {
        wrapper: createWrapper()
      });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      // Verificar que se devuelven todas las zonas (filtrado en backend)
      expect(result.current.data).toHaveLength(2);
    });
  });

  describe('useLocationCheck', () => {
    it('checks location in zones successfully', async () => {
      const { apiService } = require('@/services/api');
      const mockLocationData = {
        latitude: -39.2904,
        longitude: -71.9048
      };

      const mockResponse = {
        in_safe_zone: true,
        in_danger_zone: false,
        nearest_safe_zone: {
          id: '1',
          name: 'Zona Segura Centro',
          distance: 500
        }
      };

      apiService.checkLocationInZones.mockResolvedValue(mockResponse);

      const { result } = renderHook(() => useLocationCheck(), {
        wrapper: createWrapper()
      });

      result.current.mutate(mockLocationData);

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.data).toEqual(mockResponse);
      expect(apiService.checkLocationInZones).toHaveBeenCalledWith(
        mockLocationData.latitude,
        mockLocationData.longitude
      );
    });

    it('handles location check error', async () => {
      const { apiService } = require('@/services/api');
      apiService.checkLocationInZones.mockRejectedValue(new Error('Location check failed'));

      const { result } = renderHook(() => useLocationCheck(), {
        wrapper: createWrapper()
      });

      result.current.mutate({
        latitude: -39.2904,
        longitude: -71.9048
      });

      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      expect(result.current.error).toBeTruthy();
    });
  });

  describe('useAppVersionCheck', () => {
    it('checks app version successfully', async () => {
      const { apiService } = require('@/services/api');
      const mockVersionResponse = {
        is_supported: true,
        latest_version: '1.1.0',
        update_required: false,
        update_url: 'https://app.store/volcano-app'
      };

      apiService.checkAppVersion.mockResolvedValue(mockVersionResponse);

      const { result } = renderHook(() => useAppVersionCheck('1.0.0'), {
        wrapper: createWrapper()
      });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.data).toEqual(mockVersionResponse);
      expect(apiService.checkAppVersion).toHaveBeenCalledWith('1.0.0');
    });

    it('handles outdated version', async () => {
      const { apiService } = require('@/services/api');
      const mockVersionResponse = {
        is_supported: false,
        latest_version: '2.0.0',
        update_required: true,
        update_url: 'https://app.store/volcano-app'
      };

      apiService.checkAppVersion.mockResolvedValue(mockVersionResponse);

      const { result } = renderHook(() => useAppVersionCheck('1.0.0'), {
        wrapper: createWrapper()
      });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.data?.update_required).toBe(true);
    });

    it('skips version check when no version provided', () => {
      const { result } = renderHook(() => useAppVersionCheck(''), {
        wrapper: createWrapper()
      });

      expect(result.current.data).toBeUndefined();
      expect(result.current.isLoading).toBe(false);
    });
  });

  describe('Query Invalidation', () => {
    it('invalidates queries correctly', async () => {
      const queryClient = new QueryClient();
      const invalidateSpy = jest.spyOn(queryClient, 'invalidateQueries');

      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <QueryClientProvider client={queryClient}>
          {children}
        </QueryClientProvider>
      );

      const { result } = renderHook(() => useCurrentAlert(), { wrapper });

      // Simular invalidación
      queryClient.invalidateQueries(['mobile', 'alerts', 'current']);

      expect(invalidateSpy).toHaveBeenCalled();
    });
  });

  describe('Error Recovery', () => {
    it('recovers from network errors', async () => {
      const { apiService } = require('@/services/api');
      
      // Primera llamada falla
      apiService.healthCheck.mockRejectedValueOnce(new Error('Network error'));
      
      const { result } = renderHook(() => useHealthCheck(), {
        wrapper: createWrapper()
      });

      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      // Segunda llamada exitosa
      apiService.healthCheck.mockResolvedValue({ success: true, status: 'healthy' });
      
      result.current.refetch();

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });
    });
  });
});
