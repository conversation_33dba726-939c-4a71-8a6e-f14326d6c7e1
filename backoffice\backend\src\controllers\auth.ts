/**
 * 🌋 Volcano App Backend - Controlador de Autenticación
 * Manejo de login, logout y refresh de tokens
 */

import { Request, Response } from 'express';
import bcrypt from 'bcryptjs';
import { supabaseAdmin } from '@/services/supabase';
import { logAuditAction } from '@/services/supabase';
import { generateTokens, verifyToken, updateLastLogin } from '@/middleware/auth';
import { logger, logAuth, logSecurity } from '@/utils/logger';
import { CONFIG } from '@/config/env';
import { AuthenticatedRequest, LoginDto, JwtPayload } from '@/types';

// =====================================================
// CONTROLADOR DE LOGIN
// =====================================================

/**
 * Iniciar sesión
 * POST /api/auth/login
 */
export async function login(req: Request, res: Response) {
  try {
    const { email, password }: LoginDto = req.body;

    // Validación básica
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        error: 'Email and password are required',
        timestamp: new Date()
      });
    }

    // Buscar usuario por email
    const { data: user, error } = await supabaseAdmin
      .from('admin_users')
      .select('id, email, password_hash, full_name, role, is_active')
      .eq('email', email.toLowerCase())
      .single();

    if (error || !user) {
      logSecurity('Login attempt with invalid email', {
        email,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });
      
      return res.status(401).json({
        success: false,
        error: 'Invalid credentials',
        timestamp: new Date()
      });
    }

    // Verificar si el usuario está activo
    if (!user.is_active) {
      logSecurity('Login attempt with inactive user', {
        userId: user.id,
        email,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      }, 'medium');
      
      return res.status(401).json({
        success: false,
        error: 'Account is disabled',
        timestamp: new Date()
      });
    }

    // Verificar contraseña
    const isPasswordValid = await bcrypt.compare(password, user.password_hash);
    if (!isPasswordValid) {
      logSecurity('Login attempt with invalid password', {
        userId: user.id,
        email,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });
      
      return res.status(401).json({
        success: false,
        error: 'Invalid credentials',
        timestamp: new Date()
      });
    }

    // Generar tokens
    const tokens = generateTokens({
      user_id: user.id,
      email: user.email,
      role: user.role
    });

    // Actualizar último login
    await updateLastLogin(user.id);

    // Log de auditoría
    await logAuditAction(
      user.id,
      'LOGIN',
      'admin_users',
      user.id,
      null,
      { login_time: new Date().toISOString() },
      req.ip,
      req.get('User-Agent')
    );

    logAuth('login', user.id, req.ip, req.get('User-Agent'));

    res.json({
      success: true,
      data: {
        user: {
          id: user.id,
          email: user.email,
          full_name: user.full_name,
          role: user.role
        },
        tokens
      },
      timestamp: new Date()
    });

  } catch (error) {
    logger.error('Login error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      timestamp: new Date()
    });
  }
}

// =====================================================
// CONTROLADOR DE LOGOUT
// =====================================================

/**
 * Cerrar sesión
 * POST /api/auth/logout
 */
export async function logout(req: AuthenticatedRequest, res: Response) {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'Not authenticated',
        timestamp: new Date()
      });
    }

    // Log de auditoría
    await logAuditAction(
      req.user.id,
      'LOGOUT',
      'admin_users',
      req.user.id,
      null,
      { logout_time: new Date().toISOString() },
      req.ip,
      req.get('User-Agent')
    );

    logAuth('logout', req.user.id, req.ip, req.get('User-Agent'));

    res.json({
      success: true,
      message: 'Logged out successfully',
      timestamp: new Date()
    });

  } catch (error) {
    logger.error('Logout error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      timestamp: new Date()
    });
  }
}

// =====================================================
// CONTROLADOR DE REFRESH TOKEN
// =====================================================

/**
 * Renovar token de acceso
 * POST /api/auth/refresh
 */
export async function refreshToken(req: Request, res: Response) {
  try {
    const { refresh_token } = req.body;

    if (!refresh_token) {
      return res.status(400).json({
        success: false,
        error: 'Refresh token is required',
        timestamp: new Date()
      });
    }

    // Verificar refresh token
    let decoded: JwtPayload;
    try {
      decoded = verifyToken(refresh_token, true);
    } catch (error) {
      logSecurity('Invalid refresh token used', {
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });
      
      return res.status(401).json({
        success: false,
        error: 'Invalid refresh token',
        timestamp: new Date()
      });
    }

    // Verificar que el usuario sigue activo
    const { data: user, error } = await supabaseAdmin
      .from('admin_users')
      .select('id, email, full_name, role, is_active')
      .eq('id', decoded.user_id)
      .eq('is_active', true)
      .single();

    if (error || !user) {
      logSecurity('Refresh token used for inactive user', {
        userId: decoded.user_id,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });
      
      return res.status(401).json({
        success: false,
        error: 'User not found or inactive',
        timestamp: new Date()
      });
    }

    // Generar nuevos tokens
    const tokens = generateTokens({
      user_id: user.id,
      email: user.email,
      role: user.role
    });

    logAuth('token_refresh', user.id, req.ip, req.get('User-Agent'));

    res.json({
      success: true,
      data: {
        user: {
          id: user.id,
          email: user.email,
          full_name: user.full_name,
          role: user.role
        },
        tokens
      },
      timestamp: new Date()
    });

  } catch (error) {
    logger.error('Refresh token error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      timestamp: new Date()
    });
  }
}

// =====================================================
// CONTROLADOR DE PERFIL
// =====================================================

/**
 * Obtener perfil del usuario autenticado
 * GET /api/auth/me
 */
export async function getProfile(req: AuthenticatedRequest, res: Response) {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'Not authenticated',
        timestamp: new Date()
      });
    }

    // Obtener información completa del usuario
    const { data: user, error } = await supabaseAdmin
      .from('admin_users')
      .select('id, email, full_name, role, is_active, last_login, created_at')
      .eq('id', req.user.id)
      .single();

    if (error || !user) {
      return res.status(404).json({
        success: false,
        error: 'User not found',
        timestamp: new Date()
      });
    }

    res.json({
      success: true,
      data: {
        id: user.id,
        email: user.email,
        full_name: user.full_name,
        role: user.role,
        is_active: user.is_active,
        last_login: user.last_login,
        created_at: user.created_at
      },
      timestamp: new Date()
    });

  } catch (error) {
    logger.error('Get profile error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      timestamp: new Date()
    });
  }
}

// =====================================================
// CONTROLADOR DE CAMBIO DE CONTRASEÑA
// =====================================================

/**
 * Cambiar contraseña del usuario autenticado
 * PUT /api/auth/change-password
 */
export async function changePassword(req: AuthenticatedRequest, res: Response) {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'Not authenticated',
        timestamp: new Date()
      });
    }

    const { current_password, new_password } = req.body;

    // Validación básica
    if (!current_password || !new_password) {
      return res.status(400).json({
        success: false,
        error: 'Current password and new password are required',
        timestamp: new Date()
      });
    }

    if (new_password.length < 8) {
      return res.status(400).json({
        success: false,
        error: 'New password must be at least 8 characters long',
        timestamp: new Date()
      });
    }

    // Obtener contraseña actual
    const { data: user, error } = await supabaseAdmin
      .from('admin_users')
      .select('password_hash')
      .eq('id', req.user.id)
      .single();

    if (error || !user) {
      return res.status(404).json({
        success: false,
        error: 'User not found',
        timestamp: new Date()
      });
    }

    // Verificar contraseña actual
    const isCurrentPasswordValid = await bcrypt.compare(current_password, user.password_hash);
    if (!isCurrentPasswordValid) {
      logSecurity('Invalid current password in change password attempt', {
        userId: req.user.id,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });
      
      return res.status(400).json({
        success: false,
        error: 'Current password is incorrect',
        timestamp: new Date()
      });
    }

    // Hash de la nueva contraseña
    const saltRounds = parseInt(process.env.BCRYPT_ROUNDS || '12');
    const newPasswordHash = await bcrypt.hash(new_password, saltRounds);

    // Actualizar contraseña
    const { error: updateError } = await supabaseAdmin
      .from('admin_users')
      .update({ password_hash: newPasswordHash })
      .eq('id', req.user.id);

    if (updateError) {
      logger.error('Error updating password:', updateError);
      return res.status(500).json({
        success: false,
        error: 'Failed to update password',
        timestamp: new Date()
      });
    }

    // Log de auditoría
    await logAuditAction(
      req.user.id,
      'UPDATE',
      'admin_users',
      req.user.id,
      null,
      { action: 'password_changed' },
      req.ip,
      req.get('User-Agent')
    );

    logSecurity('Password changed successfully', {
      userId: req.user.id,
      ip: req.ip,
      userAgent: req.get('User-Agent')
    }, 'low');

    res.json({
      success: true,
      message: 'Password changed successfully',
      timestamp: new Date()
    });

  } catch (error) {
    logger.error('Change password error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      timestamp: new Date()
    });
  }
}

// =====================================================
// EXPORTACIONES
// =====================================================

export default {
  login,
  logout,
  refreshToken,
  getProfile,
  changePassword
};
