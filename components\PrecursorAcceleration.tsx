/**
 * 🌋 Volcano App - Componente de Análisis de Aceleración de Precursores
 * Visualización avanzada de análisis matemático de precursores volcánicos
 */

import { Colors } from '@/constants/Colors';
import { BorderRadius, Spacing } from '@/constants/Layout';
import { useColorScheme } from '@/hooks/useColorScheme';
import {
    COLORES_ALERTA,
    DATOS_PRUEBA,
    EstadoPrecursorAnalysis,
    PrecursorAccelerationProps
} from '@/types/precursor';
import {
    analisisCompleto,
    formatearValor,
    obtenerEstadisticas
} from '@/utils/precursorAnalysis';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import {
    Dimensions,
    ScrollView,
    StyleSheet,
    View
} from 'react-native';
import {
    Bar,
    BarChart,
    CartesianGrid,
    Line,
    LineChart,
    ResponsiveContainer,
    Tooltip,
    XAxis,
    YAxis
} from 'recharts';
import { PrimaryButton, SecondaryButton } from './ui/AccessibleButton';
import { AccessibleText } from './ui/AccessibleText';

const { width: screenWidth } = Dimensions.get('window');

/**
 * Componente principal para análisis de aceleración de precursores
 */
export function PrecursorAcceleration({
  datos = DATOS_PRUEBA,
  umbrales,
  onAlerta,
  titulo = 'Índice de Aceleración de Precursores',
  altura = 300,
  mostrarControles = true,
}: PrecursorAccelerationProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];

  // Estado del componente
  const [estado, setEstado] = useState<EstadoPrecursorAnalysis>({
    datos: null,
    analisis: null,
    alerta: null,
    cargando: false,
    error: null,
    configuracion: {
      mostrarTendencia: true,
      mostrarPuntos: true,
      suavizar: false,
      colores: {
        datosOriginales: colors.primary,
        primeraderivada: '#3b82f6', // blue-500
        segundaDerivada: '#8b5cf6', // violet-500
      },
    },
  });

  // Procesar datos cuando cambien
  const procesarDatos = useCallback(async () => {
    if (!datos?.valores || datos.valores.length === 0) {
      setEstado(prev => ({
        ...prev,
        error: 'No hay datos para analizar',
        analisis: null,
        alerta: null,
      }));
      return;
    }

    setEstado(prev => ({ ...prev, cargando: true, error: null }));

    try {
      const { analisis, alerta } = analisisCompleto(datos.valores, umbrales);
      
      setEstado(prev => ({
        ...prev,
        datos,
        analisis,
        alerta,
        cargando: false,
      }));

      // Notificar alerta si hay callback
      if (onAlerta && alerta.nivel !== 'Verde') {
        onAlerta(alerta);
      }
    } catch (error) {
      setEstado(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Error desconocido',
        cargando: false,
      }));
    }
  }, [datos, umbrales, onAlerta]);

  // Efecto para procesar datos cuando cambien
  useEffect(() => {
    procesarDatos();
  }, [procesarDatos]);

  // Preparar datos para gráficos
  const datosGraficos = useMemo(() => {
    if (!estado.analisis) return [];

    const { datosOriginales, primeraderivada, segundaDerivada } = estado.analisis;
    
    return datosOriginales.map((valor, index) => ({
      index: index + 1,
      original: valor,
      derivada1: primeraderivada[index],
      derivada2: segundaDerivada[index],
    }));
  }, [estado.analisis]);

  // Estadísticas para mostrar
  const estadisticas = useMemo(() => {
    if (!estado.analisis) return null;

    return {
      originales: obtenerEstadisticas(estado.analisis.datosOriginales),
      derivada1: obtenerEstadisticas(estado.analisis.primeraderivada),
      derivada2: obtenerEstadisticas(estado.analisis.segundaDerivada),
    };
  }, [estado.analisis]);

  // Función para usar datos de prueba
  const usarDatosPrueba = useCallback(() => {
    setEstado(prev => ({ ...prev, datos: DATOS_PRUEBA }));
  }, []);

  // Renderizar indicador de alerta
  const renderIndicadorAlerta = () => {
    if (!estado.alerta) return null;

    const colorAlerta = COLORES_ALERTA[estado.alerta.nivel];
    
    return (
      <View style={[styles.alertaBadge, { backgroundColor: colorAlerta }]}>
        <AccessibleText 
          variant="button" 
          style={[styles.alertaTexto, { color: 'white' }]}
        >
          {estado.alerta.nivel}: {estado.alerta.mensaje}
        </AccessibleText>
        {estado.alerta.valor !== null && (
          <AccessibleText 
            variant="caption" 
            style={[styles.alertaValor, { color: 'white' }]}
          >
            Valor: {formatearValor(estado.alerta.valor)}
          </AccessibleText>
        )}
      </View>
    );
  };

  // Renderizar gráfico de líneas
  const renderGraficoLineas = (
    data: any[], 
    dataKey: string, 
    nombre: string, 
    color: string
  ) => (
    <View style={styles.graficoContainer}>
      <AccessibleText variant="h4" style={styles.graficoTitulo}>
        {nombre}
      </AccessibleText>
      <ResponsiveContainer width="100%" height={altura}>
        <LineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" stroke={colors.border} />
          <XAxis 
            dataKey="index" 
            stroke={colors.text}
            fontSize={12}
          />
          <YAxis 
            stroke={colors.text}
            fontSize={12}
          />
          <Tooltip 
            contentStyle={{
              backgroundColor: colors.background,
              border: `1px solid ${colors.border}`,
              borderRadius: BorderRadius.md,
            }}
          />
          <Line
            type={estado.configuracion.suavizar ? "monotone" : "linear"}
            dataKey={dataKey}
            stroke={color}
            strokeWidth={2}
            dot={estado.configuracion.mostrarPuntos}
            connectNulls={false}
          />
        </LineChart>
      </ResponsiveContainer>
    </View>
  );

  // Renderizar gráfico de barras para segunda derivada
  const renderGraficoBarras = () => (
    <View style={styles.graficoContainer}>
      <AccessibleText variant="h4" style={styles.graficoTitulo}>
        Segunda Derivada (Aceleración)
      </AccessibleText>
      <ResponsiveContainer width="100%" height={altura}>
        <BarChart data={datosGraficos} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" stroke={colors.border} />
          <XAxis 
            dataKey="index" 
            stroke={colors.text}
            fontSize={12}
          />
          <YAxis 
            stroke={colors.text}
            fontSize={12}
          />
          <Tooltip 
            contentStyle={{
              backgroundColor: colors.background,
              border: `1px solid ${colors.border}`,
              borderRadius: BorderRadius.md,
            }}
          />
          <Bar 
            dataKey="derivada2" 
            fill={estado.configuracion.colores?.segundaDerivada}
          />
        </BarChart>
      </ResponsiveContainer>
    </View>
  );

  // Renderizar estadísticas
  const renderEstadisticas = () => {
    if (!estadisticas) return null;

    return (
      <View style={styles.estadisticasContainer}>
        <AccessibleText variant="h4" style={styles.estadisticasTitulo}>
          Estadísticas
        </AccessibleText>
        <View style={styles.estadisticasGrid}>
          <View style={styles.estadisticaItem}>
            <AccessibleText variant="caption">Datos Originales</AccessibleText>
            <AccessibleText variant="body">
              Min: {formatearValor(estadisticas.originales.min)} | 
              Max: {formatearValor(estadisticas.originales.max)} | 
              Prom: {formatearValor(estadisticas.originales.promedio)}
            </AccessibleText>
          </View>
          <View style={styles.estadisticaItem}>
            <AccessibleText variant="caption">Primera Derivada</AccessibleText>
            <AccessibleText variant="body">
              Min: {formatearValor(estadisticas.derivada1.min)} | 
              Max: {formatearValor(estadisticas.derivada1.max)} | 
              Prom: {formatearValor(estadisticas.derivada1.promedio)}
            </AccessibleText>
          </View>
          <View style={styles.estadisticaItem}>
            <AccessibleText variant="caption">Segunda Derivada</AccessibleText>
            <AccessibleText variant="body">
              Min: {formatearValor(estadisticas.derivada2.min)} | 
              Max: {formatearValor(estadisticas.derivada2.max)} | 
              Prom: {formatearValor(estadisticas.derivada2.promedio)}
            </AccessibleText>
          </View>
        </View>
      </View>
    );
  };

  if (estado.cargando) {
    return (
      <View style={[styles.container, styles.centrado]}>
        <AccessibleText variant="body">Analizando datos...</AccessibleText>
      </View>
    );
  }

  if (estado.error) {
    return (
      <View style={[styles.container, styles.centrado]}>
        <AccessibleText variant="body" color="error">
          Error: {estado.error}
        </AccessibleText>
        {mostrarControles && (
          <PrimaryButton onPress={usarDatosPrueba} style={styles.botonPrueba}>
            Usar Datos de Prueba
          </PrimaryButton>
        )}
      </View>
    );
  }

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Header */}
      <View style={styles.header}>
        <AccessibleText variant="h2" style={styles.titulo}>
          {titulo}
        </AccessibleText>
        {estado.datos && (
          <AccessibleText variant="caption" color="muted">
            {estado.datos.tipo} - {estado.datos.volcan}
          </AccessibleText>
        )}
      </View>

      {/* Indicador de Alerta */}
      {renderIndicadorAlerta()}

      {/* Controles */}
      {mostrarControles && (
        <View style={styles.controles}>
          <SecondaryButton onPress={usarDatosPrueba} style={styles.botonControl}>
            Datos de Prueba
          </SecondaryButton>
          <SecondaryButton onPress={procesarDatos} style={styles.botonControl}>
            Reanalizar
          </SecondaryButton>
        </View>
      )}

      {/* Gráficos */}
      {datosGraficos.length > 0 && (
        <>
          {renderGraficoLineas(
            datosGraficos, 
            'original', 
            'Datos Originales',
            estado.configuracion.colores?.datosOriginales || colors.primary
          )}
          
          {renderGraficoLineas(
            datosGraficos, 
            'derivada1', 
            'Primera Derivada (Tasa de Cambio)',
            estado.configuracion.colores?.primeraderivada || '#3b82f6'
          )}
          
          {renderGraficoBarras()}
        </>
      )}

      {/* Estadísticas */}
      {renderEstadisticas()}
    </ScrollView>
  );
}

// Estilos del componente
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'transparent',
    padding: Spacing.md,
  },

  centrado: {
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: 200,
  },

  header: {
    marginBottom: Spacing.lg,
    alignItems: 'center',
  },

  titulo: {
    textAlign: 'center',
    marginBottom: Spacing.xs,
  },

  alertaBadge: {
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.lg,
    marginBottom: Spacing.md,
    alignItems: 'center',
  },

  alertaTexto: {
    fontWeight: 'bold',
    textAlign: 'center',
  },

  alertaValor: {
    marginTop: Spacing.xs,
    textAlign: 'center',
  },

  controles: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: Spacing.lg,
    gap: Spacing.sm,
  },

  botonControl: {
    flex: 1,
    marginHorizontal: Spacing.xs,
  },

  botonPrueba: {
    marginTop: Spacing.md,
  },

  graficoContainer: {
    marginBottom: Spacing.xl,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: BorderRadius.lg,
    padding: Spacing.md,
  },

  graficoTitulo: {
    textAlign: 'center',
    marginBottom: Spacing.md,
    fontWeight: '600',
  },

  estadisticasContainer: {
    marginTop: Spacing.lg,
    padding: Spacing.md,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: BorderRadius.lg,
  },

  estadisticasTitulo: {
    textAlign: 'center',
    marginBottom: Spacing.md,
    fontWeight: '600',
  },

  estadisticasGrid: {
    gap: Spacing.sm,
  },

  estadisticaItem: {
    padding: Spacing.sm,
    backgroundColor: 'rgba(255, 255, 255, 0.03)',
    borderRadius: BorderRadius.md,
  },
});

export default PrecursorAcceleration;
