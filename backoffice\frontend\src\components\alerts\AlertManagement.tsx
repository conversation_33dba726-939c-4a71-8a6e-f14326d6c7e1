/**
 * 🌋 Volcano App Backoffice - Gestión de Alertas y Notificaciones
 * Componente para gestionar alertas volcánicas y notificaciones en tiempo real
 */

import React, { useState, useEffect } from 'react';
import { Bell, Send, TestTube, History, Settings, AlertTriangle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';

// =====================================================
// TIPOS Y INTERFACES
// =====================================================

interface NotificationData {
  type: 'volcano_alert' | 'zone_update' | 'system_status' | 'manual_notification' | 'test_notification';
  title: string;
  message: string;
  priority: 'low' | 'normal' | 'high';
  target_type: 'all' | 'zone' | 'location' | 'device' | 'user';
  sound: boolean;
  vibration: boolean;
  expires_in_minutes?: number;
}

interface NotificationHistory {
  id: string;
  type: string;
  title: string;
  message: string;
  target_type: string;
  sent_count: number;
  failed_count: number;
  status: string;
  created_at: string;
}

// =====================================================
// COMPONENTE PRINCIPAL
// =====================================================

export function AlertManagement() {
  const { toast } = useToast();
  
  // Estados
  const [loading, setLoading] = useState(false);
  const [history, setHistory] = useState<NotificationHistory[]>([]);
  const [stats, setStats] = useState({
    total_sent: 0,
    total_failed: 0,
    success_rate: 0,
    last_24h: 0
  });

  // Formulario de notificación manual
  const [notification, setNotification] = useState<NotificationData>({
    type: 'manual_notification',
    title: '',
    message: '',
    priority: 'normal',
    target_type: 'all',
    sound: true,
    vibration: true,
    expires_in_minutes: 60
  });

  // =====================================================
  // FUNCIONES DE API
  // =====================================================

  const fetchNotificationHistory = async () => {
    try {
      const response = await fetch('/api/notifications/history?limit=20');
      const data = await response.json();
      
      if (data.success) {
        setHistory(data.data.notifications || []);
      }
    } catch (error) {
      console.error('Error fetching notification history:', error);
    }
  };

  const fetchNotificationStats = async () => {
    try {
      const response = await fetch('/api/notifications/stats?period=24h');
      const data = await response.json();
      
      if (data.success) {
        setStats(data.data.stats);
      }
    } catch (error) {
      console.error('Error fetching notification stats:', error);
    }
  };

  const sendManualNotification = async () => {
    try {
      setLoading(true);
      
      const response = await fetch('/api/notifications/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: notification.title,
          body: notification.message,
          target_type: notification.target_type,
          sound: notification.sound,
          vibration: notification.vibration,
          priority: notification.priority,
          data: {
            type: notification.type,
            expires_in_minutes: notification.expires_in_minutes
          }
        }),
      });

      const data = await response.json();

      if (data.success) {
        toast({
          title: 'Éxito',
          description: `Notificación enviada a ${data.data.sent_count} dispositivos`,
        });
        
        // Limpiar formulario
        setNotification({
          type: 'manual_notification',
          title: '',
          message: '',
          priority: 'normal',
          target_type: 'all',
          sound: true,
          vibration: true,
          expires_in_minutes: 60
        });
        
        // Actualizar historial
        fetchNotificationHistory();
        fetchNotificationStats();
      } else {
        throw new Error(data.error || 'Failed to send notification');
      }
    } catch (error) {
      console.error('Error sending notification:', error);
      toast({
        title: 'Error',
        description: 'No se pudo enviar la notificación',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const sendTestNotification = async () => {
    try {
      setLoading(true);
      
      const response = await fetch('/api/notifications/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          device_id: 'test_device_123' // En producción, esto vendría de un selector
        }),
      });

      const data = await response.json();

      if (data.success) {
        toast({
          title: 'Éxito',
          description: 'Notificación de prueba enviada',
        });
      } else {
        throw new Error(data.error || 'Failed to send test notification');
      }
    } catch (error) {
      console.error('Error sending test notification:', error);
      toast({
        title: 'Error',
        description: 'No se pudo enviar la notificación de prueba',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // =====================================================
  // EFECTOS
  // =====================================================

  useEffect(() => {
    fetchNotificationHistory();
    fetchNotificationStats();
  }, []);

  // =====================================================
  // FUNCIONES AUXILIARES
  // =====================================================

  const getNotificationTypeColor = (type: string) => {
    switch (type) {
      case 'volcano_alert': return 'bg-red-100 text-red-800';
      case 'zone_update': return 'bg-blue-100 text-blue-800';
      case 'system_status': return 'bg-gray-100 text-gray-800';
      case 'manual_notification': return 'bg-purple-100 text-purple-800';
      case 'test_notification': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // =====================================================
  // RENDER
  // =====================================================

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Gestión de Alertas</h1>
          <p className="text-muted-foreground">
            Envía notificaciones y gestiona alertas volcánicas en tiempo real
          </p>
        </div>
        <Button onClick={sendTestNotification} variant="outline" disabled={loading}>
          <TestTube className="h-4 w-4 mr-2" />
          Prueba
        </Button>
      </div>

      {/* Estadísticas */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Enviadas (24h)</CardTitle>
            <Send className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total_sent}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Fallidas (24h)</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stats.total_failed}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tasa de Éxito</CardTitle>
            <Bell className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {stats.success_rate.toFixed(1)}%
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Historial</CardTitle>
            <History className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{history.length}</div>
          </CardContent>
        </Card>
      </div>

      {/* Contenido Principal */}
      <Tabs defaultValue="send" className="space-y-4">
        <TabsList>
          <TabsTrigger value="send">Enviar Notificación</TabsTrigger>
          <TabsTrigger value="history">Historial</TabsTrigger>
          <TabsTrigger value="settings">Configuración</TabsTrigger>
        </TabsList>

        {/* Tab: Enviar Notificación */}
        <TabsContent value="send" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Send className="h-4 w-4" />
                Enviar Notificación Manual
              </CardTitle>
              <CardDescription>
                Envía una notificación personalizada a los usuarios de la aplicación
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="title">Título</Label>
                  <Input
                    id="title"
                    placeholder="Título de la notificación"
                    value={notification.title}
                    onChange={(e) => setNotification(prev => ({ ...prev, title: e.target.value }))}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="priority">Prioridad</Label>
                  <Select
                    value={notification.priority}
                    onValueChange={(value: 'low' | 'normal' | 'high') => 
                      setNotification(prev => ({ ...prev, priority: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">Baja</SelectItem>
                      <SelectItem value="normal">Normal</SelectItem>
                      <SelectItem value="high">Alta</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="message">Mensaje</Label>
                <Textarea
                  id="message"
                  placeholder="Contenido de la notificación"
                  value={notification.message}
                  onChange={(e) => setNotification(prev => ({ ...prev, message: e.target.value }))}
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="target">Destinatarios</Label>
                  <Select
                    value={notification.target_type}
                    onValueChange={(value: 'all' | 'zone' | 'location' | 'device' | 'user') => 
                      setNotification(prev => ({ ...prev, target_type: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Todos los usuarios</SelectItem>
                      <SelectItem value="zone">Por zona</SelectItem>
                      <SelectItem value="location">Por ubicación</SelectItem>
                      <SelectItem value="device">Dispositivos específicos</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="sound"
                    checked={notification.sound}
                    onCheckedChange={(checked) => 
                      setNotification(prev => ({ ...prev, sound: checked }))
                    }
                  />
                  <Label htmlFor="sound">Sonido</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="vibration"
                    checked={notification.vibration}
                    onCheckedChange={(checked) => 
                      setNotification(prev => ({ ...prev, vibration: checked }))
                    }
                  />
                  <Label htmlFor="vibration">Vibración</Label>
                </div>
              </div>

              <div className="flex justify-end space-x-2">
                <Button
                  variant="outline"
                  onClick={() => setNotification({
                    type: 'manual_notification',
                    title: '',
                    message: '',
                    priority: 'normal',
                    target_type: 'all',
                    sound: true,
                    vibration: true,
                    expires_in_minutes: 60
                  })}
                >
                  Limpiar
                </Button>
                <Button
                  onClick={sendManualNotification}
                  disabled={loading || !notification.title || !notification.message}
                >
                  {loading ? 'Enviando...' : 'Enviar Notificación'}
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Tab: Historial */}
        <TabsContent value="history" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <History className="h-4 w-4" />
                Historial de Notificaciones
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {history.map((item) => (
                  <div key={item.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <Badge className={getNotificationTypeColor(item.type)}>
                          {item.type}
                        </Badge>
                        <span className="font-medium">{item.title}</span>
                      </div>
                      <p className="text-sm text-muted-foreground">{item.message}</p>
                      <p className="text-xs text-muted-foreground">
                        {formatDate(item.created_at)} • {item.sent_count} enviadas • {item.failed_count} fallidas
                      </p>
                    </div>
                    <Badge variant={item.status === 'sent' ? 'default' : 'secondary'}>
                      {item.status}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Tab: Configuración */}
        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-4 w-4" />
                Configuración de Notificaciones
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                Configuración de notificaciones próximamente...
              </p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default AlertManagement;
