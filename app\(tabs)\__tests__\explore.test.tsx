/**
 * 🌋 Volcano App Mobile - Tests para pantalla Explore
 * Tests unitarios para la pantalla del mapa de seguridad volcánica
 */

import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { Alert } from 'react-native';
import ExploreScreen from '../explore';

// Mock para InteractiveMap
jest.mock('@/components/InteractiveMap', () => {
  const { View, Text } = require('react-native');
  return {
    InteractiveMap: (props: any) => (
      <View testID="interactive-map">
        <Text>Mapa Interactivo</Text>
      </View>
    )
  };
});

// Mock para useColorScheme
jest.mock('@/hooks/useColorScheme', () => ({
  useColorScheme: jest.fn(() => 'light')
}));

// Mock para Alert
jest.spyOn(Alert, 'alert');

describe('ExploreScreen', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders explore screen correctly', () => {
    const { getByText, getByTestId } = render(<ExploreScreen />);
    
    expect(getByText('Mapa de Seguridad Volcánica')).toBeTruthy();
    expect(getByTestId('interactive-map')).toBeTruthy();
  });

  it('displays header with map icon and title', () => {
    const { getByText } = render(<ExploreScreen />);
    
    expect(getByText('Mapa de Seguridad Volcánica')).toBeTruthy();
  });

  it('renders interactive map component', () => {
    const { getByTestId } = render(<ExploreScreen />);
    
    const map = getByTestId('interactive-map');
    expect(map).toBeTruthy();
  });

  it('shows action buttons', () => {
    const { getByText } = render(<ExploreScreen />);
    
    expect(getByText('Mi Ubicación')).toBeTruthy();
    expect(getByText('Rutas de Evacuación')).toBeTruthy();
    expect(getByText('Información')).toBeTruthy();
  });

  it('handles location button press', () => {
    const { getByText } = render(<ExploreScreen />);
    
    const locationButton = getByText('Mi Ubicación');
    fireEvent.press(locationButton);
    
    expect(Alert.alert).toHaveBeenCalledWith(
      'Ubicación',
      'Función de ubicación en desarrollo'
    );
  });

  it('handles evacuation routes button press', () => {
    const { getByText } = render(<ExploreScreen />);
    
    const routesButton = getByText('Rutas de Evacuación');
    fireEvent.press(routesButton);
    
    expect(Alert.alert).toHaveBeenCalledWith(
      'Rutas de Evacuación',
      'Función de rutas en desarrollo'
    );
  });

  it('handles info button press', () => {
    const { getByText } = render(<ExploreScreen />);
    
    const infoButton = getByText('Información');
    fireEvent.press(infoButton);
    
    expect(Alert.alert).toHaveBeenCalledWith(
      'Información',
      'Función de información en desarrollo'
    );
  });

  it('handles location update from map', async () => {
    const { getByTestId } = render(<ExploreScreen />);
    
    const map = getByTestId('interactive-map');
    
    // Simular actualización de ubicación
    const mockLocation = {
      lat: -39.2904,
      lng: -71.9048
    };
    
    // El componente debería manejar la actualización sin errores
    expect(map).toBeTruthy();
  });

  it('displays location info when available', async () => {
    const { getByText } = render(<ExploreScreen />);
    
    // Inicialmente no debería mostrar información de ubicación
    expect(() => getByText('Lat:')).toThrow();
  });

  it('applies correct styling based on color scheme', () => {
    const { useColorScheme } = require('@/hooks/useColorScheme');
    
    // Test con tema oscuro
    useColorScheme.mockReturnValue('dark');
    
    const { getByTestId } = render(<ExploreScreen />);
    
    const container = getByTestId('explore-container');
    expect(container.props.style).toMatchObject({
      backgroundColor: expect.any(String)
    });
  });

  it('handles map component props correctly', () => {
    const { getByTestId } = render(<ExploreScreen />);
    
    const map = getByTestId('interactive-map');
    expect(map).toBeTruthy();
    
    // Verificar que se pasan las props correctas al mapa
    // (esto dependería de la implementación específica del mock)
  });

  it('shows proper accessibility labels', () => {
    const { getByLabelText } = render(<ExploreScreen />);
    
    expect(getByLabelText('Obtener mi ubicación actual')).toBeTruthy();
    expect(getByLabelText('Ver rutas de evacuación')).toBeTruthy();
    expect(getByLabelText('Mostrar información adicional')).toBeTruthy();
  });

  it('handles screen orientation changes', () => {
    const { getByTestId } = render(<ExploreScreen />);
    
    const container = getByTestId('explore-container');
    expect(container).toBeTruthy();
    
    // El componente debería manejar cambios de orientación
    expect(container.props.style).toHaveProperty('flex');
  });

  it('displays safe area view correctly', () => {
    const { getByTestId } = render(<ExploreScreen />);
    
    const safeArea = getByTestId('explore-container');
    expect(safeArea).toBeTruthy();
  });

  it('handles button interactions with proper feedback', () => {
    const { getByText } = render(<ExploreScreen />);
    
    const buttons = [
      getByText('Mi Ubicación'),
      getByText('Rutas de Evacuación'),
      getByText('Información')
    ];
    
    buttons.forEach(button => {
      expect(button).toBeTruthy();
      
      // Simular press
      fireEvent.press(button);
      
      // Verificar que se llamó Alert.alert
      expect(Alert.alert).toHaveBeenCalled();
    });
  });

  it('maintains proper component hierarchy', () => {
    const { getByTestId } = render(<ExploreScreen />);
    
    const container = getByTestId('explore-container');
    const map = getByTestId('interactive-map');
    
    expect(container).toBeTruthy();
    expect(map).toBeTruthy();
  });

  it('handles error states gracefully', () => {
    // Simular error en el componente del mapa
    const { InteractiveMap } = require('@/components/InteractiveMap');
    InteractiveMap.mockImplementationOnce(() => {
      throw new Error('Map error');
    });
    
    // El componente debería manejar errores sin crashear
    expect(() => {
      render(<ExploreScreen />);
    }).not.toThrow();
  });

  it('provides proper test IDs for testing', () => {
    const { getByTestId } = render(<ExploreScreen />);
    
    expect(getByTestId('explore-container')).toBeTruthy();
    expect(getByTestId('interactive-map')).toBeTruthy();
  });

  it('handles component lifecycle correctly', () => {
    const { unmount } = render(<ExploreScreen />);
    
    // El componente debería desmontarse sin errores
    expect(() => {
      unmount();
    }).not.toThrow();
  });

  it('displays proper spacing and layout', () => {
    const { getByTestId } = render(<ExploreScreen />);
    
    const container = getByTestId('explore-container');
    expect(container.props.style).toHaveProperty('flex', 1);
  });

  it('handles theme changes correctly', () => {
    const { useColorScheme } = require('@/hooks/useColorScheme');
    
    // Cambiar tema
    useColorScheme.mockReturnValue('dark');
    
    const { rerender, getByTestId } = render(<ExploreScreen />);
    
    // Cambiar de vuelta a light
    useColorScheme.mockReturnValue('light');
    rerender(<ExploreScreen />);
    
    const container = getByTestId('explore-container');
    expect(container).toBeTruthy();
  });

  it('maintains performance with frequent updates', () => {
    const { getByTestId } = render(<ExploreScreen />);
    
    const map = getByTestId('interactive-map');
    
    // Simular múltiples actualizaciones rápidas
    for (let i = 0; i < 10; i++) {
      // El componente debería manejar actualizaciones frecuentes
      expect(map).toBeTruthy();
    }
  });
});
