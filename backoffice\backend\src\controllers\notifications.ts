/**
 * 🌋 Volcano App Backend - Controlador de Notificaciones
 * Endpoints para gestión de notificaciones push y alertas en tiempo real
 */

import { NotificationTarget } from '@/services/notifications';
import { logAuditAction, supabaseAdmin } from '@/services/supabase';
import { AuthenticatedRequest } from '@/types';
import { logger } from '@/utils/logger';
import { Response } from 'express';

// =====================================================
// ENVIAR NOTIFICACIÓN MANUAL
// =====================================================

/**
 * Enviar notificación push manual
 * POST /api/notifications/send
 */
export async function sendManualNotification(req: AuthenticatedRequest, res: Response) {
  try {
    const {
      title,
      body,
      target_type = 'all',
      target_criteria,
      data,
      sound = false,
      vibration = false,
      priority = 'normal'
    } = req.body;

    // Validaciones básicas
    if (!title || !body) {
      return res.status(400).json({
        success: false,
        error: 'Title and body are required',
        timestamp: new Date()
      });
    }

    const target: NotificationTarget = {
      type: target_type,
      criteria: target_criteria
    };

    // Simular envío de notificación manual
    const payload = {
      title,
      body,
      data: {
        type: 'manual_notification',
        sent_by: req.user?.email,
        ...data
      },
      sound,
      vibration,
      priority
    };

    // Por ahora simulamos el envío
    const result = {
      success: true,
      sent_count: target_type === 'all' ? 100 : 10, // Simulado
      failed_count: 0,
      notification_id: `manual_${Date.now()}`
    };

    // Log de auditoría
    await logAuditAction(
      req.user?.id || '',
      'SEND_NOTIFICATION',
      'notifications',
      result.notification_id,
      null,
      { payload, target, result },
      req.ip,
      req.get('User-Agent')
    );

    logger.info(`Manual notification sent by ${req.user?.email}`, {
      title,
      target_type,
      sent_count: result.sent_count
    });

    res.json({
      success: true,
      data: result,
      timestamp: new Date()
    });

  } catch (error) {
    logger.error('Send manual notification error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to send notification',
      timestamp: new Date()
    });
  }
}

// =====================================================
// PROBAR NOTIFICACIÓN
// =====================================================

/**
 * Enviar notificación de prueba
 * POST /api/notifications/test
 */
export async function sendTestNotification(req: AuthenticatedRequest, res: Response) {
  try {
    const { device_id } = req.body;

    if (!device_id) {
      return res.status(400).json({
        success: false,
        error: 'Device ID is required for test notification',
        timestamp: new Date()
      });
    }

    const target: NotificationTarget = {
      type: 'device',
      criteria: { deviceIds: [device_id] }
    };

    const payload = {
      title: '🧪 Notificación de Prueba',
      body: 'Esta es una notificación de prueba del sistema Volcano App',
      data: {
        type: 'test_notification',
        sent_by: req.user?.email,
        timestamp: new Date().toISOString()
      },
      sound: true,
      vibration: true,
      priority: 'normal' as const
    };

    // Simular envío de prueba
    const result = {
      success: true,
      sent_count: 1,
      failed_count: 0,
      notification_id: `test_${Date.now()}`
    };

    logger.info(`Test notification sent to device ${device_id} by ${req.user?.email}`);

    res.json({
      success: true,
      data: result,
      message: 'Test notification sent successfully',
      timestamp: new Date()
    });

  } catch (error) {
    logger.error('Send test notification error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to send test notification',
      timestamp: new Date()
    });
  }
}

// =====================================================
// HISTORIAL DE NOTIFICACIONES
// =====================================================

/**
 * Obtener historial de notificaciones
 * GET /api/notifications/history
 */
export async function getNotificationHistory(req: AuthenticatedRequest, res: Response) {
  try {
    const {
      page = 1,
      limit = 20,
      type,
      start_date,
      end_date
    } = req.query as any;

    const offset = (parseInt(page) - 1) * parseInt(limit);

    let query = supabaseAdmin
      .from('notification_logs')
      .select(`
        id,
        entity_id,
        notification_type,
        title,
        body,
        target_type,
        sent_count,
        failed_count,
        success,
        notification_id,
        created_at
      `, { count: 'exact' })
      .order('created_at', { ascending: false })
      .range(offset, offset + parseInt(limit) - 1);

    // Filtros opcionales
    if (type) {
      query = query.eq('notification_type', type);
    }

    if (start_date) {
      query = query.gte('created_at', start_date);
    }

    if (end_date) {
      query = query.lte('created_at', end_date);
    }

    const { data: notifications, error, count } = await query;

    if (error) {
      throw error;
    }

    res.json({
      success: true,
      data: {
        notifications: notifications || [],
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count || 0,
          pages: Math.ceil((count || 0) / parseInt(limit))
        }
      },
      timestamp: new Date()
    });

  } catch (error) {
    logger.error('Get notification history error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get notification history',
      timestamp: new Date()
    });
  }
}

// =====================================================
// ESTADÍSTICAS DE NOTIFICACIONES
// =====================================================

/**
 * Obtener estadísticas de notificaciones
 * GET /api/notifications/stats
 */
export async function getNotificationStats(req: AuthenticatedRequest, res: Response) {
  try {
    const { period = '7d' } = req.query as any;

    // Calcular fecha de inicio basada en el período
    const now = new Date();
    const startDate = new Date();
    
    switch (period) {
      case '24h':
        startDate.setHours(now.getHours() - 24);
        break;
      case '7d':
        startDate.setDate(now.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(now.getDate() - 30);
        break;
      default:
        startDate.setDate(now.getDate() - 7);
    }

    // Obtener estadísticas de la base de datos
    const { data: stats, error } = await supabaseAdmin
      .from('notification_logs')
      .select('notification_type, sent_count, failed_count, success, created_at')
      .gte('created_at', startDate.toISOString());

    if (error) {
      throw error;
    }

    // Procesar estadísticas
    const processedStats = {
      total_sent: stats?.reduce((sum, n) => sum + (n.sent_count || 0), 0) || 0,
      total_failed: stats?.reduce((sum, n) => sum + (n.failed_count || 0), 0) || 0,
      success_rate: 0,
      by_type: {} as Record<string, any>,
      by_day: {} as Record<string, any>
    };

    const totalAttempts = processedStats.total_sent + processedStats.total_failed;
    processedStats.success_rate = totalAttempts > 0 
      ? (processedStats.total_sent / totalAttempts) * 100 
      : 0;

    // Agrupar por tipo
    stats?.forEach(stat => {
      const type = stat.notification_type || 'unknown';
      if (!processedStats.by_type[type]) {
        processedStats.by_type[type] = {
          sent: 0,
          failed: 0,
          count: 0
        };
      }
      processedStats.by_type[type].sent += stat.sent_count || 0;
      processedStats.by_type[type].failed += stat.failed_count || 0;
      processedStats.by_type[type].count += 1;
    });

    // Agrupar por día
    stats?.forEach(stat => {
      const day = stat.created_at?.split('T')[0] || 'unknown';
      if (!processedStats.by_day[day]) {
        processedStats.by_day[day] = {
          sent: 0,
          failed: 0,
          count: 0
        };
      }
      processedStats.by_day[day].sent += stat.sent_count || 0;
      processedStats.by_day[day].failed += stat.failed_count || 0;
      processedStats.by_day[day].count += 1;
    });

    res.json({
      success: true,
      data: {
        period,
        stats: processedStats,
        period_start: startDate.toISOString(),
        period_end: now.toISOString()
      },
      timestamp: new Date()
    });

  } catch (error) {
    logger.error('Get notification stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get notification statistics',
      timestamp: new Date()
    });
  }
}

// =====================================================
// CONFIGURACIÓN DE NOTIFICACIONES
// =====================================================

/**
 * Obtener configuración de notificaciones
 * GET /api/notifications/config
 */
export async function getNotificationConfig(req: AuthenticatedRequest, res: Response) {
  try {
    const { data: configs, error } = await supabaseAdmin
      .from('system_config')
      .select('key, value')
      .like('key', 'notification_%');

    if (error) {
      throw error;
    }

    const configObject = configs?.reduce((acc: any, config: any) => {
      acc[config.key] = config.value;
      return acc;
    }, {}) || {};

    // Configuración por defecto
    const defaultConfig = {
      notification_enabled: true,
      notification_sound_default: true,
      notification_vibration_default: true,
      notification_priority_emergency: 'high',
      notification_priority_warning: 'high',
      notification_priority_normal: 'normal'
    };

    const finalConfig = { ...defaultConfig, ...configObject };

    res.json({
      success: true,
      data: finalConfig,
      timestamp: new Date()
    });

  } catch (error) {
    logger.error('Get notification config error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get notification configuration',
      timestamp: new Date()
    });
  }
}

// =====================================================
// EXPORTACIONES
// =====================================================

export default {
  sendManualNotification,
  sendTestNotification,
  getNotificationHistory,
  getNotificationStats,
  getNotificationConfig
};
