/**
 * 🌋 Volcano App Frontend - Tests para ZonesTable
 * Tests unitarios para el componente de tabla de zonas
 */

import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi } from 'vitest';
import { ZonesTable } from '../ZonesTable';

// Mock data
const mockZones = [
  {
    id: '1',
    name: 'Zona Segura Centro',
    description: 'Zona de evacuación principal',
    zone_type: 'SAFE' as const,
    geometry: {
      type: 'Polygon' as const,
      coordinates: [[[-71.9048, -39.2904], [-71.9000, -39.2904], [-71.9000, -39.2850], [-71.9048, -39.2850], [-71.9048, -39.2904]]]
    },
    capacity: 1000,
    is_active: true,
    version: 1,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    created_by: {
      full_name: 'Admin User',
      email: '<EMAIL>'
    }
  },
  {
    id: '2',
    name: 'Zona de Peligro Norte',
    description: 'Zona de alto riesgo volcánico',
    zone_type: 'DANGER' as const,
    geometry: {
      type: 'Polygon' as const,
      coordinates: [[[-71.9100, -39.2800], [-71.9050, -39.2800], [-71.9050, -39.2750], [-71.9100, -39.2750], [-71.9100, -39.2800]]]
    },
    capacity: null,
    is_active: true,
    version: 1,
    created_at: '2024-01-02T00:00:00Z',
    updated_at: '2024-01-02T00:00:00Z',
    created_by: {
      full_name: 'Admin User',
      email: '<EMAIL>'
    }
  }
];

const mockProps = {
  zones: mockZones,
  onCreateZone: vi.fn(),
  onEditZone: vi.fn(),
  onDeleteZone: vi.fn()
};

describe('ZonesTable', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders zones table correctly', () => {
    render(<ZonesTable {...mockProps} />);
    
    // Verificar que se muestra el título
    expect(screen.getByText('Gestión de Zonas de Seguridad')).toBeInTheDocument();
    
    // Verificar que se muestra el botón de crear zona
    expect(screen.getByText('Nueva Zona')).toBeInTheDocument();
    
    // Verificar que se muestran las zonas
    expect(screen.getByText('Zona Segura Centro')).toBeInTheDocument();
    expect(screen.getByText('Zona de Peligro Norte')).toBeInTheDocument();
  });

  it('displays zone information correctly', () => {
    render(<ZonesTable {...mockProps} />);
    
    // Verificar información de la primera zona
    expect(screen.getByText('Zona Segura Centro')).toBeInTheDocument();
    expect(screen.getByText('Zona de evacuación principal')).toBeInTheDocument();
    expect(screen.getByText('SAFE')).toBeInTheDocument();
    expect(screen.getByText('1000')).toBeInTheDocument();
    
    // Verificar información de la segunda zona
    expect(screen.getByText('Zona de Peligro Norte')).toBeInTheDocument();
    expect(screen.getByText('Zona de alto riesgo volcánico')).toBeInTheDocument();
    expect(screen.getByText('DANGER')).toBeInTheDocument();
  });

  it('calls onCreateZone when create button is clicked', () => {
    render(<ZonesTable {...mockProps} />);
    
    const createButton = screen.getByText('Nueva Zona');
    fireEvent.click(createButton);
    
    expect(mockProps.onCreateZone).toHaveBeenCalledTimes(1);
  });

  it('calls onEditZone when edit button is clicked', () => {
    render(<ZonesTable {...mockProps} />);
    
    const editButtons = screen.getAllByText('Editar');
    fireEvent.click(editButtons[0]);
    
    expect(mockProps.onEditZone).toHaveBeenCalledTimes(1);
    expect(mockProps.onEditZone).toHaveBeenCalledWith(mockZones[0]);
  });

  it('calls onDeleteZone when delete button is clicked', () => {
    render(<ZonesTable {...mockProps} />);
    
    const deleteButtons = screen.getAllByText('Eliminar');
    fireEvent.click(deleteButtons[0]);
    
    expect(mockProps.onDeleteZone).toHaveBeenCalledTimes(1);
    expect(mockProps.onDeleteZone).toHaveBeenCalledWith('1');
  });

  it('shows correct zone type badges', () => {
    render(<ZonesTable {...mockProps} />);
    
    // Verificar que se muestran los badges de tipo de zona
    const safeBadge = screen.getByText('SAFE');
    const dangerBadge = screen.getByText('DANGER');
    
    expect(safeBadge).toBeInTheDocument();
    expect(dangerBadge).toBeInTheDocument();
  });

  it('shows active status correctly', () => {
    render(<ZonesTable {...mockProps} />);
    
    // Verificar que se muestran los estados activos
    const activeStatuses = screen.getAllByText('Activa');
    expect(activeStatuses).toHaveLength(2);
  });

  it('handles empty zones list', () => {
    render(<ZonesTable {...mockProps} zones={[]} />);
    
    // Verificar que se muestra mensaje cuando no hay zonas
    expect(screen.getByText('No hay zonas de seguridad configuradas')).toBeInTheDocument();
  });

  it('displays creation date correctly', () => {
    render(<ZonesTable {...mockProps} />);
    
    // Verificar que se muestran las fechas de creación
    expect(screen.getByText('01/01/2024')).toBeInTheDocument();
    expect(screen.getByText('02/01/2024')).toBeInTheDocument();
  });

  it('shows creator information', () => {
    render(<ZonesTable {...mockProps} />);
    
    // Verificar que se muestra información del creador
    const creatorInfo = screen.getAllByText('Admin User');
    expect(creatorInfo).toHaveLength(2);
  });

  it('handles zone capacity display', () => {
    render(<ZonesTable {...mockProps} />);
    
    // Verificar capacidad de la primera zona
    expect(screen.getByText('1000')).toBeInTheDocument();
    
    // Verificar que se muestra "N/A" para zonas sin capacidad
    expect(screen.getByText('N/A')).toBeInTheDocument();
  });

  it('applies correct styling for different zone types', () => {
    render(<ZonesTable {...mockProps} />);
    
    const safeBadge = screen.getByText('SAFE');
    const dangerBadge = screen.getByText('DANGER');
    
    // Verificar que los badges tienen las clases CSS correctas
    expect(safeBadge).toHaveClass('bg-green-100');
    expect(dangerBadge).toHaveClass('bg-red-100');
  });

  it('shows loading state when zones are being fetched', () => {
    render(<ZonesTable {...mockProps} zones={[]} loading={true} />);
    
    // Verificar que se muestra el estado de carga
    expect(screen.getByText('Cargando zonas...')).toBeInTheDocument();
  });

  it('handles search functionality', async () => {
    render(<ZonesTable {...mockProps} />);
    
    const searchInput = screen.getByPlaceholderText('Buscar zonas...');
    fireEvent.change(searchInput, { target: { value: 'Centro' } });
    
    await waitFor(() => {
      // Verificar que se filtra correctamente
      expect(screen.getByText('Zona Segura Centro')).toBeInTheDocument();
      expect(screen.queryByText('Zona de Peligro Norte')).not.toBeInTheDocument();
    });
  });

  it('handles filter by zone type', async () => {
    render(<ZonesTable {...mockProps} />);
    
    const filterSelect = screen.getByDisplayValue('Todos los tipos');
    fireEvent.change(filterSelect, { target: { value: 'SAFE' } });
    
    await waitFor(() => {
      // Verificar que se filtra por tipo
      expect(screen.getByText('Zona Segura Centro')).toBeInTheDocument();
      expect(screen.queryByText('Zona de Peligro Norte')).not.toBeInTheDocument();
    });
  });

  it('handles pagination correctly', () => {
    const manyZones = Array.from({ length: 25 }, (_, i) => ({
      ...mockZones[0],
      id: `zone-${i}`,
      name: `Zona ${i}`
    }));
    
    render(<ZonesTable {...mockProps} zones={manyZones} />);
    
    // Verificar que se muestra la paginación
    expect(screen.getByText('Página 1 de 3')).toBeInTheDocument();
    
    // Verificar botones de navegación
    expect(screen.getByText('Siguiente')).toBeInTheDocument();
    expect(screen.getByText('Anterior')).toBeDisabled();
  });
});
