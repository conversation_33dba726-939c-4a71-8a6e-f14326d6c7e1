/**
 * 🌋 Volcano App Frontend - Tests para useAuth
 * Tests de integración para el hook de autenticación
 */

import { renderHook, act, waitFor } from '@testing-library/react';
import { vi } from 'vitest';
import { useAuthState } from '../useAuth';

// Mock para localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn()
};

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage
});

// Mock para fetch
const mockFetch = vi.fn();
global.fetch = mockFetch;

describe('useAuth', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockLocalStorage.getItem.mockClear();
    mockLocalStorage.setItem.mockClear();
    mockLocalStorage.removeItem.mockClear();
    mockFetch.mockClear();
  });

  it('initializes with no user when no tokens in localStorage', async () => {
    mockLocalStorage.getItem.mockReturnValue(null);
    
    const { result } = renderHook(() => useAuthState());
    
    await waitFor(() => {
      expect(result.current.user).toBeNull();
      expect(result.current.tokens).toBeNull();
      expect(result.current.isLoading).toBe(false);
    });
  });

  it('loads tokens from localStorage on initialization', async () => {
    const mockTokens = {
      access_token: 'mock-access-token',
      refresh_token: 'mock-refresh-token'
    };
    
    mockLocalStorage.getItem.mockReturnValue(JSON.stringify(mockTokens));
    
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: () => Promise.resolve({
        success: true,
        data: {
          id: '1',
          email: '<EMAIL>',
          full_name: 'Admin User',
          role: 'ADMIN'
        }
      })
    });
    
    const { result } = renderHook(() => useAuthState());
    
    await waitFor(() => {
      expect(result.current.tokens).toEqual(mockTokens);
      expect(result.current.user).toEqual({
        id: '1',
        email: '<EMAIL>',
        full_name: 'Admin User',
        role: 'ADMIN'
      });
      expect(result.current.isLoading).toBe(false);
    });
  });

  it('handles invalid tokens in localStorage', async () => {
    mockLocalStorage.getItem.mockReturnValue('invalid-json');
    
    const { result } = renderHook(() => useAuthState());
    
    await waitFor(() => {
      expect(result.current.user).toBeNull();
      expect(result.current.tokens).toBeNull();
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('volcano_auth_tokens');
    });
  });

  it('handles failed user profile fetch', async () => {
    const mockTokens = {
      access_token: 'invalid-token',
      refresh_token: 'mock-refresh-token'
    };
    
    mockLocalStorage.getItem.mockReturnValue(JSON.stringify(mockTokens));
    
    mockFetch.mockResolvedValueOnce({
      ok: false,
      status: 401
    });
    
    const { result } = renderHook(() => useAuthState());
    
    await waitFor(() => {
      expect(result.current.user).toBeNull();
      expect(result.current.tokens).toBeNull();
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('volcano_auth_tokens');
    });
  });

  it('handles login correctly', async () => {
    const { result } = renderHook(() => useAuthState());
    
    const mockTokens = {
      access_token: 'new-access-token',
      refresh_token: 'new-refresh-token'
    };
    
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: () => Promise.resolve({
        success: true,
        data: {
          id: '1',
          email: '<EMAIL>',
          full_name: 'Admin User',
          role: 'ADMIN'
        }
      })
    });
    
    act(() => {
      result.current.login(mockTokens);
    });
    
    await waitFor(() => {
      expect(result.current.tokens).toEqual(mockTokens);
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'volcano_auth_tokens',
        JSON.stringify(mockTokens)
      );
    });
  });

  it('handles logout correctly', async () => {
    const mockTokens = {
      access_token: 'mock-access-token',
      refresh_token: 'mock-refresh-token'
    };
    
    mockLocalStorage.getItem.mockReturnValue(JSON.stringify(mockTokens));
    
    const { result } = renderHook(() => useAuthState());
    
    // Esperar a que se cargue el estado inicial
    await waitFor(() => {
      expect(result.current.tokens).toEqual(mockTokens);
    });
    
    act(() => {
      result.current.logout();
    });
    
    expect(result.current.user).toBeNull();
    expect(result.current.tokens).toBeNull();
    expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('volcano_auth_tokens');
  });

  it('fetches user profile with correct headers', async () => {
    const mockTokens = {
      access_token: 'mock-access-token',
      refresh_token: 'mock-refresh-token'
    };
    
    mockLocalStorage.getItem.mockReturnValue(JSON.stringify(mockTokens));
    
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: () => Promise.resolve({
        success: true,
        data: {
          id: '1',
          email: '<EMAIL>',
          full_name: 'Admin User',
          role: 'ADMIN'
        }
      })
    });
    
    renderHook(() => useAuthState());
    
    await waitFor(() => {
      expect(mockFetch).toHaveBeenCalledWith('/auth/me', {
        headers: {
          'Authorization': 'Bearer mock-access-token',
        },
      });
    });
  });

  it('handles network errors during profile fetch', async () => {
    const mockTokens = {
      access_token: 'mock-access-token',
      refresh_token: 'mock-refresh-token'
    };
    
    mockLocalStorage.getItem.mockReturnValue(JSON.stringify(mockTokens));
    
    mockFetch.mockRejectedValueOnce(new Error('Network error'));
    
    const { result } = renderHook(() => useAuthState());
    
    await waitFor(() => {
      expect(result.current.user).toBeNull();
      expect(result.current.tokens).toBeNull();
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('volcano_auth_tokens');
    });
  });

  it('maintains loading state during initialization', () => {
    mockLocalStorage.getItem.mockReturnValue(null);
    
    const { result } = renderHook(() => useAuthState());
    
    expect(result.current.isLoading).toBe(true);
  });

  it('handles token refresh scenario', async () => {
    const mockTokens = {
      access_token: 'expired-token',
      refresh_token: 'valid-refresh-token'
    };
    
    mockLocalStorage.getItem.mockReturnValue(JSON.stringify(mockTokens));
    
    // Primera llamada falla (token expirado)
    mockFetch.mockResolvedValueOnce({
      ok: false,
      status: 401
    });
    
    const { result } = renderHook(() => useAuthState());
    
    await waitFor(() => {
      expect(result.current.user).toBeNull();
      expect(result.current.tokens).toBeNull();
    });
  });

  it('preserves user state across re-renders', async () => {
    const mockTokens = {
      access_token: 'mock-access-token',
      refresh_token: 'mock-refresh-token'
    };
    
    const mockUser = {
      id: '1',
      email: '<EMAIL>',
      full_name: 'Admin User',
      role: 'ADMIN'
    };
    
    mockLocalStorage.getItem.mockReturnValue(JSON.stringify(mockTokens));
    
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: () => Promise.resolve({
        success: true,
        data: mockUser
      })
    });
    
    const { result, rerender } = renderHook(() => useAuthState());
    
    await waitFor(() => {
      expect(result.current.user).toEqual(mockUser);
    });
    
    rerender();
    
    expect(result.current.user).toEqual(mockUser);
    expect(result.current.tokens).toEqual(mockTokens);
  });

  it('handles concurrent login calls correctly', async () => {
    const { result } = renderHook(() => useAuthState());
    
    const mockTokens1 = {
      access_token: 'token-1',
      refresh_token: 'refresh-1'
    };
    
    const mockTokens2 = {
      access_token: 'token-2',
      refresh_token: 'refresh-2'
    };
    
    mockFetch.mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({
        success: true,
        data: {
          id: '1',
          email: '<EMAIL>',
          full_name: 'Admin User',
          role: 'ADMIN'
        }
      })
    });
    
    act(() => {
      result.current.login(mockTokens1);
      result.current.login(mockTokens2);
    });
    
    await waitFor(() => {
      expect(result.current.tokens).toEqual(mockTokens2);
    });
  });

  it('clears state on logout even if localStorage fails', async () => {
    const { result } = renderHook(() => useAuthState());
    
    // Simular error en localStorage
    mockLocalStorage.removeItem.mockImplementationOnce(() => {
      throw new Error('localStorage error');
    });
    
    act(() => {
      result.current.logout();
    });
    
    expect(result.current.user).toBeNull();
    expect(result.current.tokens).toBeNull();
  });
});
