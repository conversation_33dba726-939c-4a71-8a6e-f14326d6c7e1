/**
 * 🌋 Volcano App Mobile - Jest Setup
 * Configuración global para tests de la aplicación móvil
 */

import 'react-native-gesture-handler/jestSetup';

// Mock para React Native Reanimated
jest.mock('react-native-reanimated', () => {
  const Reanimated = require('react-native-reanimated/mock');
  Reanimated.default.call = () => {};
  return Reanimated;
});

// Mock para Expo modules
jest.mock('expo-constants', () => ({
  expoConfig: {
    extra: {
      supabaseUrl: 'https://test.supabase.co',
      supabaseAnonKey: 'test-anon-key',
      eas: {
        projectId: 'test-project-id'
      }
    }
  },
  default: {
    expoConfig: {
      extra: {
        supabaseUrl: 'https://test.supabase.co',
        supabaseAnonKey: 'test-anon-key'
      }
    }
  }
}));

jest.mock('expo-device', () => ({
  isDevice: true,
  deviceType: 1,
  deviceName: 'Test Device'
}));

jest.mock('expo-location', () => ({
  requestForegroundPermissionsAsync: jest.fn(() => 
    Promise.resolve({ status: 'granted' })
  ),
  getCurrentPositionAsync: jest.fn(() => 
    Promise.resolve({
      coords: {
        latitude: -39.2904,
        longitude: -71.9048,
        accuracy: 10
      }
    })
  ),
  watchPositionAsync: jest.fn(() => 
    Promise.resolve({ remove: jest.fn() })
  ),
  Accuracy: {
    High: 1,
    Balanced: 2,
    Low: 3
  }
}));

jest.mock('expo-notifications', () => ({
  requestPermissionsAsync: jest.fn(() => 
    Promise.resolve({ status: 'granted' })
  ),
  getExpoPushTokenAsync: jest.fn(() => 
    Promise.resolve({ data: 'test-push-token' })
  ),
  scheduleNotificationAsync: jest.fn(() => 
    Promise.resolve('test-notification-id')
  ),
  addNotificationReceivedListener: jest.fn(() => ({ remove: jest.fn() })),
  addNotificationResponseReceivedListener: jest.fn(() => ({ remove: jest.fn() })),
  setNotificationHandler: jest.fn()
}));

jest.mock('expo-haptics', () => ({
  impactAsync: jest.fn(() => Promise.resolve()),
  notificationAsync: jest.fn(() => Promise.resolve()),
  selectionAsync: jest.fn(() => Promise.resolve()),
  ImpactFeedbackStyle: {
    Light: 'light',
    Medium: 'medium',
    Heavy: 'heavy'
  },
  NotificationFeedbackType: {
    Success: 'success',
    Warning: 'warning',
    Error: 'error'
  }
}));

// Mock para React Native modules
jest.mock('react-native', () => {
  const RN = jest.requireActual('react-native');
  return {
    ...RN,
    Platform: {
      OS: 'ios',
      select: jest.fn((obj) => obj.ios || obj.default)
    },
    Vibration: {
      vibrate: jest.fn()
    },
    Alert: {
      alert: jest.fn()
    },
    Linking: {
      openURL: jest.fn(() => Promise.resolve())
    }
  };
});

// Mock para React Native Maps
jest.mock('react-native-maps', () => {
  const React = require('react');
  return {
    __esModule: true,
    default: React.forwardRef(() => null),
    Marker: () => null,
    Polygon: () => null,
    Polyline: () => null,
    Circle: () => null,
    PROVIDER_GOOGLE: 'google'
  };
});

// Mock para WebView
jest.mock('react-native-webview', () => ({
  WebView: 'WebView'
}));

// Mock para Supabase
jest.mock('@supabase/supabase-js', () => ({
  createClient: jest.fn(() => ({
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          single: jest.fn(() => Promise.resolve({ data: null, error: null }))
        }))
      })),
      insert: jest.fn(() => Promise.resolve({ data: null, error: null })),
      update: jest.fn(() => Promise.resolve({ data: null, error: null })),
      delete: jest.fn(() => Promise.resolve({ data: null, error: null }))
    })),
    auth: {
      signIn: jest.fn(() => Promise.resolve({ user: null, error: null })),
      signOut: jest.fn(() => Promise.resolve({ error: null })),
      getUser: jest.fn(() => Promise.resolve({ user: null, error: null }))
    },
    channel: jest.fn(() => ({
      on: jest.fn(() => ({
        subscribe: jest.fn()
      }))
    }))
  }))
}));

// Mock para AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(() => Promise.resolve(null)),
  setItem: jest.fn(() => Promise.resolve()),
  removeItem: jest.fn(() => Promise.resolve()),
  clear: jest.fn(() => Promise.resolve())
}));

// Mock para React Query
jest.mock('@tanstack/react-query', () => ({
  useQuery: jest.fn(() => ({
    data: null,
    isLoading: false,
    error: null,
    refetch: jest.fn()
  })),
  useMutation: jest.fn(() => ({
    mutate: jest.fn(),
    isLoading: false,
    error: null
  })),
  QueryClient: jest.fn(() => ({
    invalidateQueries: jest.fn()
  })),
  QueryClientProvider: ({ children }) => children
}));

// Mock para Socket.IO
jest.mock('socket.io-client', () => ({
  io: jest.fn(() => ({
    on: jest.fn(),
    emit: jest.fn(),
    disconnect: jest.fn(),
    connected: true
  }))
}));

// Configuración global para tests
global.console = {
  ...console,
  warn: jest.fn(),
  error: jest.fn()
};

// Timeout para tests async
jest.setTimeout(30000);

// Mock para fetch
global.fetch = jest.fn(() =>
  Promise.resolve({
    ok: true,
    json: () => Promise.resolve({}),
    text: () => Promise.resolve('')
  })
);

// Configuración para React Native Testing Library
import '@testing-library/react-native/extend-expect';
