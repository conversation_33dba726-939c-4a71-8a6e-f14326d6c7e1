/**
 * 🌋 Volcano App Backoffice - Mapa Interactivo con Herramientas de Dibujo
 * Componente de mapa para crear y editar zonas de seguridad con Leaflet Draw
 */

import { useToast } from '@/hooks/use-toast';
import { useMapDrawing } from '@/hooks/useMapDrawing';
import {
    formatArea,
    geoJSONToLeaflet,
    VOLCANO_COORDS,
    ZoneGeometry
} from '@/services/geometry';
import L from 'leaflet';
import 'leaflet-draw';
import 'leaflet-draw/dist/leaflet.draw.css';
import 'leaflet/dist/leaflet.css';
import { Info } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { MapContainer, TileLayer, useMap } from 'react-leaflet';

// =====================================================
// TIPOS Y INTERFACES
// =====================================================

interface Zone {
  id: string;
  name: string;
  description: string;
  zone_type: 'SAFE' | 'EMERGENCY' | 'DANGER' | 'EVACUATION' | 'RESTRICTED';
  geometry: ZoneGeometry;
  is_active: boolean;
}

interface ZoneDrawingMapProps {
  zones: Zone[];
  onZoneCreate: (geometry: ZoneGeometry) => void;
  onZoneEdit: (zone: Zone, newGeometry: ZoneGeometry) => void;
  onZoneDelete: (zoneId: string) => void;
}

// =====================================================
// CONFIGURACIÓN DE LEAFLET
// =====================================================

// Configurar iconos de Leaflet
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

// =====================================================
// COMPONENTE DE CONTROLES DE DIBUJO MEJORADO
// =====================================================

function DrawingControls({ zones, onZoneCreate, onZoneEdit, onZoneDelete }: ZoneDrawingMapProps) {
  const map = useMap();
  const { toast } = useToast();

  console.log('🗺️ DrawingControls rendered with:', { zones: zones.length, map: !!map });

  // Usar el hook personalizado para manejo de dibujo
  const {
    drawingState,
    drawnItemsRef,
    setupDrawControl,
    setupGlobalFunctions,
    getZoneColor,
    handlers
  } = useMapDrawing(map, zones, {
    onZoneCreate,
    onZoneEdit,
    onZoneDelete
  });

  console.log('🎯 useMapDrawing state:', drawingState);

  useEffect(() => {
    if (!map) return;

    // Crear grupo para elementos dibujados
    const drawnItems = new L.FeatureGroup();
    map.addLayer(drawnItems);
    drawnItemsRef.current = drawnItems;

    // Configurar controles de dibujo usando el hook
    const drawControl = setupDrawControl();
    if (!drawControl) return;

    // Configurar funciones globales para popups
    setupGlobalFunctions();

    // Configurar eventos de dibujo usando los handlers del hook
    map.on(L.Draw.Event.CREATED, handlers.onDrawCreated);
    map.on(L.Draw.Event.EDITED, handlers.onDrawEdited);
    map.on(L.Draw.Event.DELETED, handlers.onDrawDeleted);
    map.on(L.Draw.Event.DRAWSTART, handlers.onDrawStart);
    map.on(L.Draw.Event.DRAWSTOP, handlers.onDrawStop);

    return () => {
      // Limpiar eventos
      map.off(L.Draw.Event.CREATED, handlers.onDrawCreated);
      map.off(L.Draw.Event.EDITED, handlers.onDrawEdited);
      map.off(L.Draw.Event.DELETED, handlers.onDrawDeleted);
      map.off(L.Draw.Event.DRAWSTART, handlers.onDrawStart);
      map.off(L.Draw.Event.DRAWSTOP, handlers.onDrawStop);

      // Limpiar controles y layers
      if (drawControl) {
        map.removeControl(drawControl);
      }
      if (drawnItemsRef.current) {
        map.removeLayer(drawnItemsRef.current);
      }

      // Limpiar funciones globales
      delete (window as any).confirmZone;
      delete (window as any).cancelZone;
      delete (window as any).editZone;
      delete (window as any).deleteZone;
    };
  }, [map, setupDrawControl, setupGlobalFunctions, handlers]);

  // Cargar zonas existentes
  useEffect(() => {
    if (!map || !drawnItemsRef.current) return;

    const drawnItems = drawnItemsRef.current;

    // Limpiar zonas existentes
    drawnItems.clearLayers();

    // Agregar zonas existentes al mapa
    zones.forEach(zone => {
      if (zone.geometry && zone.geometry.coordinates && zone.geometry.coordinates[0]) {
        try {
          const latLngs = geoJSONToLeaflet(zone.geometry);

          const polygon = L.polygon(latLngs, {
            color: getZoneColor(zone.zone_type),
            fillOpacity: zone.is_active ? 0.3 : 0.1,
            weight: 2,
            opacity: zone.is_active ? 1 : 0.5
          });

          // Agregar datos de zona al layer
          (polygon as any).zoneData = zone;

          // Popup mejorado con información de la zona
          const popupContent = createZonePopup(zone);
          polygon.bindPopup(popupContent);
          drawnItems.addLayer(polygon);
        } catch (error) {
          console.error('Error loading zone:', zone.id, error);
          toast({
            title: 'Error',
            description: `No se pudo cargar la zona: ${zone.name}`,
            variant: 'destructive',
          });
        }
      }
    });

    // Funciones globales para los botones del popup de zonas existentes
    (window as any).editZone = (zoneId: string) => {
      const zone = zones.find(z => z.id === zoneId);
      if (zone) {
        // Para editar, necesitamos pasar la geometría actual
        onZoneEdit(zone, zone.geometry);
        map.closePopup();
      }
    };

    (window as any).deleteZone = (zoneId: string) => {
      if (confirm('¿Estás seguro de que quieres eliminar esta zona?')) {
        onZoneDelete(zoneId);
        map.closePopup();
      }
    };

  }, [zones, map, onZoneEdit, onZoneDelete, getZoneColor, toast]);

  // Función para crear popup de zona existente
  const createZonePopup = (zone: Zone): string => {
    const area = zone.geometry ? formatArea(zone.geometry.coordinates[0].length > 0 ? 1000 : 0) : 'N/A';

    return `
      <div style="min-width: 250px; font-family: system-ui;">
        <h4 style="margin: 0 0 10px 0; color: #1f2937;">${zone.name}</h4>
        <div style="margin-bottom: 10px; padding: 8px; background: #f3f4f6; border-radius: 4px;">
          <p style="margin: 0; font-size: 12px; color: #6b7280;"><strong>Tipo:</strong> ${getZoneTypeLabel(zone.zone_type)}</p>
          <p style="margin: 0; font-size: 12px; color: #6b7280;"><strong>Estado:</strong> ${zone.is_active ? 'Activa' : 'Inactiva'}</p>
          <p style="margin: 0; font-size: 12px; color: #6b7280;"><strong>Área:</strong> ${area}</p>
        </div>
        <p style="margin: 0 0 12px 0; font-size: 13px; color: #4b5563;">${zone.description}</p>
        <div style="display: flex; gap: 8px;">
          <button onclick="window.editZone('${zone.id}')"
                  style="flex: 1; background: #3b82f6; color: white; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer; font-size: 12px;">
            ✏️ Editar
          </button>
          <button onclick="window.deleteZone('${zone.id}')"
                  style="flex: 1; background: #ef4444; color: white; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer; font-size: 12px;">
            🗑️ Eliminar
          </button>
        </div>
      </div>
    `;
  };

  return null;
}

// =====================================================
// FUNCIONES AUXILIARES
// =====================================================

function getZoneColor(type: string): string {
  switch (type) {
    case 'SAFE': return '#22C55E';
    case 'EMERGENCY': return '#3B82F6';
    case 'EVACUATION': return '#F59E0B';
    case 'DANGER': return '#EF4444';
    case 'RESTRICTED': return '#6B7280';
    default: return '#6B7280';
  }
}

function getZoneTypeLabel(type: string): string {
  switch (type) {
    case 'SAFE': return 'Segura';
    case 'EMERGENCY': return 'Emergencia';
    case 'EVACUATION': return 'Evacuación';
    case 'DANGER': return 'Peligro';
    case 'RESTRICTED': return 'Restringida';
    default: return type;
  }
}

// =====================================================
// COMPONENTE PRINCIPAL
// =====================================================

export function ZoneDrawingMap({ zones, onZoneCreate, onZoneEdit, onZoneDelete }: ZoneDrawingMapProps) {
  // Usar coordenadas del Volcán Villarrica como centro
  const [mapCenter] = useState<[number, number]>([VOLCANO_COORDS.lat, VOLCANO_COORDS.lng]);
  const [mapZoom] = useState(12);

  console.log('🌋 ZoneDrawingMap rendered with:', {
    zones: zones.length,
    center: mapCenter,
    zoom: mapZoom,
    volcanoCoords: VOLCANO_COORDS
  });

  return (
    <div className="h-[500px] w-full rounded-lg overflow-hidden border shadow-sm">
      <MapContainer
        center={mapCenter}
        zoom={mapZoom}
        style={{ height: '100%', width: '100%' }}
        className="z-0"
      >
        <TileLayer
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
          maxZoom={18}
        />

        {/* Marcador del volcán */}
        <VolcanoMarker />

        {/* Controles de dibujo */}
        <DrawingControls
          zones={zones}
          onZoneCreate={onZoneCreate}
          onZoneEdit={onZoneEdit}
          onZoneDelete={onZoneDelete}
        />
      </MapContainer>

      {/* Panel de información */}
      <DrawingInfo />
    </div>
  );
}

// =====================================================
// COMPONENTE DE MARCADOR DEL VOLCÁN
// =====================================================

function VolcanoMarker() {
  const map = useMap();

  useEffect(() => {
    // Crear icono personalizado para el volcán
    const volcanoIcon = L.divIcon({
      className: 'volcano-marker',
      html: `
        <div style="
          background: #ef4444;
          border-radius: 50%;
          width: 24px;
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
          border: 2px solid white;
          box-shadow: 0 2px 4px rgba(0,0,0,0.3);
          font-size: 14px;
        ">🌋</div>
      `,
      iconSize: [24, 24],
      iconAnchor: [12, 12],
    });

    const volcanoMarker = L.marker([VOLCANO_COORDS.lat, VOLCANO_COORDS.lng], {
      icon: volcanoIcon
    }).addTo(map);

    volcanoMarker.bindPopup(`
      <div style="text-align: center; font-family: system-ui;">
        <h4 style="margin: 0 0 8px 0; color: #1f2937;">🌋 Volcán Villarrica</h4>
        <p style="margin: 0; font-size: 12px; color: #6b7280;">
          Lat: ${VOLCANO_COORDS.lat}<br>
          Lng: ${VOLCANO_COORDS.lng}
        </p>
      </div>
    `);

    return () => {
      map.removeLayer(volcanoMarker);
    };
  }, [map]);

  return null;
}

// =====================================================
// COMPONENTE DE INFORMACIÓN DE DIBUJO
// =====================================================

function DrawingInfo() {
  return (
    <div className="absolute top-2 right-2 bg-white/90 backdrop-blur-sm rounded-lg p-3 shadow-lg border z-10 max-w-xs">
      <div className="flex items-center gap-2 mb-2">
        <Info className="h-4 w-4 text-blue-500" />
        <span className="text-sm font-medium">Herramientas de Dibujo</span>
      </div>
      <div className="text-xs text-gray-600 space-y-1">
        <p>• Usa las herramientas para dibujar zonas</p>
        <p>• Haz clic en las zonas para editarlas</p>
        <p>• Las geometrías se validan automáticamente</p>
      </div>
    </div>
  );
}

export default ZoneDrawingMap;
