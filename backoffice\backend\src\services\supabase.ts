/**
 * 🌋 Volcano App Backend - Servicio de Supabase
 * Configuración y utilidades para interactuar con Supabase
 */

import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { logger } from '@/utils/logger';
import { CONFIG } from '@/config/env';

// =====================================================
// CONFIGURACIÓN DE SUPABASE
// =====================================================

const { SUPABASE_URL, SUPABASE_ANON_KEY, SUPABASE_SERVICE_ROLE_KEY } = CONFIG.DATABASE;

if (!SUPABASE_URL || !SUPABASE_ANON_KEY) {
  throw new Error('Missing Supabase configuration. Please check your environment variables.');
}

// Cliente público (con RLS habilitado)
export const supabase: SupabaseClient = createClient(SUPABASE_URL, SUPABASE_ANON_KEY, {
  auth: {
    autoRefreshToken: true,
    persistSession: false
  }
});

// Cliente administrativo (bypassa RLS)
export const supabaseAdmin: SupabaseClient = createClient(
  SUPABASE_URL,
  SUPABASE_SERVICE_ROLE_KEY || SUPABASE_ANON_KEY,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

// =====================================================
// UTILIDADES DE BASE DE DATOS
// =====================================================

/**
 * Ejecuta una consulta SQL raw en Supabase
 */
export async function executeRawQuery<T = any>(
  query: string, 
  params: any[] = []
): Promise<T[]> {
  try {
    const { data, error } = await supabaseAdmin.rpc('execute_sql', {
      query,
      params
    });

    if (error) {
      logger.error('Error executing raw query:', { error, query });
      throw error;
    }

    return data || [];
  } catch (error) {
    logger.error('Failed to execute raw query:', { error, query });
    throw error;
  }
}

/**
 * Verifica la conexión con la base de datos
 */
export async function checkDatabaseConnection(): Promise<boolean> {
  try {
    const { error } = await supabase
      .from('system_config')
      .select('key')
      .limit(1);

    if (error) {
      logger.error('Database connection check failed:', error);
      return false;
    }

    logger.info('Database connection successful');
    return true;
  } catch (error) {
    logger.error('Database connection error:', error);
    return false;
  }
}

/**
 * Obtiene estadísticas del sistema
 */
export async function getSystemStats() {
  try {
    const { data, error } = await supabaseAdmin.rpc('get_system_stats');

    if (error) {
      logger.error('Error getting system stats:', error);
      throw error;
    }

    return data?.[0] || {
      total_alerts: 0,
      active_alerts: 0,
      total_zones: 0,
      active_zones: 0,
      total_users: 0,
      active_users_last_hour: 0
    };
  } catch (error) {
    logger.error('Failed to get system stats:', error);
    throw error;
  }
}

/**
 * Limpia datos antiguos
 */
export async function cleanupOldData(): Promise<void> {
  try {
    const { error } = await supabaseAdmin.rpc('cleanup_old_data');

    if (error) {
      logger.error('Error cleaning up old data:', error);
      throw error;
    }

    logger.info('Old data cleanup completed successfully');
  } catch (error) {
    logger.error('Failed to cleanup old data:', error);
    throw error;
  }
}

// =====================================================
// UTILIDADES GEOESPACIALES
// =====================================================

/**
 * Convierte coordenadas a formato PostGIS Point
 */
export function coordinatesToPostGIS(lat: number, lng: number): string {
  return `POINT(${lng} ${lat})`;
}

/**
 * Convierte GeoJSON a formato PostGIS
 */
export function geoJSONToPostGIS(geoJSON: any): string {
  return `ST_GeomFromGeoJSON('${JSON.stringify(geoJSON)}')`;
}

/**
 * Valida si un punto está dentro de una zona
 */
export async function isPointInZone(
  lat: number, 
  lng: number, 
  zoneId: string
): Promise<boolean> {
  try {
    const { data, error } = await supabaseAdmin.rpc('point_in_zone', {
      point_lat: lat,
      point_lng: lng,
      zone_id: zoneId
    });

    if (error) {
      logger.error('Error checking point in zone:', error);
      return false;
    }

    return data || false;
  } catch (error) {
    logger.error('Failed to check point in zone:', error);
    return false;
  }
}

/**
 * Calcula la distancia entre dos puntos
 */
export async function calculateDistance(
  lat1: number,
  lng1: number,
  lat2: number,
  lng2: number
): Promise<number> {
  try {
    const { data, error } = await supabaseAdmin.rpc('calculate_distance', {
      lat1,
      lng1,
      lat2,
      lng2
    });

    if (error) {
      logger.error('Error calculating distance:', error);
      return 0;
    }

    return data || 0;
  } catch (error) {
    logger.error('Failed to calculate distance:', error);
    return 0;
  }
}

// =====================================================
// UTILIDADES DE AUDITORÍA
// =====================================================

/**
 * Registra una acción en el log de auditoría
 */
export async function logAuditAction(
  userId: string,
  action: string,
  tableName?: string,
  recordId?: string,
  oldValues?: any,
  newValues?: any,
  ipAddress?: string,
  userAgent?: string
): Promise<void> {
  try {
    const { error } = await supabaseAdmin
      .from('audit_logs')
      .insert({
        user_id: userId,
        action,
        table_name: tableName,
        record_id: recordId,
        old_values: oldValues,
        new_values: newValues,
        ip_address: ipAddress,
        user_agent: userAgent
      });

    if (error) {
      logger.error('Error logging audit action:', error);
    }
  } catch (error) {
    logger.error('Failed to log audit action:', error);
  }
}

// =====================================================
// UTILIDADES DE CONFIGURACIÓN
// =====================================================

/**
 * Obtiene un valor de configuración del sistema
 */
export async function getSystemConfig(key: string): Promise<any> {
  try {
    const { data, error } = await supabaseAdmin
      .from('system_config')
      .select('value')
      .eq('key', key)
      .single();

    if (error) {
      logger.error(`Error getting system config for key ${key}:`, error);
      return null;
    }

    return data?.value;
  } catch (error) {
    logger.error(`Failed to get system config for key ${key}:`, error);
    return null;
  }
}

/**
 * Actualiza un valor de configuración del sistema
 */
export async function updateSystemConfig(
  key: string, 
  value: any, 
  userId?: string
): Promise<void> {
  try {
    const { error } = await supabaseAdmin
      .from('system_config')
      .upsert({
        key,
        value,
        updated_by: userId,
        updated_at: new Date().toISOString()
      });

    if (error) {
      logger.error(`Error updating system config for key ${key}:`, error);
      throw error;
    }

    logger.info(`System config updated for key: ${key}`);
  } catch (error) {
    logger.error(`Failed to update system config for key ${key}:`, error);
    throw error;
  }
}

// =====================================================
// HEALTH CHECK
// =====================================================

/**
 * Verifica el estado de salud de Supabase
 */
export async function healthCheck(): Promise<{
  database: boolean;
  auth: boolean;
  storage: boolean;
}> {
  const health = {
    database: false,
    auth: false,
    storage: false
  };

  try {
    // Check database
    health.database = await checkDatabaseConnection();

    // Check auth (simple test)
    try {
      await supabase.auth.getSession();
      health.auth = true;
    } catch {
      health.auth = false;
    }

    // Check storage (simple test)
    try {
      const { data } = await supabase.storage.listBuckets();
      health.storage = Array.isArray(data);
    } catch {
      health.storage = false;
    }

  } catch (error) {
    logger.error('Health check error:', error);
  }

  return health;
}

// =====================================================
// INICIALIZACIÓN
// =====================================================

/**
 * Inicializa el servicio de Supabase
 */
export async function initializeSupabase(): Promise<void> {
  try {
    logger.info('Initializing Supabase connection...');
    
    const isConnected = await checkDatabaseConnection();
    if (!isConnected) {
      throw new Error('Failed to connect to Supabase database');
    }

    logger.info('Supabase initialized successfully');
  } catch (error) {
    logger.error('Failed to initialize Supabase:', error);
    throw error;
  }
}

// Exportar instancias por defecto
export default {
  supabase,
  supabaseAdmin,
  checkDatabaseConnection,
  getSystemStats,
  cleanupOldData,
  healthCheck,
  initializeSupabase
};
