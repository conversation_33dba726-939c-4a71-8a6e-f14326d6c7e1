/**
 * 🌋 Volcano App Frontend - Tests para AuthContext
 * Tests de integración para el contexto de autenticación
 */

import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi } from 'vitest';
import React, { useContext } from 'react';
import { AuthProvider, AuthContext } from '../AuthContext';

// Mock para react-hot-toast
vi.mock('react-hot-toast', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn()
  }
}));

// Mock para localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn()
};

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage
});

// Mock para fetch
const mockFetch = vi.fn();
global.fetch = mockFetch;

// Mock para authService
vi.mock('@/services/auth', () => ({
  authService: {
    login: vi.fn(),
    logout: vi.fn(),
    refreshToken: vi.fn(),
    setAuthToken: vi.fn(),
    getStoredTokens: vi.fn(),
    clearStoredTokens: vi.fn()
  }
}));

// Componente de prueba para usar el contexto
const TestComponent = () => {
  const { user, tokens, isLoading, login, logout, refreshToken } = useContext(AuthContext);
  
  return (
    <div>
      <div data-testid="user">{user ? user.email : 'No user'}</div>
      <div data-testid="tokens">{tokens ? 'Has tokens' : 'No tokens'}</div>
      <div data-testid="loading">{isLoading ? 'Loading' : 'Not loading'}</div>
      <button onClick={() => login('<EMAIL>', 'password')}>Login</button>
      <button onClick={logout}>Logout</button>
      <button onClick={refreshToken}>Refresh</button>
    </div>
  );
};

const renderWithProvider = (children: React.ReactNode) => {
  return render(
    <AuthProvider>
      {children}
    </AuthProvider>
  );
};

describe('AuthContext', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockLocalStorage.getItem.mockClear();
    mockLocalStorage.setItem.mockClear();
    mockLocalStorage.removeItem.mockClear();
    mockFetch.mockClear();
  });

  it('provides initial state correctly', () => {
    mockLocalStorage.getItem.mockReturnValue(null);
    
    renderWithProvider(<TestComponent />);
    
    expect(screen.getByTestId('user')).toHaveTextContent('No user');
    expect(screen.getByTestId('tokens')).toHaveTextContent('No tokens');
    expect(screen.getByTestId('loading')).toHaveTextContent('Loading');
  });

  it('loads stored tokens on initialization', async () => {
    const mockTokens = {
      access_token: 'stored-access-token',
      refresh_token: 'stored-refresh-token'
    };
    
    mockLocalStorage.getItem.mockImplementation((key) => {
      if (key === 'access_token') return mockTokens.access_token;
      if (key === 'refresh_token') return mockTokens.refresh_token;
      return null;
    });
    
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: () => Promise.resolve({
        success: true,
        data: {
          id: '1',
          email: '<EMAIL>',
          full_name: 'Admin User',
          role: 'ADMIN'
        }
      })
    });
    
    renderWithProvider(<TestComponent />);
    
    await waitFor(() => {
      expect(screen.getByTestId('user')).toHaveTextContent('<EMAIL>');
      expect(screen.getByTestId('tokens')).toHaveTextContent('Has tokens');
      expect(screen.getByTestId('loading')).toHaveTextContent('Not loading');
    });
  });

  it('handles successful login', async () => {
    const mockAuthService = await import('@/services/auth');
    
    const mockTokens = {
      access_token: 'new-access-token',
      refresh_token: 'new-refresh-token'
    };
    
    const mockUser = {
      id: '1',
      email: '<EMAIL>',
      full_name: 'Test User',
      role: 'ADMIN'
    };
    
    mockAuthService.authService.login.mockResolvedValueOnce({
      user: mockUser,
      tokens: mockTokens
    });
    
    renderWithProvider(<TestComponent />);
    
    const loginButton = screen.getByText('Login');
    fireEvent.click(loginButton);
    
    await waitFor(() => {
      expect(screen.getByTestId('user')).toHaveTextContent('<EMAIL>');
      expect(screen.getByTestId('tokens')).toHaveTextContent('Has tokens');
    });
    
    expect(mockLocalStorage.setItem).toHaveBeenCalledWith('access_token', mockTokens.access_token);
    expect(mockLocalStorage.setItem).toHaveBeenCalledWith('refresh_token', mockTokens.refresh_token);
  });

  it('handles login error', async () => {
    const mockAuthService = await import('@/services/auth');
    
    mockAuthService.authService.login.mockRejectedValueOnce(new Error('Invalid credentials'));
    
    renderWithProvider(<TestComponent />);
    
    const loginButton = screen.getByText('Login');
    fireEvent.click(loginButton);
    
    await waitFor(() => {
      expect(screen.getByTestId('user')).toHaveTextContent('No user');
      expect(screen.getByTestId('tokens')).toHaveTextContent('No tokens');
    });
  });

  it('handles logout correctly', async () => {
    const mockAuthService = await import('@/services/auth');
    
    // Configurar estado inicial con usuario logueado
    const mockTokens = {
      access_token: 'access-token',
      refresh_token: 'refresh-token'
    };
    
    const mockUser = {
      id: '1',
      email: '<EMAIL>',
      full_name: 'Test User',
      role: 'ADMIN'
    };
    
    mockAuthService.authService.login.mockResolvedValueOnce({
      user: mockUser,
      tokens: mockTokens
    });
    
    renderWithProvider(<TestComponent />);
    
    // Hacer login primero
    const loginButton = screen.getByText('Login');
    fireEvent.click(loginButton);
    
    await waitFor(() => {
      expect(screen.getByTestId('user')).toHaveTextContent('<EMAIL>');
    });
    
    // Hacer logout
    const logoutButton = screen.getByText('Logout');
    fireEvent.click(logoutButton);
    
    await waitFor(() => {
      expect(screen.getByTestId('user')).toHaveTextContent('No user');
      expect(screen.getByTestId('tokens')).toHaveTextContent('No tokens');
    });
    
    expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('access_token');
    expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('refresh_token');
  });

  it('handles token refresh', async () => {
    const mockAuthService = await import('@/services/auth');
    
    const newTokens = {
      access_token: 'new-access-token',
      refresh_token: 'new-refresh-token'
    };
    
    mockAuthService.authService.refreshToken.mockResolvedValueOnce(newTokens);
    
    renderWithProvider(<TestComponent />);
    
    const refreshButton = screen.getByText('Refresh');
    fireEvent.click(refreshButton);
    
    await waitFor(() => {
      expect(mockAuthService.authService.refreshToken).toHaveBeenCalled();
    });
  });

  it('handles token refresh error', async () => {
    const mockAuthService = await import('@/services/auth');
    
    mockAuthService.authService.refreshToken.mockRejectedValueOnce(new Error('Refresh failed'));
    
    renderWithProvider(<TestComponent />);
    
    const refreshButton = screen.getByText('Refresh');
    fireEvent.click(refreshButton);
    
    await waitFor(() => {
      expect(screen.getByTestId('user')).toHaveTextContent('No user');
      expect(screen.getByTestId('tokens')).toHaveTextContent('No tokens');
    });
  });

  it('validates user profile on initialization', async () => {
    mockLocalStorage.getItem.mockImplementation((key) => {
      if (key === 'access_token') return 'stored-token';
      if (key === 'refresh_token') return 'stored-refresh';
      return null;
    });
    
    mockFetch.mockResolvedValueOnce({
      ok: false,
      status: 401
    });
    
    renderWithProvider(<TestComponent />);
    
    await waitFor(() => {
      expect(screen.getByTestId('user')).toHaveTextContent('No user');
      expect(screen.getByTestId('tokens')).toHaveTextContent('No tokens');
    });
    
    expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('access_token');
    expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('refresh_token');
  });

  it('handles loading state during login', async () => {
    const mockAuthService = await import('@/services/auth');
    
    // Simular respuesta lenta
    mockAuthService.authService.login.mockImplementationOnce(() => 
      new Promise(resolve => setTimeout(() => resolve({
        user: { id: '1', email: '<EMAIL>', full_name: 'Test', role: 'ADMIN' },
        tokens: { access_token: 'token', refresh_token: 'refresh' }
      }), 100))
    );
    
    renderWithProvider(<TestComponent />);
    
    const loginButton = screen.getByText('Login');
    fireEvent.click(loginButton);
    
    // Verificar estado de carga
    expect(screen.getByTestId('loading')).toHaveTextContent('Loading');
    
    await waitFor(() => {
      expect(screen.getByTestId('loading')).toHaveTextContent('Not loading');
    });
  });

  it('provides context values to children', () => {
    const ChildComponent = () => {
      const context = useContext(AuthContext);
      
      return (
        <div>
          <div data-testid="has-login">{typeof context.login === 'function' ? 'Yes' : 'No'}</div>
          <div data-testid="has-logout">{typeof context.logout === 'function' ? 'Yes' : 'No'}</div>
          <div data-testid="has-refresh">{typeof context.refreshToken === 'function' ? 'Yes' : 'No'}</div>
        </div>
      );
    };
    
    renderWithProvider(<ChildComponent />);
    
    expect(screen.getByTestId('has-login')).toHaveTextContent('Yes');
    expect(screen.getByTestId('has-logout')).toHaveTextContent('Yes');
    expect(screen.getByTestId('has-refresh')).toHaveTextContent('Yes');
  });

  it('throws error when used outside provider', () => {
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
    
    expect(() => {
      render(<TestComponent />);
    }).toThrow('useAuth must be used within an AuthProvider');
    
    consoleSpy.mockRestore();
  });

  it('handles concurrent operations correctly', async () => {
    const mockAuthService = await import('@/services/auth');
    
    mockAuthService.authService.login.mockResolvedValue({
      user: { id: '1', email: '<EMAIL>', full_name: 'Test', role: 'ADMIN' },
      tokens: { access_token: 'token', refresh_token: 'refresh' }
    });
    
    renderWithProvider(<TestComponent />);
    
    const loginButton = screen.getByText('Login');
    
    // Hacer múltiples clicks rápidos
    fireEvent.click(loginButton);
    fireEvent.click(loginButton);
    fireEvent.click(loginButton);
    
    await waitFor(() => {
      expect(screen.getByTestId('user')).toHaveTextContent('<EMAIL>');
    });
    
    // Solo debería haber una llamada a login
    expect(mockAuthService.authService.login).toHaveBeenCalledTimes(1);
  });
});
