/**
 * 🌋 Volcano App Backend - Controlador de Alertas Volcánicas
 * CRUD completo para gestión de alertas volcánicas
 */

import { notificationService } from '@/services/notifications';
import { logAuditAction, supabaseAdmin } from '@/services/supabase';
import {
    AlertLevel,
    AuthenticatedRequest,
    CreateAlertDto,
    DEFAULT_PAGINATION,
    UpdateAlertDto
} from '@/types';
import { logDatabase, logger } from '@/utils/logger';
import { Response } from 'express';

// =====================================================
// OBTENER TODAS LAS ALERTAS
// =====================================================

/**
 * Obtener lista de alertas con paginación y filtros
 * GET /api/alerts
 */
export async function getAlerts(req: AuthenticatedRequest, res: Response) {
  try {
    const {
      page = DEFAULT_PAGINATION.page,
      limit = DEFAULT_PAGINATION.limit,
      sort_by = 'created_at',
      sort_order = 'desc',
      search,
      alert_level,
      is_active,
      is_scheduled
    } = req.query as any;

    // Validar límite máximo
    const validLimit = Math.min(parseInt(limit), DEFAULT_PAGINATION.max_limit);
    const offset = (parseInt(page) - 1) * validLimit;

    // Construir query base
    let query = supabaseAdmin
      .from('volcano_alerts')
      .select(`
        id,
        title,
        message,
        alert_level,
        volcano_name,
        volcano_lat,
        volcano_lng,
        is_active,
        is_scheduled,
        scheduled_for,
        expires_at,
        created_at,
        updated_at,
        metadata,
        admin_users!created_by(full_name, email)
      `, { count: 'exact' });

    // Aplicar filtros
    if (search) {
      query = query.or(`title.ilike.%${search}%,message.ilike.%${search}%`);
    }

    if (alert_level) {
      query = query.eq('alert_level', alert_level);
    }

    if (is_active !== undefined) {
      query = query.eq('is_active', is_active === 'true');
    }

    if (is_scheduled !== undefined) {
      query = query.eq('is_scheduled', is_scheduled === 'true');
    }

    // Aplicar ordenamiento y paginación
    query = query
      .order(sort_by, { ascending: sort_order === 'asc' })
      .range(offset, offset + validLimit - 1);

    const { data: alerts, error, count } = await query;

    if (error) {
      logDatabase('SELECT', 'volcano_alerts', undefined, req.user?.id, error);
      throw error;
    }

    const totalPages = Math.ceil((count || 0) / validLimit);

    res.json({
      success: true,
      data: alerts,
      pagination: {
        page: parseInt(page),
        limit: validLimit,
        total: count || 0,
        totalPages,
        hasNext: parseInt(page) < totalPages,
        hasPrev: parseInt(page) > 1
      },
      timestamp: new Date()
    });

  } catch (error) {
    logger.error('Get alerts error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch alerts',
      timestamp: new Date()
    });
  }
}

// =====================================================
// OBTENER ALERTA POR ID
// =====================================================

/**
 * Obtener una alerta específica por ID
 * GET /api/alerts/:id
 */
export async function getAlertById(req: AuthenticatedRequest, res: Response) {
  try {
    const { id } = req.params;

    const { data: alert, error } = await supabaseAdmin
      .from('volcano_alerts')
      .select(`
        *,
        admin_users!created_by(full_name, email)
      `)
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return res.status(404).json({
          success: false,
          error: 'Alert not found',
          timestamp: new Date()
        });
      }
      throw error;
    }

    res.json({
      success: true,
      data: alert,
      timestamp: new Date()
    });

  } catch (error) {
    logger.error('Get alert by ID error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch alert',
      timestamp: new Date()
    });
  }
}

// =====================================================
// CREAR NUEVA ALERTA
// =====================================================

/**
 * Crear una nueva alerta volcánica
 * POST /api/alerts
 */
export async function createAlert(req: AuthenticatedRequest, res: Response) {
  try {
    const alertData: CreateAlertDto = req.body;

    // Validaciones básicas
    if (!alertData.title || !alertData.message || !alertData.alert_level) {
      return res.status(400).json({
        success: false,
        error: 'Title, message, and alert_level are required',
        timestamp: new Date()
      });
    }

    // Validar nivel de alerta
    if (!Object.values(AlertLevel).includes(alertData.alert_level)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid alert level',
        timestamp: new Date()
      });
    }

    // Limpiar campos de fecha (convertir cadenas vacías a null)
    const cleanedAlertData = { ...alertData } as any;
    if (cleanedAlertData.expires_at === '' || cleanedAlertData.expires_at === undefined) {
      cleanedAlertData.expires_at = null;
    }
    if (cleanedAlertData.scheduled_for === '' || cleanedAlertData.scheduled_for === undefined) {
      cleanedAlertData.scheduled_for = null;
    }

    // Preparar datos para inserción
    const insertData = {
      ...cleanedAlertData,
      created_by: req.user?.id,
      volcano_name: alertData.volcano_name || 'Volcán Villarrica',
      volcano_lat: alertData.volcano_lat || -39.420000,
      volcano_lng: alertData.volcano_lng || -71.939167
    };

    const { data: newAlert, error } = await supabaseAdmin
      .from('volcano_alerts')
      .insert(insertData)
      .select(`
        *,
        admin_users!created_by(full_name, email)
      `)
      .single();

    if (error) {
      logDatabase('INSERT', 'volcano_alerts', undefined, req.user?.id, error);
      throw error;
    }

    // Log de auditoría
    await logAuditAction(
      req.user?.id || '',
      'CREATE',
      'volcano_alerts',
      newAlert.id,
      null,
      newAlert,
      req.ip,
      req.get('User-Agent')
    );

    logDatabase('INSERT', 'volcano_alerts', newAlert.id, req.user?.id);

    // Enviar notificación push si la alerta está activa
    if (newAlert.is_active) {
      try {
        await notificationService.sendVolcanoAlert(newAlert);
        logger.info(`Push notification sent for new alert: ${newAlert.id}`);
      } catch (notificationError) {
        logger.error('Failed to send push notification for new alert:', notificationError);
        // No fallar la creación de la alerta por un error de notificación
      }
    }

    res.status(201).json({
      success: true,
      data: newAlert,
      message: 'Alert created successfully',
      timestamp: new Date()
    });

  } catch (error) {
    logger.error('Create alert error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create alert',
      timestamp: new Date()
    });
  }
}

// =====================================================
// ACTUALIZAR ALERTA
// =====================================================

/**
 * Actualizar una alerta existente
 * PUT /api/alerts/:id
 */
export async function updateAlert(req: AuthenticatedRequest, res: Response) {
  try {
    const { id } = req.params;
    const updateData: UpdateAlertDto = req.body;

    // Obtener alerta actual para auditoría
    const { data: currentAlert, error: fetchError } = await supabaseAdmin
      .from('volcano_alerts')
      .select('*')
      .eq('id', id)
      .single();

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return res.status(404).json({
          success: false,
          error: 'Alert not found',
          timestamp: new Date()
        });
      }
      throw fetchError;
    }

    // Validar nivel de alerta si se proporciona
    if (updateData.alert_level && !Object.values(AlertLevel).includes(updateData.alert_level)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid alert level',
        timestamp: new Date()
      });
    }

    // Limpiar campos de fecha (convertir cadenas vacías a null)
    const cleanedUpdateData = { ...updateData } as any;
    if (cleanedUpdateData.expires_at === '' || cleanedUpdateData.expires_at === undefined) {
      cleanedUpdateData.expires_at = null;
    }
    if (cleanedUpdateData.scheduled_for === '' || cleanedUpdateData.scheduled_for === undefined) {
      cleanedUpdateData.scheduled_for = null;
    }

    // Actualizar alerta
    const { data: updatedAlert, error } = await supabaseAdmin
      .from('volcano_alerts')
      .update({
        ...cleanedUpdateData,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select(`
        *,
        admin_users!created_by(full_name, email)
      `)
      .single();

    if (error) {
      logDatabase('UPDATE', 'volcano_alerts', id, req.user?.id, error);
      throw error;
    }

    // Log de auditoría
    await logAuditAction(
      req.user?.id || '',
      'UPDATE',
      'volcano_alerts',
      id,
      currentAlert,
      updatedAlert,
      req.ip,
      req.get('User-Agent')
    );

    logDatabase('UPDATE', 'volcano_alerts', id, req.user?.id);

    // Enviar notificación push si la alerta está activa y hubo cambios significativos
    if (updatedAlert.is_active && (
      currentAlert.alert_level !== updatedAlert.alert_level ||
      currentAlert.message !== updatedAlert.message ||
      currentAlert.is_active !== updatedAlert.is_active
    )) {
      try {
        await notificationService.sendVolcanoAlert(updatedAlert);
        logger.info(`Push notification sent for updated alert: ${updatedAlert.id}`);
      } catch (notificationError) {
        logger.error('Failed to send push notification for updated alert:', notificationError);
        // No fallar la actualización por un error de notificación
      }
    }

    res.json({
      success: true,
      data: updatedAlert,
      message: 'Alert updated successfully',
      timestamp: new Date()
    });

  } catch (error) {
    logger.error('Update alert error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update alert',
      timestamp: new Date()
    });
  }
}

// =====================================================
// ELIMINAR ALERTA
// =====================================================

/**
 * Eliminar una alerta (soft delete)
 * DELETE /api/alerts/:id
 */
export async function deleteAlert(req: AuthenticatedRequest, res: Response) {
  try {
    const { id } = req.params;

    // Obtener alerta actual para auditoría
    const { data: currentAlert, error: fetchError } = await supabaseAdmin
      .from('volcano_alerts')
      .select('*')
      .eq('id', id)
      .single();

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return res.status(404).json({
          success: false,
          error: 'Alert not found',
          timestamp: new Date()
        });
      }
      throw fetchError;
    }

    // Soft delete (marcar como inactiva)
    const { error } = await supabaseAdmin
      .from('volcano_alerts')
      .update({ 
        is_active: false,
        updated_at: new Date().toISOString()
      })
      .eq('id', id);

    if (error) {
      logDatabase('DELETE', 'volcano_alerts', id, req.user?.id, error);
      throw error;
    }

    // Log de auditoría
    await logAuditAction(
      req.user?.id || '',
      'DELETE',
      'volcano_alerts',
      id,
      currentAlert,
      { is_active: false },
      req.ip,
      req.get('User-Agent')
    );

    logDatabase('DELETE', 'volcano_alerts', id, req.user?.id);

    res.json({
      success: true,
      message: 'Alert deleted successfully',
      timestamp: new Date()
    });

  } catch (error) {
    logger.error('Delete alert error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete alert',
      timestamp: new Date()
    });
  }
}

// =====================================================
// OBTENER ALERTAS ACTIVAS (PARA APP MÓVIL)
// =====================================================

/**
 * Obtener alertas activas para la app móvil
 * GET /api/alerts/active
 */
export async function getActiveAlerts(req: AuthenticatedRequest, res: Response) {
  try {
    const { data: alerts, error } = await supabaseAdmin
      .from('active_alerts')
      .select('*')
      .order('alert_level', { ascending: false })
      .order('created_at', { ascending: false });

    if (error) {
      throw error;
    }

    res.json({
      success: true,
      data: alerts || [],
      timestamp: new Date()
    });

  } catch (error) {
    logger.error('Get active alerts error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch active alerts',
      timestamp: new Date()
    });
  }
}

// =====================================================
// EXPORTACIONES
// =====================================================

export default {
  getAlerts,
  getAlertById,
  createAlert,
  updateAlert,
  deleteAlert,
  getActiveAlerts
};
