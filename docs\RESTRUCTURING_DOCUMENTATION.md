# 🌋 Volcano App Backend - Documentación de Reestructuración

## 📋 Resumen Ejecutivo

Este documento detalla la reestructuración completa del backend de Volcano App, migrando de una arquitectura monolítica (`simple-server.js` de 1,128 líneas) a una arquitectura modular, escalable y mantenible utilizando TypeScript, Express.js y mejores prácticas de desarrollo.

## 🎯 Objetivos Alcanzados

- ✅ **Modularización**: Separación de responsabilidades en controladores, rutas, middleware y servicios
- ✅ **TypeScript**: Código completamente tipado para mayor robustez
- ✅ **Escalabilidad**: Arquitectura preparada para crecimiento futuro
- ✅ **Mantenibilidad**: Código organizado y documentado
- ✅ **Seguridad**: Implementación de mejores prácticas de seguridad
- ✅ **Testing Ready**: Estructura preparada para testing automatizado

## 🏗️ Arquitectura Implementada

### Estructura de Directorios

```
src/
├── config/
│   └── env.ts                 # Configuración centralizada de variables de entorno
├── controllers/
│   ├── alerts.ts             # Gestión de alertas volcánicas
│   ├── audit.ts              # Logs de auditoría del sistema
│   ├── auth.ts               # Autenticación y autorización
│   ├── config.ts             # Configuración del sistema
│   ├── mobile.ts             # Endpoints para app móvil
│   └── zones.ts              # Gestión de zonas de seguridad
├── middleware/
│   ├── auth.ts               # Autenticación JWT y autorización por roles
│   ├── errorHandler.ts       # Manejo centralizado de errores
│   └── validation.ts         # Validaciones de entrada
├── routes/
│   ├── alerts.ts             # Rutas de alertas volcánicas
│   ├── audit.ts              # Rutas de auditoría
│   ├── auth.ts               # Rutas de autenticación
│   ├── config.ts             # Rutas de configuración
│   ├── index.ts              # Router principal
│   ├── mobile.ts             # Rutas para app móvil
│   └── zones.ts              # Rutas de zonas de seguridad
├── services/
│   └── supabase.ts           # Servicio de base de datos Supabase
├── types/
│   └── index.ts              # Definiciones de tipos TypeScript
├── utils/
│   └── logger.ts             # Sistema de logging con Winston
└── index.ts                  # Punto de entrada principal
```

## 🔧 Componentes Principales

### 1. Controladores (Controllers)

#### `alerts.ts` - Gestión de Alertas Volcánicas
- **Funciones**: CRUD completo de alertas
- **Características**: Paginación, filtros, validaciones, auditoría
- **Endpoints**: GET, POST, PUT, DELETE `/api/alerts`

#### `zones.ts` - Gestión de Zonas de Seguridad
- **Funciones**: CRUD de zonas con geometrías GeoJSON
- **Características**: Versionado, capacidad, información de contacto
- **Endpoints**: GET, POST, PUT, DELETE `/api/zones`

#### `audit.ts` - Sistema de Auditoría
- **Funciones**: Logs de actividad, estadísticas, actividad reciente
- **Características**: Filtros avanzados, reportes
- **Endpoints**: GET `/api/audit`, `/api/audit/stats`, `/api/audit/recent`

#### `mobile.ts` - API para App Móvil
- **Funciones**: Endpoints optimizados para móvil
- **Características**: Configuración, ubicación, verificación de zonas
- **Endpoints**: GET `/api/mobile/config`, POST `/api/mobile/location/report`

#### `config.ts` - Configuración del Sistema
- **Funciones**: Gestión de configuraciones dinámicas
- **Características**: Configuraciones públicas/privadas, reset a defaults
- **Endpoints**: GET, PUT, DELETE `/api/config`

#### `auth.ts` - Autenticación y Autorización
- **Funciones**: Login, logout, refresh tokens, cambio de contraseña
- **Características**: JWT, bcrypt, auditoría de accesos
- **Endpoints**: POST `/api/auth/login`, GET `/api/auth/me`

### 2. Middleware

#### `auth.ts` - Autenticación y Autorización
```typescript
// Funciones principales:
- authenticateToken()           // Verificación de JWT
- requireRole()                 // Autorización por roles específicos
- requireMinimumRole()          // Autorización por nivel mínimo
- optionalAuth()                // Autenticación opcional
```

#### `validation.ts` - Validaciones de Entrada
```typescript
// Validaciones implementadas:
- validateLogin                 // Validación de credenciales
- validateCreateAlert          // Validación de creación de alertas
- validateCreateZone           // Validación de creación de zonas
- validatePagination           // Validación de parámetros de paginación
```

#### `errorHandler.ts` - Manejo de Errores
```typescript
// Clases de error:
- AppError                     // Error base de aplicación
- ValidationError              // Errores de validación
- NotFoundError               // Recursos no encontrados
- UnauthorizedError           // Errores de autenticación
```

### 3. Rutas (Routes)

Cada archivo de rutas implementa:
- Validaciones específicas
- Middleware de autenticación/autorización
- Documentación de endpoints
- Manejo de errores

### 4. Servicios

#### `supabase.ts` - Servicio de Base de Datos
```typescript
// Clientes configurados:
- supabase                     // Cliente público (con RLS)
- supabaseAdmin               // Cliente administrativo (sin RLS)

// Funciones utilitarias:
- healthCheck()               // Verificación de salud
- getSystemConfig()           // Obtener configuración
- logAuditAction()           // Registrar acción de auditoría
```

### 5. Configuración

#### `env.ts` - Variables de Entorno
```typescript
// Configuraciones organizadas:
- SERVER_CONFIG               // Puerto, host, entorno
- DATABASE_CONFIG             // Supabase URLs y keys
- JWT_CONFIG                  // Secretos y expiración
- SECURITY_CONFIG             // Bcrypt, rate limiting
- CORS_CONFIG                 // Configuración CORS
```

## 🚀 API Endpoints Implementados

### Autenticación
```
POST   /api/auth/login              # Iniciar sesión
POST   /api/auth/logout             # Cerrar sesión
POST   /api/auth/refresh            # Renovar token
GET    /api/auth/me                 # Obtener perfil
PUT    /api/auth/change-password    # Cambiar contraseña
```

### Alertas Volcánicas
```
GET    /api/alerts                  # Listar alertas (paginado)
GET    /api/alerts/:id              # Obtener alerta específica
POST   /api/alerts                 # Crear nueva alerta
PUT    /api/alerts/:id              # Actualizar alerta
DELETE /api/alerts/:id              # Eliminar alerta
GET    /api/alerts/active           # Obtener alertas activas
```

### Zonas de Seguridad
```
GET    /api/zones                   # Listar zonas (paginado)
GET    /api/zones/:id               # Obtener zona específica
POST   /api/zones                  # Crear nueva zona
PUT    /api/zones/:id               # Actualizar zona
DELETE /api/zones/:id               # Eliminar zona
GET    /api/zones/active            # Obtener zonas activas
```

### Auditoría
```
GET    /api/audit                   # Listar logs de auditoría
GET    /api/audit/:id               # Obtener log específico
GET    /api/audit/stats             # Estadísticas de auditoría
GET    /api/audit/recent            # Actividad reciente
```

### App Móvil
```
GET    /api/mobile/alerts/current   # Alerta actual
GET    /api/mobile/zones/all        # Todas las zonas
POST   /api/mobile/location/report  # Reportar ubicación
POST   /api/mobile/location/check   # Verificar ubicación en zonas
GET    /api/mobile/config           # Configuración para móvil
```

### Configuración
```
GET    /api/config                  # Listar configuraciones
GET    /api/config/:key             # Obtener configuración específica
PUT    /api/config/:key             # Actualizar configuración
DELETE /api/config/:key             # Eliminar configuración
GET    /api/config/public           # Configuraciones públicas
POST   /api/config/reset            # Restablecer a defaults
```

### Sistema
```
GET    /health                      # Health check
GET    /status                      # Estado detallado del sistema
GET    /api/test                    # Información de la API
```

## 🔒 Seguridad Implementada

### Autenticación JWT
- Tokens de acceso (24h) y refresh (7d)
- Secretos configurables por entorno
- Verificación automática de usuarios activos

### Autorización por Roles
```typescript
enum UserRole {
  VIEWER = 'VIEWER',           # Solo lectura
  OPERATOR = 'OPERATOR',       # Operaciones básicas
  ADMIN = 'ADMIN'              # Acceso completo
}
```

### Validaciones
- Validación de entrada con express-validator
- Sanitización de datos
- Validación de tipos y rangos

### Rate Limiting
- 100 requests por 15 minutos por IP
- Configurable por entorno

### CORS
- Orígenes configurables
- Credenciales habilitadas para dominios autorizados

## 📊 Logging y Monitoreo

### Sistema de Logs (Winston)
```typescript
// Niveles de log:
- error                        # Errores del sistema
- warn                         # Advertencias
- info                         # Información general
- http                         # Requests HTTP
- debug                        # Información de debug
```

### Archivos de Log
- `logs/app.log` - Log general
- `logs/error.log` - Solo errores
- `logs/exceptions.log` - Excepciones no capturadas
- Rotación automática por tamaño

### Auditoría
- Todas las acciones CRUD registradas
- Información de usuario, IP, user agent
- Valores anteriores y nuevos para updates

## 🛠️ Configuración y Deployment

### Variables de Entorno Requeridas
```bash
# Servidor
NODE_ENV=development
PORT=3001
HOST=localhost

# Supabase
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# JWT
JWT_SECRET=your-jwt-secret
JWT_REFRESH_SECRET=your-refresh-secret
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Seguridad
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS
CORS_ORIGIN=http://localhost:3000,http://localhost:5173
CORS_CREDENTIALS=true
```

### Scripts de NPM
```bash
npm run dev          # Desarrollo con nodemon
npm run build        # Compilar TypeScript
npm start            # Ejecutar en producción
npm test             # Ejecutar tests
npm run lint         # Linting con ESLint
```

## 🧪 Testing

### Estructura Preparada
- Configuración de Jest
- Tipos TypeScript para testing
- Mocks de Supabase preparados

### Recomendaciones de Testing
```typescript
// Tests a implementar:
- Unit tests para controladores
- Integration tests para rutas
- Tests de middleware
- Tests de validaciones
- Tests de autenticación/autorización
```

## 📈 Métricas de Mejora

### Antes (simple-server.js)
- **Líneas de código**: 1,128 líneas en un solo archivo
- **Mantenibilidad**: Baja (código monolítico)
- **Escalabilidad**: Limitada
- **Testing**: Difícil de testear
- **Tipos**: JavaScript sin tipos

### Después (Arquitectura Modular)
- **Archivos**: 20+ archivos organizados
- **Mantenibilidad**: Alta (separación de responsabilidades)
- **Escalabilidad**: Excelente (fácil agregar funcionalidades)
- **Testing**: Preparado para testing automatizado
- **Tipos**: TypeScript completamente tipado

## 🔄 Migración Realizada

### Proceso de Migración
1. **Análisis**: Identificación de funcionalidades en simple-server.js
2. **Diseño**: Definición de arquitectura modular
3. **Implementación**: Creación de componentes modulares
4. **Migración**: Traslado de lógica a componentes apropiados
5. **Testing**: Verificación de funcionalidad
6. **Optimización**: Mejoras de rendimiento y seguridad

### Funcionalidades Migradas
- ✅ Sistema de autenticación completo
- ✅ CRUD de alertas volcánicas
- ✅ CRUD de zonas de seguridad
- ✅ Sistema de auditoría
- ✅ API para app móvil
- ✅ Gestión de configuración
- ✅ Health checks y monitoreo

## 🚀 Estado Actual

### Servidor Funcionando
```bash
# Servidor ejecutándose en:
http://localhost:3001

# Health check verificado:
curl http://localhost:3001/health
# Response: {"status":"healthy","timestamp":"...","services":{"database":true,"auth":true,"storage":true}}

# API funcionando:
curl http://localhost:3001/api/test
# Response: Información completa de endpoints disponibles
```

### Compilación Exitosa
- ✅ TypeScript compila sin errores
- ✅ Todos los módulos se resuelven correctamente
- ✅ Alias de paths configurados
- ✅ Variables de entorno validadas

## 📋 Próximos Pasos Recomendados

### Corto Plazo (1-2 semanas)
1. **Testing**: Implementar tests unitarios e integración
2. **Documentación API**: Configurar Swagger/OpenAPI
3. **Validación**: Tests de carga y rendimiento

### Medio Plazo (1 mes)
1. **Cache**: Implementar Redis para optimización
2. **Monitoreo**: Configurar métricas y alertas
3. **CI/CD**: Pipeline de deployment automatizado

### Largo Plazo (2-3 meses)
1. **Microservicios**: Evaluar separación en servicios
2. **Containerización**: Docker y Kubernetes
3. **Observabilidad**: Tracing distribuido

## 👥 Equipo y Mantenimiento

### Responsabilidades
- **Backend Developer**: Mantenimiento de controladores y servicios
- **DevOps**: Configuración de deployment y monitoreo
- **QA**: Testing automatizado y manual
- **Security**: Auditorías de seguridad regulares

### Documentación de Código
- Comentarios JSDoc en todas las funciones públicas
- README actualizado con instrucciones de setup
- Documentación de API auto-generada

---

## 📞 Contacto y Soporte

Para preguntas sobre la implementación o mejoras futuras, contactar al equipo de desarrollo.

**Fecha de Reestructuración**: Junio 2025  
**Versión**: 1.0.0  
**Estado**: ✅ Completado y Funcionando
