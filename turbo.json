{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env.*local"], "pipeline": {"build": {"dependsOn": ["^build"], "outputs": ["dist/**", "build/**", ".next/**", "!.next/cache/**"]}, "dev": {"cache": false, "persistent": true}, "test": {"dependsOn": ["^build"], "outputs": ["coverage/**"]}, "lint": {"dependsOn": ["^build"]}, "lint:fix": {"dependsOn": ["^build"]}, "type-check": {"dependsOn": ["^build"]}, "clean": {"cache": false}, "db:migrate": {"cache": false}, "db:seed": {"cache": false}}}