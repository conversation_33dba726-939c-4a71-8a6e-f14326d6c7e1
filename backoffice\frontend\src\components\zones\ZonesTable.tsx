import {
    Calendar,
    Edit,
    Plus,
    Shield,
    Trash2,
    Users
} from 'lucide-react'
import React from 'react'
import { cn } from '../../lib/utils'
import { Badge } from '../ui/badge'
import { Button } from '../ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../ui/table'

interface Zone {
  id: string
  name: string
  description: string
  zone_type: 'SAFE' | 'EMERGENCY' | 'EVACUATION' | 'DANGER'
  geometry: {
    type: string
    coordinates: number[][][]
  }
  center_lat?: number
  center_lng?: number
  capacity?: number
  contact_info: any
  facilities: any
  is_active: boolean
  version: number
  created_by?: string
  created_at: string
  updated_at: string
  metadata: any
}

interface ZonesTableProps {
  zones: Zone[]
  onCreateZone: () => void
  onEditZone: (zone: Zone) => void
  onDeleteZone: (id: string) => void
}

function getZoneTypeVariant(type: string): "default" | "secondary" | "destructive" | "outline" | "success" | "warning" | "info" {
  switch (type.toUpperCase()) {
    case 'SAFE': return 'success'
    case 'EMERGENCY': return 'info'
    case 'EVACUATION': return 'warning'
    case 'DANGER': return 'destructive'
    default: return 'default'
  }
}

function getZoneTypeIcon(type: string) {
  switch (type.toUpperCase()) {
    case 'SAFE': return '🛡️'
    case 'EMERGENCY': return '🏥'
    case 'EVACUATION': return '🚨'
    case 'DANGER': return '⚠️'
    default: return '📍'
  }
}

export function ZonesTable({ zones, onCreateZone, onEditZone, onDeleteZone }: ZonesTableProps) {
  const activeZones = zones.filter(zone => zone.is_active)
  const safeZones = zones.filter(zone => zone.zone_type === 'SAFE' && zone.is_active)
  const emergencyZones = zones.filter(zone => zone.zone_type === 'EMERGENCY' && zone.is_active)
  const dangerZones = zones.filter(zone => zone.zone_type === 'DANGER' && zone.is_active)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Gestión de Zonas de Seguridad</h2>
          <p className="text-muted-foreground mt-2">
            Administra las zonas de evacuación, refugio y peligro
          </p>
        </div>
        <Button onClick={onCreateZone} className="gap-2">
          <Plus className="h-4 w-4" />
          Nueva Zona
        </Button>
      </div>

      {/* Estadísticas rápidas */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total de Zonas</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{zones.length}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Zonas Seguras</CardTitle>
            <div className="text-lg">🛡️</div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{safeZones.length}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Zonas de Emergencia</CardTitle>
            <div className="text-lg">🏥</div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{emergencyZones.length}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Zonas de Peligro</CardTitle>
            <div className="text-lg">⚠️</div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{dangerZones.length}</div>
          </CardContent>
        </Card>
      </div>

      {/* Tabla de zonas */}
      <Card>
        <CardHeader>
          <CardTitle>Zonas Registradas ({zones.length})</CardTitle>
          <CardDescription>
            Lista completa de todas las zonas de seguridad del sistema
          </CardDescription>
        </CardHeader>
        <CardContent>
          {zones.length === 0 ? (
            <div className="text-center py-12">
              <Shield className="mx-auto h-12 w-12 text-muted-foreground" />
              <h3 className="mt-4 text-lg font-semibold">No hay zonas</h3>
              <p className="text-muted-foreground mt-2">
                Comienza creando una nueva zona de seguridad.
              </p>
              <Button onClick={onCreateZone} className="mt-4 gap-2">
                <Plus className="h-4 w-4" />
                Nueva Zona
              </Button>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Zona</TableHead>
                  <TableHead>Tipo</TableHead>
                  <TableHead>Capacidad</TableHead>
                  <TableHead>Estado</TableHead>
                  <TableHead>Versión</TableHead>
                  <TableHead>Fecha</TableHead>
                  <TableHead className="text-right">Acciones</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {zones.map((zone) => (
                  <TableRow key={zone.id}>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          <span className="text-lg">{getZoneTypeIcon(zone.zone_type)}</span>
                          <div>
                            <p className="font-medium">{zone.name}</p>
                            <p className="text-sm text-muted-foreground line-clamp-1">
                              {zone.description}
                            </p>
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={getZoneTypeVariant(zone.zone_type)}>
                        {zone.zone_type}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <Users className="h-3 w-3 text-muted-foreground" />
                        <span className="text-sm">
                          {zone.capacity ? `${zone.capacity} personas` : 'No especificada'}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <div className={cn(
                          "h-2 w-2 rounded-full",
                          zone.is_active ? "bg-green-500" : "bg-gray-400"
                        )} />
                        <span className="text-sm">
                          {zone.is_active ? 'Activa' : 'Inactiva'}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">v{zone.version}</Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <Calendar className="h-3 w-3 text-muted-foreground" />
                        <span className="text-sm">
                          {new Date(zone.created_at).toLocaleDateString()}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex items-center justify-end gap-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => onEditZone(zone)}
                          className="h-8 w-8"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => onDeleteZone(zone.id)}
                          className="h-8 w-8 text-destructive hover:text-destructive"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
