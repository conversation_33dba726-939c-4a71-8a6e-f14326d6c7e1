# 🛠️ Guía de Configuración para Desarrollo

## 🎯 Setup Rápido para Nuevos Desarrolladores

### 1. Configuración Inicial

```bash
# 1. Clonar el repositorio
git clone <repo-url>
cd volcanoApp

# 2. Instalar dependencias del backend
cd backoffice/backend
npm install

# 3. Instalar dependencias del mobile
cd ../../
npm install

# 4. Configurar variables de entorno
cp backoffice/backend/.env.example backoffice/backend/.env
```

### 2. Obtener tu IP Local

**Script automático** (Windows):
```bash
# Crear script get-ip.bat
@echo off
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr "IPv4"') do (
    for /f "tokens=1" %%b in ("%%a") do (
        echo Tu IP local es: %%b
        echo Actualiza el fallback en services/api.ts con esta IP
    )
)
```

**Script automático** (macOS/Linux):
```bash
#!/bin/bash
# Crear script get-ip.sh
IP=$(ifconfig | grep "inet " | grep -v 127.0.0.1 | awk '{print $2}' | head -1)
echo "Tu IP local es: $IP"
echo "Actualiza el fallback en services/api.ts con esta IP"
```

### 3. Configuración del Backend

**Archivo**: `backoffice/backend/.env`
```bash
# Configuración para desarrollo móvil
NODE_ENV=development
PORT=3002
HOST=0.0.0.0

# Base de datos
DATABASE_URL=your_supabase_url
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_key

# CORS para desarrollo (incluir tu IP local)
CORS_ORIGIN=http://localhost:5173,http://localhost:3000,http://localhost:8081,http://YOUR_IP:8081
```

### 4. Scripts de Desarrollo

**Archivo**: `package.json` (raíz del proyecto)
```json
{
  "scripts": {
    "dev:backend": "cd backoffice/backend && npm run dev",
    "dev:mobile": "npx expo start",
    "dev:web": "npx expo start --web",
    "dev:all": "concurrently \"npm run dev:backend\" \"npm run dev:mobile\"",
    "check:network": "node scripts/check-network.js",
    "setup:dev": "node scripts/setup-development.js"
  }
}
```

---

## 🔧 Scripts de Automatización

### Script de Verificación de Red

**Archivo**: `scripts/check-network.js`
```javascript
const { exec } = require('child_process');
const os = require('os');

function getLocalIP() {
  const interfaces = os.networkInterfaces();
  for (const name of Object.keys(interfaces)) {
    for (const interface of interfaces[name]) {
      if (interface.family === 'IPv4' && !interface.internal) {
        return interface.address;
      }
    }
  }
  return 'No se pudo obtener la IP';
}

function checkBackendConnectivity(ip) {
  return new Promise((resolve) => {
    exec(`curl -s http://${ip}:3002/health`, (error, stdout) => {
      resolve(!error && stdout.includes('ok'));
    });
  });
}

async function main() {
  const localIP = getLocalIP();
  console.log(`🔍 IP Local detectada: ${localIP}`);
  
  console.log('🧪 Verificando conectividad del backend...');
  
  // Verificar localhost
  const localhostOk = await checkBackendConnectivity('localhost');
  console.log(`📍 localhost:3002 - ${localhostOk ? '✅ OK' : '❌ FAIL'}`);
  
  // Verificar IP de red
  const networkOk = await checkBackendConnectivity(localIP);
  console.log(`🌐 ${localIP}:3002 - ${networkOk ? '✅ OK' : '❌ FAIL'}`);
  
  if (!networkOk) {
    console.log('\n⚠️  PROBLEMA DETECTADO:');
    console.log('El backend no es accesible desde la IP de red.');
    console.log('Soluciones:');
    console.log('1. Verificar que HOST=0.0.0.0 en .env');
    console.log('2. Reiniciar el backend');
    console.log('3. Verificar firewall');
  } else {
    console.log('\n✅ Todo configurado correctamente!');
  }
}

main().catch(console.error);
```

### Script de Setup Automático

**Archivo**: `scripts/setup-development.js`
```javascript
const fs = require('fs');
const path = require('path');
const os = require('os');

function getLocalIP() {
  const interfaces = os.networkInterfaces();
  for (const name of Object.keys(interfaces)) {
    for (const interface of interfaces[name]) {
      if (interface.family === 'IPv4' && !interface.internal) {
        return interface.address;
      }
    }
  }
  return '*************'; // Fallback
}

function updateApiConfig(ip) {
  const apiPath = path.join(__dirname, '../services/api.ts');
  let content = fs.readFileSync(apiPath, 'utf8');
  
  // Actualizar IP en el fallback
  content = content.replace(
    /return 'http:\/\/\d+\.\d+\.\d+\.\d+:3002\/api';/,
    `return 'http://${ip}:3002/api';`
  );
  
  fs.writeFileSync(apiPath, content);
  console.log(`✅ API config actualizada con IP: ${ip}`);
}

function updateWebSocketConfig(ip) {
  const wsPath = path.join(__dirname, '../services/websocket.ts');
  let content = fs.readFileSync(wsPath, 'utf8');
  
  // Actualizar IP en el fallback
  content = content.replace(
    /return 'http:\/\/\d+\.\d+\.\d+\.\d+:3002';/,
    `return 'http://${ip}:3002';`
  );
  
  fs.writeFileSync(wsPath, content);
  console.log(`✅ WebSocket config actualizada con IP: ${ip}`);
}

function updateBackendEnv(ip) {
  const envPath = path.join(__dirname, '../backoffice/backend/.env');
  let content = fs.readFileSync(envPath, 'utf8');
  
  // Actualizar CORS_ORIGIN
  const corsOrigins = [
    'http://localhost:5173',
    'http://localhost:3000',
    'http://localhost:8081',
    `http://${ip}:8081`
  ].join(',');
  
  content = content.replace(
    /CORS_ORIGIN=.*/,
    `CORS_ORIGIN=${corsOrigins}`
  );
  
  fs.writeFileSync(envPath, content);
  console.log(`✅ Backend CORS actualizado con IP: ${ip}`);
}

function main() {
  console.log('🚀 Configurando desarrollo automáticamente...');
  
  const localIP = getLocalIP();
  console.log(`🔍 IP Local detectada: ${localIP}`);
  
  try {
    updateApiConfig(localIP);
    updateWebSocketConfig(localIP);
    updateBackendEnv(localIP);
    
    console.log('\n✅ Configuración completada!');
    console.log('\n📋 Próximos pasos:');
    console.log('1. npm run dev:backend');
    console.log('2. npm run dev:mobile');
    console.log('3. Probar en dispositivo móvil');
    
  } catch (error) {
    console.error('❌ Error durante la configuración:', error.message);
  }
}

main();
```

---

## 🧪 Testing y Validación

### Test de Conectividad Completo

**Archivo**: `scripts/test-connectivity.js`
```javascript
const axios = require('axios');

async function testEndpoint(url, name) {
  try {
    const response = await axios.get(url, { timeout: 5000 });
    console.log(`✅ ${name}: ${response.status} - ${response.data.status || 'OK'}`);
    return true;
  } catch (error) {
    console.log(`❌ ${name}: ${error.message}`);
    return false;
  }
}

async function main() {
  const localIP = '************'; // Cambiar por tu IP
  
  console.log('🧪 Testing API Connectivity...\n');
  
  const tests = [
    [`http://localhost:3002/health`, 'Backend (localhost)'],
    [`http://${localIP}:3002/health`, 'Backend (network)'],
    [`http://localhost:3002/api/mobile/health`, 'Mobile API (localhost)'],
    [`http://${localIP}:3002/api/mobile/health`, 'Mobile API (network)'],
    [`http://localhost:3002/api/mobile/config`, 'Config API (localhost)'],
    [`http://${localIP}:3002/api/mobile/config`, 'Config API (network)'],
  ];
  
  let passed = 0;
  for (const [url, name] of tests) {
    if (await testEndpoint(url, name)) {
      passed++;
    }
  }
  
  console.log(`\n📊 Resultados: ${passed}/${tests.length} tests pasaron`);
  
  if (passed === tests.length) {
    console.log('🎉 ¡Todas las conexiones funcionan correctamente!');
  } else {
    console.log('⚠️  Algunos tests fallaron. Revisar configuración.');
  }
}

main().catch(console.error);
```

---

## 📱 Guía de Testing en Dispositivos

### 1. Testing en Expo Go

```bash
# 1. Iniciar el backend
npm run dev:backend

# 2. Iniciar Expo
npm run dev:mobile

# 3. Escanear QR con Expo Go
# 4. Verificar logs en la consola
```

### 2. Testing en Simulador

```bash
# iOS Simulator
npm run dev:mobile
# Presionar 'i' para abrir iOS simulator

# Android Emulator
npm run dev:mobile
# Presionar 'a' para abrir Android emulator
```

### 3. Debugging de Red

```javascript
// Agregar en App.js para debugging
console.log('🔧 Network Debug Info:', {
  platform: Platform.OS,
  hostUri: Constants.expoConfig?.hostUri,
  apiBaseURL: 'TU_API_BASE_URL',
  wsURL: 'TU_WEBSOCKET_URL'
});
```

---

## 🚨 Problemas Comunes y Soluciones

| Problema | Síntoma | Solución |
|----------|---------|----------|
| Backend no accesible desde red | `curl http://IP:3002` falla | Verificar `HOST=0.0.0.0` en .env |
| CORS errors en móvil | Network errors en device | Agregar IP a CORS_ORIGIN |
| WebSocket timeout | WS connection fails | Usar misma IP que API |
| Funciona en web, falla en móvil | Platform-specific errors | Implementar detección de plataforma |
| IP cambia frecuentemente | Config se rompe | Usar script de auto-setup |

---

## 📋 Checklist de Deployment

### Pre-Producción
- [ ] Cambiar URLs hardcodeadas por variables de entorno
- [ ] Configurar CORS específico para dominios de producción
- [ ] Implementar HTTPS
- [ ] Configurar certificados SSL
- [ ] Testing en red externa

### Producción
- [ ] Variables de entorno configuradas
- [ ] DNS configurado
- [ ] Load balancer configurado
- [ ] Monitoring implementado
- [ ] Logs de producción configurados

---

**💡 Tip Final**: Siempre mantener esta documentación actualizada cuando cambies la configuración de red o agregues nuevos endpoints.
