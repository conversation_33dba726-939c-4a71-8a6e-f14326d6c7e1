# Changelog

Todos los cambios notables de este proyecto serán documentados en este archivo.

El formato está basado en [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
y este proyecto adhiere a [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.0.0] - 2025-06-01

### 🎉 Lanzamiento Inicial - Reestructuración Completa

#### Added
- **Arquitectura Modular**: Migración completa de `simple-server.js` monolítico a arquitectura modular
- **TypeScript**: Implementación completa con tipos estrictos
- **Controladores**:
  - `alerts.ts` - CRUD completo de alertas volcánicas
  - `zones.ts` - Gestión de zonas de seguridad con geometrías GeoJSON
  - `audit.ts` - Sistema de auditoría con estadísticas
  - `mobile.ts` - API optimizada para aplicación móvil
  - `config.ts` - Gestión dinámica de configuraciones
  - `auth.ts` - Autenticación y autorización completa
- **Middleware**:
  - `auth.ts` - Autenticación JWT y autorización por roles
  - `validation.ts` - Validaciones robustas con express-validator
  - `errorHandler.ts` - Manejo centralizado de errores
- **Rutas Organizadas**:
  - Router principal con documentación automática
  - Rutas específicas para cada módulo
  - Endpoints de información y health checks
- **Configuración Centralizada**:
  - `env.ts` - Gestión tipada de variables de entorno
  - Validación de configuración requerida
  - Configuraciones por entorno
- **Sistema de Logging**:
  - Winston con rotación de archivos
  - Logs estructurados en JSON
  - Diferentes niveles de logging
- **Seguridad**:
  - JWT con tokens de acceso y refresh
  - Bcrypt para hash de contraseñas
  - Rate limiting configurable
  - CORS configurado
  - Validación y sanitización de entrada

#### API Endpoints Implementados

##### Autenticación
- `POST /api/auth/login` - Iniciar sesión
- `POST /api/auth/logout` - Cerrar sesión
- `POST /api/auth/refresh` - Renovar token
- `GET /api/auth/me` - Obtener perfil de usuario
- `PUT /api/auth/change-password` - Cambiar contraseña

##### Alertas Volcánicas
- `GET /api/alerts` - Listar alertas con paginación y filtros
- `GET /api/alerts/:id` - Obtener alerta específica
- `POST /api/alerts` - Crear nueva alerta
- `PUT /api/alerts/:id` - Actualizar alerta existente
- `DELETE /api/alerts/:id` - Eliminar alerta (soft delete)
- `GET /api/alerts/active` - Obtener alertas activas

##### Zonas de Seguridad
- `GET /api/zones` - Listar zonas con paginación y filtros
- `GET /api/zones/:id` - Obtener zona específica
- `POST /api/zones` - Crear nueva zona
- `PUT /api/zones/:id` - Actualizar zona existente
- `DELETE /api/zones/:id` - Eliminar zona (soft delete)
- `GET /api/zones/active` - Obtener zonas activas

##### Auditoría
- `GET /api/audit` - Listar logs de auditoría
- `GET /api/audit/:id` - Obtener log específico
- `GET /api/audit/stats` - Estadísticas de auditoría
- `GET /api/audit/recent` - Actividad reciente

##### App Móvil
- `GET /api/mobile/alerts/current` - Alerta volcánica actual
- `GET /api/mobile/zones/all` - Todas las zonas de seguridad
- `POST /api/mobile/location/report` - Reportar ubicación de usuario
- `POST /api/mobile/location/check` - Verificar ubicación en zonas
- `GET /api/mobile/config` - Configuración para app móvil

##### Configuración del Sistema
- `GET /api/config` - Listar todas las configuraciones
- `GET /api/config/:key` - Obtener configuración específica
- `PUT /api/config/:key` - Actualizar configuración
- `DELETE /api/config/:key` - Eliminar configuración
- `GET /api/config/public` - Configuraciones públicas
- `POST /api/config/reset` - Restablecer a valores por defecto

##### Sistema
- `GET /health` - Health check del sistema
- `GET /status` - Estado detallado del servidor
- `GET /api/test` - Información de la API y endpoints

#### Features Principales

##### Autenticación y Autorización
- Sistema de roles: VIEWER, OPERATOR, ADMIN
- JWT con expiración configurable
- Refresh tokens para sesiones largas
- Middleware de autorización por roles y niveles
- Auditoría completa de accesos

##### Gestión de Alertas
- Niveles de alerta: NORMAL, ADVISORY, WATCH, WARNING, EMERGENCY
- Programación de alertas futuras
- Expiración automática
- Metadatos extensibles
- Versionado de alertas

##### Zonas de Seguridad
- Geometrías GeoJSON para definir áreas
- Tipos de zona: SAFE, EMERGENCY, DANGER, EVACUATION, RESTRICTED
- Información de capacidad y contacto
- Facilidades disponibles
- Versionado automático

##### Sistema de Auditoría
- Registro automático de todas las operaciones CRUD
- Información de usuario, IP, user agent
- Valores anteriores y nuevos para updates
- Estadísticas y reportes
- Filtros avanzados

##### API Móvil
- Endpoints optimizados para aplicaciones móviles
- Verificación de ubicación en zonas de seguridad
- Configuración dinámica
- Tracking anónimo de ubicaciones
- Recomendaciones de seguridad

##### Configuración Dinámica
- Configuraciones modificables en tiempo real
- Configuraciones públicas vs privadas
- Reset a valores por defecto
- Validación de tipos
- Historial de cambios

#### Technical Improvements

##### Arquitectura
- Separación clara de responsabilidades
- Patrón MVC implementado
- Inyección de dependencias
- Código modular y reutilizable

##### TypeScript
- Tipos estrictos en todo el código
- Interfaces bien definidas
- Enums para constantes
- Generics para reutilización

##### Validación
- Validación de entrada robusta
- Sanitización automática
- Mensajes de error descriptivos
- Validación de tipos y rangos

##### Error Handling
- Manejo centralizado de errores
- Clases de error específicas
- Logging automático de errores
- Respuestas consistentes

##### Logging
- Logs estructurados en JSON
- Rotación automática de archivos
- Diferentes niveles de log
- Correlación de requests

##### Performance
- Paginación eficiente
- Índices de base de datos optimizados
- Caching de configuraciones
- Queries optimizadas

#### Database Schema

##### Tablas Principales
- `admin_users` - Usuarios del sistema administrativo
- `volcano_alerts` - Alertas volcánicas
- `safety_zones` - Zonas de seguridad
- `audit_logs` - Logs de auditoría
- `system_config` - Configuración del sistema
- `app_user_locations` - Ubicaciones de usuarios móviles

##### Características
- Row Level Security (RLS) implementado
- Triggers para auditoría automática
- Índices optimizados para consultas
- Constraints para integridad de datos

#### Configuration

##### Environment Variables
- Configuración completa por entorno
- Validación de variables requeridas
- Valores por defecto seguros
- Documentación completa

##### Security
- Secretos JWT configurables
- Rate limiting ajustable
- CORS configurable por entorno
- Bcrypt rounds configurables

#### Documentation

##### Code Documentation
- JSDoc en todas las funciones públicas
- Comentarios explicativos
- Tipos TypeScript como documentación
- README completo

##### API Documentation
- Endpoints documentados
- Ejemplos de uso
- Códigos de respuesta
- Esquemas de datos

#### Testing Ready

##### Structure
- Configuración de Jest
- Estructura de tests preparada
- Mocks de servicios externos
- Utilities para testing

##### Coverage
- Tests unitarios preparados
- Tests de integración estructurados
- Tests de middleware
- Tests de validaciones

### Changed
- **Arquitectura**: Migración completa de monolito a modular
- **Lenguaje**: JavaScript → TypeScript
- **Estructura**: Archivo único → Múltiples módulos organizados
- **Configuración**: Variables hardcodeadas → Configuración centralizada
- **Logging**: Console.log → Winston estructurado
- **Validación**: Validación manual → express-validator
- **Errores**: Manejo ad-hoc → Sistema centralizado

### Deprecated
- `simple-server.js` - Reemplazado por arquitectura modular

### Removed
- Código duplicado en el archivo monolítico
- Validaciones inconsistentes
- Logging no estructurado
- Configuración hardcodeada

### Fixed
- Manejo inconsistente de errores
- Validaciones faltantes
- Logging inadecuado
- Configuración no centralizada
- Código no tipado

### Security
- Implementación de JWT con refresh tokens
- Bcrypt para hash de contraseñas
- Rate limiting para prevenir ataques
- Validación y sanitización de entrada
- CORS configurado correctamente
- Auditoría completa de acciones

---

## [0.1.0] - 2025-05-31

### Added
- Archivo monolítico `simple-server.js` inicial
- Funcionalidades básicas de API
- Conexión a Supabase
- Endpoints básicos de alertas y zonas

### Notes
- Versión inicial monolítica antes de la reestructuración
- Base funcional para la migración a arquitectura modular

---

## Tipos de Cambios

- `Added` para nuevas funcionalidades
- `Changed` para cambios en funcionalidades existentes
- `Deprecated` para funcionalidades que serán removidas
- `Removed` para funcionalidades removidas
- `Fixed` para corrección de bugs
- `Security` para mejoras de seguridad
