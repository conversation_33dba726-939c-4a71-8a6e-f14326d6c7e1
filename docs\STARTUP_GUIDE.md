# 🚀 Guía de Inicio - Volcano App

Esta guía te ayudará a arrancar los 3 proyectos principales de Volcano App de manera eficiente.

## 📋 **Estructura del Proyecto**

El proyecto Volcano App está compuesto por 3 aplicaciones principales:

1. **📱 App Móvil** (React Native + Expo) - Ubicada en la raíz del proyecto
2. **🔧 Backend API** (Node.js + Express + TypeScript) - `backoffice/backend/`
3. **💻 Frontend Admin** (React + Vite + TypeScript) - `backoffice/frontend/`

---

## **🏗️ Arquitectura del Sistema**

```
volcano-app/
├── 📱 App Móvil (React Native + Expo)
│   ├── app/                 # Rutas de la aplicación
│   ├── components/          # Componentes reutilizables
│   ├── services/           # Servicios API
│   └── package.json        # Dependencias móvil
├── backoffice/
│   ├── 🔧 backend/         # API Backend
│   │   ├── src/            # Código fuente
│   │   ├── dist/           # Código compilado
│   │   └── package.json    # Dependencias backend
│   └── 💻 frontend/        # Panel Admin
│       ├── src/            # Código fuente
│       ├── dist/           # Build de producción
│       └── package.json    # Dependencias frontend
└── docs/                   # Documentación
```

---

## **🚀 Comandos de Inicio Rápido**

### **Opción 1: Arrancar Todo desde la Raíz**
```bash
# App Móvil
npm run dev

# Backend (desde raíz)
npm run dev:backend

# Frontend (navegar al directorio)
cd backoffice/frontend && npm run dev
```

### **Opción 2: Arrancar Cada Proyecto Individualmente**

#### **📱 App Móvil (React Native + Expo)**
```bash
# Desde la raíz del proyecto
npm install                 # Solo la primera vez
npm run dev                 # Modo desarrollo
# o
npm start                   # Alternativo
expo start                  # Directo con Expo

# Para plataformas específicas:
npm run android            # Android
npm run ios                # iOS  
npm run web                # Web
```

#### **🔧 Backend API (Node.js + Express)**
```bash
# Opción A: Desde la raíz
npm run dev:backend

# Opción B: Navegar al directorio
cd backoffice/backend
npm install                # Solo la primera vez
npm run dev                # Modo desarrollo
npm run build              # Build para producción
npm start                  # Ejecutar build
```

#### **💻 Frontend Admin (React + Vite)**
```bash
cd backoffice/frontend
npm install                # Solo la primera vez
npm run dev                # Modo desarrollo
npm run build              # Build para producción
npm run preview            # Preview del build
```

---

## **🌐 URLs de Acceso**

Una vez que arranques los proyectos, podrás acceder a:

| Aplicación | URL | Puerto | Descripción |
|------------|-----|--------|-------------|
| 📱 **App Móvil** | `http://localhost:8081` | 8081 | Expo DevTools |
| 🔧 **Backend API** | `http://localhost:3001` | 3001 | API REST |
| 💻 **Frontend Admin** | `http://localhost:5173` | 5173 | Panel Administrativo |

### **Endpoints Importantes del Backend:**
- `GET /health` - Estado del servidor
- `GET /api/alerts` - Alertas volcánicas
- `GET /api/zones` - Zonas de seguridad
- `POST /api/auth/login` - Autenticación

---

## **⚡ Flujo de Desarrollo Recomendado**

### **1. Orden de Arranque:**
```bash
# 1. Backend primero (para que las APIs estén disponibles)
npm run dev:backend

# 2. Frontend Admin (para gestionar datos)
cd backoffice/frontend && npm run dev

# 3. App Móvil (consume las APIs)
npm run dev
```

### **2. Desarrollo en Paralelo:**
```bash
# Terminal 1 - Backend
npm run dev:backend

# Terminal 2 - Frontend Admin  
cd backoffice/frontend && npm run dev

# Terminal 3 - App Móvil
npm run dev
```

---

## **🔧 Configuración Inicial**

### **Primera Vez - Instalar Dependencias:**
```bash
# Raíz (App Móvil)
npm install

# Backend
cd backoffice/backend && npm install

# Frontend
cd ../frontend && npm install
```

### **Variables de Entorno:**
Cada proyecto necesita su archivo `.env`:

#### **Backend** (`backoffice/backend/.env`):
```env
PORT=3001
SUPABASE_URL=tu_supabase_url
SUPABASE_ANON_KEY=tu_supabase_key
JWT_SECRET=tu_jwt_secret
NODE_ENV=development
```

#### **Frontend** (`backoffice/frontend/.env`):
```env
VITE_API_URL=http://localhost:3001
VITE_SUPABASE_URL=tu_supabase_url
VITE_SUPABASE_ANON_KEY=tu_supabase_key
```

#### **App Móvil** (`.env` en raíz):
```env
EXPO_PUBLIC_API_URL=http://localhost:3001
EXPO_PUBLIC_SUPABASE_URL=tu_supabase_url
EXPO_PUBLIC_SUPABASE_ANON_KEY=tu_supabase_key
```

---

## **🛠️ Scripts Disponibles**

### **App Móvil (Raíz):**
```json
{
  "dev": "expo start",
  "start": "expo start", 
  "android": "expo start --android",
  "ios": "expo start --ios",
  "web": "expo start --web",
  "dev:backend": "cd backoffice/backend && npm run dev",
  "reset-project": "node ./scripts/reset-project.js"
}
```

### **Backend:**
```json
{
  "dev": "nodemon --exec ts-node src/index.ts",
  "build": "tsc",
  "start": "node dist/index.js",
  "test": "jest",
  "lint": "eslint src/**/*.ts"
}
```

### **Frontend:**
```json
{
  "dev": "vite",
  "build": "tsc && vite build", 
  "preview": "vite preview",
  "lint": "eslint src --ext ts,tsx"
}
```

---

## **🔍 Verificación de Estado**

### **Comprobar que Todo Funciona:**
```bash
# Backend API
curl http://localhost:3001/health

# Frontend Admin
open http://localhost:5173

# App Móvil
# Verificar en Expo DevTools: http://localhost:8081
```

### **Logs y Debugging:**
```bash
# Ver logs del backend
cd backoffice/backend && npm run dev

# Ver logs del frontend
cd backoffice/frontend && npm run dev

# Ver logs de la app móvil
npm run dev
```

---

## **🚨 Solución de Problemas Comunes**

### **Puerto Ocupado:**
```bash
# Cambiar puerto del backend
PORT=3002 npm run dev

# Cambiar puerto del frontend
npm run dev -- --port 3000
```

### **Dependencias Faltantes:**
```bash
# Limpiar e instalar
rm -rf node_modules package-lock.json
npm install
```

### **Problemas de Expo:**
```bash
# Limpiar caché de Expo
expo start -c

# Reinstalar Expo CLI
npm install -g @expo/cli
```

---

## **📱 Integración entre Proyectos**

### **Flujo de Datos:**
1. **Admin** crea/modifica alertas → **Backend API** → **Base de Datos**
2. **App Móvil** consulta → **Backend API** → Datos actualizados
3. **WebSocket** para actualizaciones en tiempo real

### **Comunicación:**
- **Frontend Admin** ↔ **Backend**: HTTP REST + WebSocket
- **App Móvil** ↔ **Backend**: HTTP REST + WebSocket
- **Backend** ↔ **Supabase**: PostgreSQL + PostGIS

---

## **📊 Monitoreo y Métricas**

### **URLs de Monitoreo:**
- Backend Health: `http://localhost:3001/health`
- API Docs: `http://localhost:3001/api-docs`
- Frontend: `http://localhost:5173`
- Expo DevTools: `http://localhost:8081`

### **Logs Importantes:**
- Backend: Logs en consola y archivo
- Frontend: DevTools del navegador
- App Móvil: Expo DevTools + React Native Debugger

---

**¡Listo para desarrollar! 🌋**

Para más información específica, consulta:
- `docs/API_DOCUMENTATION.md` - Documentación de la API
- `docs/DEVELOPMENT.md` - Guía de desarrollo
- `docs/MOBILE_INTEGRATION_GUIDE.md` - Integración móvil
