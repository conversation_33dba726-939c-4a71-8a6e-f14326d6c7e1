/**
 * 🌋 Volcano App Backend - Rutas de Alertas Volcánicas
 * Endpoints para gestión completa de alertas volcánicas
 */

import { Router } from 'express';
import { requireMinimumRole } from '@/middleware/auth';
import { 
  validateCreateAlert, 
  validateUpdateAlert, 
  validateAlertId,
  validatePagination,
  validateSearch
} from '@/middleware/validation';
import { asyncHandler } from '@/middleware/errorHandler';
import { UserRole } from '@/types';

// Importar controladores
import {
  getAlerts,
  getAlertById,
  createAlert,
  updateAlert,
  deleteAlert,
  getActiveAlerts
} from '@/controllers/alerts';

// =====================================================
// ROUTER DE ALERTAS
// =====================================================

const router = Router();

// =====================================================
// RUTAS DE CONSULTA
// =====================================================

/**
 * Obtener todas las alertas con paginación y filtros
 * GET /alerts
 */
router.get('/', 
  validatePagination,
  validateSearch,
  asyncHandler(getAlerts)
);

/**
 * Obtener alertas activas (para compatibilidad)
 * GET /alerts/active
 */
router.get('/active', 
  asyncHandler(getActiveAlerts)
);

/**
 * Obtener una alerta específica por ID
 * GET /alerts/:id
 */
router.get('/:id', 
  validateAlertId,
  asyncHandler(getAlertById)
);

// =====================================================
// RUTAS DE MODIFICACIÓN (REQUIEREN PERMISOS)
// =====================================================

/**
 * Crear nueva alerta volcánica
 * POST /alerts
 * Requiere rol mínimo: OPERATOR
 */
router.post('/', 
  requireMinimumRole(UserRole.OPERATOR),
  validateCreateAlert,
  asyncHandler(createAlert)
);

/**
 * Actualizar alerta existente
 * PUT /alerts/:id
 * Requiere rol mínimo: OPERATOR
 */
router.put('/:id', 
  requireMinimumRole(UserRole.OPERATOR),
  validateUpdateAlert,
  asyncHandler(updateAlert)
);

/**
 * Eliminar alerta (soft delete)
 * DELETE /alerts/:id
 * Requiere rol mínimo: OPERATOR
 */
router.delete('/:id', 
  requireMinimumRole(UserRole.OPERATOR),
  validateAlertId,
  asyncHandler(deleteAlert)
);

// =====================================================
// RUTAS DE INFORMACIÓN
// =====================================================

/**
 * Información sobre alertas
 * GET /alerts/_info
 */
router.get('/_info', (req, res) => {
  res.json({
    success: true,
    data: {
      endpoints: {
        list: 'GET /api/alerts',
        get: 'GET /api/alerts/:id',
        create: 'POST /api/alerts',
        update: 'PUT /api/alerts/:id',
        delete: 'DELETE /api/alerts/:id',
        active: 'GET /api/alerts/active'
      },
      alert_levels: [
        'NORMAL',
        'ADVISORY', 
        'WATCH',
        'WARNING',
        'EMERGENCY'
      ],
      permissions: {
        view: 'All authenticated users',
        create: 'OPERATOR or higher',
        update: 'OPERATOR or higher',
        delete: 'OPERATOR or higher'
      },
      pagination: {
        default_page: 1,
        default_limit: 20,
        max_limit: 100
      },
      filters: {
        search: 'Search in title and message',
        alert_level: 'Filter by alert level',
        is_active: 'Filter by active status',
        is_scheduled: 'Filter by scheduled status'
      },
      sorting: {
        fields: ['created_at', 'updated_at', 'alert_level', 'title'],
        orders: ['asc', 'desc']
      }
    },
    timestamp: new Date()
  });
});

// =====================================================
// EXPORTACIÓN
// =====================================================

export default router;
