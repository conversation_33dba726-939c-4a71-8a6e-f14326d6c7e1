/**
 * 🌋 Volcano App - Script de Prueba para Device ID
 * Script para probar el servicio de generación de device_id
 */

// Simular el entorno de React Native para testing
global.Platform = {
  OS: 'test'
};

global.Constants = {
  expoConfig: {
    version: '1.0.0'
  }
};

// Mock de AsyncStorage para testing
const mockAsyncStorage = {
  storage: {},
  getItem: function(key) {
    return Promise.resolve(this.storage[key] || null);
  },
  setItem: function(key, value) {
    this.storage[key] = value;
    return Promise.resolve();
  },
  removeItem: function(key) {
    delete this.storage[key];
    return Promise.resolve();
  }
};

// Simular el módulo de AsyncStorage
const Module = require('module');
const originalRequire = Module.prototype.require;

Module.prototype.require = function(...args) {
  if (args[0] === '@react-native-async-storage/async-storage') {
    return { default: mockAsyncStorage };
  }
  return originalRequire.apply(this, args);
};

async function testDeviceIdGeneration() {
  console.log('🧪 Probando generación de Device ID...');
  
  try {
    // Importar el servicio dinámicamente
    const deviceIdModule = require('../services/deviceId.ts');
    
    // Probar generación de device_id
    console.log('\n1. Probando getDeviceId()...');
    const deviceId1 = await deviceIdModule.getDeviceId();
    console.log('✅ Device ID generado:', deviceId1);
    console.log('✅ Longitud:', deviceId1.length);
    
    // Verificar que es válido
    const isValid = deviceIdModule.validateDeviceId(deviceId1);
    console.log('✅ Es válido:', isValid);
    
    // Probar que se mantiene el mismo ID en la segunda llamada
    console.log('\n2. Probando persistencia...');
    const deviceId2 = await deviceIdModule.getDeviceId();
    console.log('✅ Device ID recuperado:', deviceId2);
    console.log('✅ Son iguales:', deviceId1 === deviceId2);
    
    // Probar regeneración
    console.log('\n3. Probando regeneración...');
    const deviceId3 = await deviceIdModule.regenerateDeviceId();
    console.log('✅ Device ID regenerado:', deviceId3);
    console.log('✅ Es diferente:', deviceId1 !== deviceId3);
    
    // Probar limpieza
    console.log('\n4. Probando limpieza...');
    await deviceIdModule.clearDeviceId();
    const deviceId4 = await deviceIdModule.getDeviceId();
    console.log('✅ Device ID después de limpiar:', deviceId4);
    console.log('✅ Es nuevo:', deviceId3 !== deviceId4);
    
    // Probar información del dispositivo
    console.log('\n5. Probando información del dispositivo...');
    const deviceInfo = deviceIdModule.getDeviceInfo();
    console.log('✅ Info del dispositivo:', JSON.stringify(deviceInfo, null, 2));
    
    console.log('\n🎉 ¡Todas las pruebas de Device ID pasaron!');
    return true;
    
  } catch (error) {
    console.error('❌ Error en las pruebas:', error);
    return false;
  }
}

async function testValidation() {
  console.log('\n🧪 Probando validación de Device ID...');
  
  try {
    const deviceIdModule = require('../services/deviceId.ts');
    
    // Casos de prueba para validación
    const testCases = [
      { id: 'short', expected: false, description: 'ID muy corto' },
      { id: 'a'.repeat(5), expected: false, description: 'ID de 5 caracteres' },
      { id: 'a'.repeat(10), expected: true, description: 'ID de 10 caracteres (mínimo)' },
      { id: 'a'.repeat(50), expected: true, description: 'ID de 50 caracteres' },
      { id: 'a'.repeat(100), expected: true, description: 'ID de 100 caracteres (máximo)' },
      { id: 'a'.repeat(101), expected: false, description: 'ID de 101 caracteres (muy largo)' },
      { id: '', expected: false, description: 'ID vacío' },
      { id: null, expected: false, description: 'ID null' },
      { id: undefined, expected: false, description: 'ID undefined' }
    ];
    
    for (const testCase of testCases) {
      const result = deviceIdModule.validateDeviceId(testCase.id);
      const status = result === testCase.expected ? '✅' : '❌';
      console.log(`${status} ${testCase.description}: ${result} (esperado: ${testCase.expected})`);
    }
    
    console.log('\n✅ Pruebas de validación completadas');
    return true;
    
  } catch (error) {
    console.error('❌ Error en pruebas de validación:', error);
    return false;
  }
}

async function runAllTests() {
  console.log('🌋 Volcano App - Pruebas de Device ID Service');
  console.log('='.repeat(50));
  
  const test1 = await testDeviceIdGeneration();
  const test2 = await testValidation();
  
  console.log('\n' + '='.repeat(50));
  console.log('📊 Resumen de Pruebas:');
  console.log(`✅ Generación de Device ID: ${test1 ? 'PASÓ' : 'FALLÓ'}`);
  console.log(`✅ Validación de Device ID: ${test2 ? 'PASÓ' : 'FALLÓ'}`);
  
  if (test1 && test2) {
    console.log('\n🎉 ¡Todas las pruebas pasaron!');
    console.log('📱 El servicio de Device ID está funcionando correctamente.');
  } else {
    console.log('\n⚠️ Algunas pruebas fallaron. Revisar los errores arriba.');
  }
}

// Ejecutar pruebas si se llama directamente
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  testDeviceIdGeneration,
  testValidation,
  runAllTests
};
