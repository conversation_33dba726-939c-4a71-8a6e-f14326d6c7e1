import {
    Activity,
    AlertTriangle,
    BarChart3,
    Clock,
    Map as MapIcon,
    Menu,
    RefreshCw,
    Settings,
    Shield,
    Users,
    X
} from 'lucide-react'
import React, { useState } from 'react'
import { cn } from '../../lib/utils'
import { Badge } from '../ui/badge'
import { Button } from '../ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader, SidebarItem } from '../ui/sidebar'

interface AppLayoutProps {
  children: React.ReactNode
  activeTab: string
  onTabChange: (tab: string) => void
  health?: any
  loading?: boolean
  onRefresh?: () => void
}

const navigationItems = [
  { 
    key: 'dashboard', 
    label: 'Dashboard', 
    icon: Activity,
    description: 'Vista general del sistema'
  },
  { 
    key: 'alerts', 
    label: 'Alertas Volcánicas', 
    icon: AlertTriangle,
    description: 'Gestión de alertas'
  },
  { 
    key: 'zones', 
    label: 'Zonas de Seguridad', 
    icon: Shield,
    description: 'Administrar zonas'
  },
  { 
    key: 'map', 
    label: 'Mapa Interactivo', 
    icon: MapIcon,
    description: 'Visualización geográfica'
  },
  { 
    key: 'analytics', 
    label: 'Analíticas', 
    icon: BarChart3,
    description: 'Reportes y métricas'
  },
  { 
    key: 'users', 
    label: 'Usuarios', 
    icon: Users,
    description: 'Gestión de usuarios'
  },
  { 
    key: 'audit', 
    label: 'Auditoría', 
    icon: Clock,
    description: 'Logs del sistema'
  },
  { 
    key: 'settings', 
    label: 'Configuración', 
    icon: Settings,
    description: 'Ajustes del sistema'
  }
]

export function AppLayout({ 
  children, 
  activeTab, 
  onTabChange, 
  health, 
  loading, 
  onRefresh 
}: AppLayoutProps) {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)

  return (
    <div className="flex h-screen bg-background">
      {/* Sidebar */}
      <div className={cn(
        "transition-all duration-300 ease-in-out",
        sidebarCollapsed ? "w-16" : "w-64"
      )}>
        <Sidebar className={cn(
          "transition-all duration-300",
          sidebarCollapsed ? "w-16" : "w-64"
        )}>
          <SidebarHeader className="justify-between">
            {!sidebarCollapsed && (
              <div className="flex items-center gap-2">
                <span className="text-2xl">🌋</span>
                <div>
                  <h1 className="text-lg font-bold">Volcano App</h1>
                  <p className="text-xs text-muted-foreground">Panel Admin</p>
                </div>
              </div>
            )}
            {sidebarCollapsed && (
              <span className="text-2xl mx-auto">🌋</span>
            )}
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
              className="h-8 w-8"
            >
              {sidebarCollapsed ? <Menu className="h-4 w-4" /> : <X className="h-4 w-4" />}
            </Button>
          </SidebarHeader>

          <SidebarContent>
            <div className="space-y-1">
              {navigationItems.map((item) => {
                const Icon = item.icon
                return (
                  <SidebarItem
                    key={item.key}
                    active={activeTab === item.key}
                    icon={<Icon className="h-4 w-4" />}
                    onClick={() => onTabChange(item.key)}
                    className="group"
                  >
                    {!sidebarCollapsed && (
                      <div className="flex-1">
                        <div className="font-medium">{item.label}</div>
                        <div className="text-xs text-muted-foreground group-hover:text-accent-foreground">
                          {item.description}
                        </div>
                      </div>
                    )}
                  </SidebarItem>
                )
              })}
            </div>
          </SidebarContent>

          <SidebarFooter>
            {!sidebarCollapsed && (
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm">Estado del Sistema</CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="flex items-center gap-2">
                    <div className={cn(
                      "h-2 w-2 rounded-full",
                      health?.status === 'healthy' ? "bg-green-500" : "bg-red-500"
                    )} />
                    <span className="text-xs">
                      {health?.status === 'healthy' ? 'Operativo' : 'Con problemas'}
                    </span>
                  </div>
                  {health?.services && (
                    <div className="mt-2 space-y-1">
                      <div className="flex justify-between text-xs">
                        <span>Base de datos</span>
                        <Badge variant={health.services.database ? "success" : "destructive"} className="h-4 text-xs">
                          {health.services.database ? "OK" : "Error"}
                        </Badge>
                      </div>
                      <div className="flex justify-between text-xs">
                        <span>Servidor</span>
                        <Badge variant={health.services.server ? "success" : "destructive"} className="h-4 text-xs">
                          {health.services.server ? "OK" : "Error"}
                        </Badge>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}
            {sidebarCollapsed && health && (
              <div className="flex justify-center">
                <div className={cn(
                  "h-3 w-3 rounded-full",
                  health.status === 'healthy' ? "bg-green-500" : "bg-red-500"
                )} />
              </div>
            )}
          </SidebarFooter>
        </Sidebar>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <div className="flex h-16 items-center justify-between px-6">
            <div className="flex items-center gap-4">
              <h2 className="text-xl font-semibold">
                {navigationItems.find(item => item.key === activeTab)?.label || 'Dashboard'}
              </h2>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={onRefresh}
                disabled={loading}
                className="gap-2"
              >
                <RefreshCw className={cn("h-4 w-4", loading && "animate-spin")} />
                Actualizar
              </Button>
            </div>
          </div>
        </header>

        {/* Page Content */}
        <main className="flex-1 overflow-auto p-6">
          {children}
        </main>
      </div>
    </div>
  )
}
