/**
 * 🌋 Volcano App Backend - Sistema de Logging
 * Configuración de Winston para logging estructurado
 */

import winston from 'winston';
import path from 'path';
import { CONFIG } from '@/config/env';

// =====================================================
// CONFIGURACIÓN DE LOGGING
// =====================================================

const { LOG_LEVEL, LOG_FILE_PATH, LOG_MAX_SIZE, LOG_MAX_FILES } = CONFIG.LOGGING;

// Crear directorio de logs si no existe
const logDir = path.dirname(LOG_FILE_PATH);

// =====================================================
// FORMATOS PERSONALIZADOS
// =====================================================

// Formato para desarrollo (consola)
const developmentFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.errors({ stack: true }),
  winston.format.colorize(),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    let log = `${timestamp} [${level}]: ${message}`;
    
    if (Object.keys(meta).length > 0) {
      log += `\n${JSON.stringify(meta, null, 2)}`;
    }
    
    return log;
  })
);

// Formato para producción (archivo)
const productionFormat = winston.format.combine(
  winston.format.timestamp(),
  winston.format.errors({ stack: true }),
  winston.format.json()
);

// =====================================================
// TRANSPORTS
// =====================================================

const transports: winston.transport[] = [];

// Console transport (siempre habilitado en desarrollo)
if (CONFIG.SERVER.NODE_ENV !== 'production') {
  transports.push(
    new winston.transports.Console({
      level: LOG_LEVEL,
      format: developmentFormat
    })
  );
}

// File transport (siempre habilitado)
transports.push(
  new winston.transports.File({
    filename: LOG_FILE_PATH,
    level: LOG_LEVEL,
    format: productionFormat,
    maxsize: parseInt(LOG_MAX_SIZE.replace(/[^\d]/g, '')) * 1024 * 1024, // Convert to bytes
    maxFiles: LOG_MAX_FILES,
    tailable: true
  })
);

// Error file transport (solo errores)
transports.push(
  new winston.transports.File({
    filename: path.join(logDir, 'error.log'),
    level: 'error',
    format: productionFormat,
    maxsize: parseInt(LOG_MAX_SIZE.replace(/[^\d]/g, '')) * 1024 * 1024, // Convert to bytes
    maxFiles: LOG_MAX_FILES,
    tailable: true
  })
);

// =====================================================
// LOGGER PRINCIPAL
// =====================================================

export const logger = winston.createLogger({
  level: LOG_LEVEL,
  format: productionFormat,
  transports,
  exitOnError: false,
  
  // Manejo de excepciones no capturadas
  exceptionHandlers: [
    new winston.transports.File({
      filename: path.join(logDir, 'exceptions.log'),
      format: productionFormat
    })
  ],
  
  // Manejo de promesas rechazadas
  rejectionHandlers: [
    new winston.transports.File({
      filename: path.join(logDir, 'rejections.log'),
      format: productionFormat
    })
  ]
});

// =====================================================
// UTILIDADES DE LOGGING
// =====================================================

/**
 * Log de request HTTP
 */
export function logRequest(req: any, res: any, responseTime: number) {
  const logData = {
    method: req.method,
    url: req.url,
    status: res.statusCode,
    responseTime: `${responseTime}ms`,
    userAgent: req.get('User-Agent'),
    ip: req.ip || req.connection.remoteAddress,
    userId: req.user?.id,
    requestId: req.id
  };

  if (res.statusCode >= 400) {
    logger.warn('HTTP Request Error', logData);
  } else {
    logger.info('HTTP Request', logData);
  }
}

/**
 * Log de autenticación
 */
export function logAuth(action: 'login' | 'logout' | 'token_refresh', userId: string, ip?: string, userAgent?: string) {
  logger.info('Authentication Event', {
    action,
    userId,
    ip,
    userAgent,
    timestamp: new Date().toISOString()
  });
}

/**
 * Log de operaciones de base de datos
 */
export function logDatabase(operation: string, table: string, recordId?: string, userId?: string, error?: any) {
  const logData = {
    operation,
    table,
    recordId,
    userId,
    timestamp: new Date().toISOString()
  };

  if (error) {
    logger.error('Database Operation Error', { ...logData, error });
  } else {
    logger.info('Database Operation', logData);
  }
}

/**
 * Log de seguridad
 */
export function logSecurity(event: string, details: any, severity: 'low' | 'medium' | 'high' | 'critical' = 'medium') {
  logger.warn('Security Event', {
    event,
    severity,
    details,
    timestamp: new Date().toISOString()
  });
}

/**
 * Log de performance
 */
export function logPerformance(operation: string, duration: number, details?: any) {
  const logData = {
    operation,
    duration: `${duration}ms`,
    details,
    timestamp: new Date().toISOString()
  };

  if (duration > 5000) { // Más de 5 segundos
    logger.warn('Slow Operation', logData);
  } else {
    logger.info('Performance', logData);
  }
}

/**
 * Log de errores de validación
 */
export function logValidation(field: string, value: any, error: string, userId?: string) {
  logger.warn('Validation Error', {
    field,
    value,
    error,
    userId,
    timestamp: new Date().toISOString()
  });
}

/**
 * Log de notificaciones
 */
export function logNotification(type: 'push' | 'email' | 'sms', recipient: string, success: boolean, error?: any) {
  const logData = {
    type,
    recipient,
    success,
    timestamp: new Date().toISOString()
  };

  if (error) {
    logger.error('Notification Failed', { ...logData, error });
  } else {
    logger.info('Notification Sent', logData);
  }
}

// =====================================================
// MIDDLEWARE DE LOGGING
// =====================================================

/**
 * Middleware para logging de requests
 */
export function requestLogger() {
  return (req: any, res: any, next: any) => {
    const start = Date.now();
    
    // Generar ID único para el request
    req.id = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    res.on('finish', () => {
      const responseTime = Date.now() - start;
      logRequest(req, res, responseTime);
    });
    
    next();
  };
}

/**
 * Middleware para capturar errores
 */
export function errorLogger() {
  return (error: any, req: any, res: any, next: any) => {
    logger.error('Unhandled Error', {
      error: {
        message: error.message,
        stack: error.stack,
        name: error.name
      },
      request: {
        method: req.method,
        url: req.url,
        headers: req.headers,
        body: req.body,
        userId: req.user?.id,
        requestId: req.id
      }
    });
    
    next(error);
  };
}

// =====================================================
// CONFIGURACIÓN ADICIONAL
// =====================================================

// En producción, agregar transport para servicios externos
if (CONFIG.SERVER.NODE_ENV === 'production') {
  // Ejemplo: Sentry, LogDNA, etc.
  if (CONFIG.APP.SENTRY_DSN) {
    // Configurar Sentry transport aquí
  }
}

// Stream para Morgan (HTTP logging)
export const morganStream = {
  write: (message: string) => {
    logger.info(message.trim());
  }
};

// =====================================================
// HEALTH CHECK DEL LOGGER
// =====================================================

export function loggerHealthCheck(): boolean {
  try {
    logger.info('Logger health check');
    return true;
  } catch (error) {
    console.error('Logger health check failed:', error);
    return false;
  }
}

// =====================================================
// EXPORTACIONES
// =====================================================

export default {
  logger,
  logRequest,
  logAuth,
  logDatabase,
  logSecurity,
  logPerformance,
  logValidation,
  logNotification,
  requestLogger,
  errorLogger,
  morganStream,
  loggerHealthCheck
};
