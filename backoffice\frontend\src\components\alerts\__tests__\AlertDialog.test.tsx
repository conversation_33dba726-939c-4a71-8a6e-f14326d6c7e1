/**
 * 🌋 Volcano App Frontend - Tests para AlertDialog
 * Tests unitarios para el diálogo de creación/edición de alertas
 */

import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi } from 'vitest';
import { AlertDialog } from '../AlertDialog';

const mockAlert = {
  id: '1',
  title: 'Alerta Volcánica Amarilla',
  message: 'Actividad volcánica moderada detectada en el Volcán Villarrica',
  alert_level: 'YELLOW' as const,
  volcano_name: 'Villarrica',
  volcano_lat: -39.2904,
  volcano_lng: -71.9048,
  is_active: true,
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
  expires_at: '2024-12-31T23:59:59Z'
};

const mockProps = {
  open: true,
  onClose: vi.fn(),
  alert: null,
  onSave: vi.fn(),
  isSubmitting: false
};

describe('AlertDialog', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders create alert dialog correctly', () => {
    render(<AlertDialog {...mockProps} />);
    
    expect(screen.getByText('Crear Nueva Alerta')).toBeInTheDocument();
    expect(screen.getByText('Crear alerta volcánica')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Título de la alerta')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Mensaje de la alerta')).toBeInTheDocument();
  });

  it('renders edit alert dialog correctly', () => {
    render(<AlertDialog {...mockProps} alert={mockAlert} />);
    
    expect(screen.getByText('Editar Alerta')).toBeInTheDocument();
    expect(screen.getByText('Modificar alerta volcánica')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Alerta Volcánica Amarilla')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Actividad volcánica moderada detectada en el Volcán Villarrica')).toBeInTheDocument();
  });

  it('does not render when closed', () => {
    render(<AlertDialog {...mockProps} open={false} />);
    
    expect(screen.queryByText('Crear Nueva Alerta')).not.toBeInTheDocument();
  });

  it('calls onClose when close button is clicked', () => {
    render(<AlertDialog {...mockProps} />);
    
    const closeButton = screen.getByRole('button', { name: /cerrar/i });
    fireEvent.click(closeButton);
    
    expect(mockProps.onClose).toHaveBeenCalledTimes(1);
  });

  it('calls onClose when cancel button is clicked', () => {
    render(<AlertDialog {...mockProps} />);
    
    const cancelButton = screen.getByText('Cancelar');
    fireEvent.click(cancelButton);
    
    expect(mockProps.onClose).toHaveBeenCalledTimes(1);
  });

  it('validates required fields', async () => {
    const user = userEvent.setup();
    render(<AlertDialog {...mockProps} />);
    
    const saveButton = screen.getByText('Crear Alerta');
    await user.click(saveButton);
    
    await waitFor(() => {
      expect(screen.getByText('El título es requerido')).toBeInTheDocument();
      expect(screen.getByText('El mensaje es requerido')).toBeInTheDocument();
      expect(screen.getByText('El nombre del volcán es requerido')).toBeInTheDocument();
    });
  });

  it('handles form submission for creating alert', async () => {
    const user = userEvent.setup();
    render(<AlertDialog {...mockProps} />);
    
    // Llenar formulario
    await user.type(screen.getByPlaceholderText('Título de la alerta'), 'Nueva Alerta');
    await user.type(screen.getByPlaceholderText('Mensaje de la alerta'), 'Mensaje de prueba');
    await user.selectOptions(screen.getByDisplayValue('GREEN'), 'YELLOW');
    await user.type(screen.getByPlaceholderText('Nombre del volcán'), 'Villarrica');
    await user.type(screen.getByPlaceholderText('Latitud'), '-39.2904');
    await user.type(screen.getByPlaceholderText('Longitud'), '-71.9048');
    
    // Enviar formulario
    const saveButton = screen.getByText('Crear Alerta');
    await user.click(saveButton);
    
    await waitFor(() => {
      expect(mockProps.onSave).toHaveBeenCalledWith(
        expect.objectContaining({
          title: 'Nueva Alerta',
          message: 'Mensaje de prueba',
          alert_level: 'YELLOW',
          volcano_name: 'Villarrica',
          volcano_lat: -39.2904,
          volcano_lng: -71.9048
        })
      );
    });
  });

  it('handles form submission for updating alert', async () => {
    const user = userEvent.setup();
    render(<AlertDialog {...mockProps} alert={mockAlert} />);
    
    // Modificar título
    const titleInput = screen.getByDisplayValue('Alerta Volcánica Amarilla');
    await user.clear(titleInput);
    await user.type(titleInput, 'Alerta Modificada');
    
    // Enviar formulario
    const saveButton = screen.getByText('Actualizar Alerta');
    await user.click(saveButton);
    
    await waitFor(() => {
      expect(mockProps.onSave).toHaveBeenCalledWith(
        expect.objectContaining({
          id: '1',
          title: 'Alerta Modificada'
        })
      );
    });
  });

  it('displays alert level options correctly', () => {
    render(<AlertDialog {...mockProps} />);
    
    const alertLevelSelect = screen.getByDisplayValue('GREEN');
    expect(alertLevelSelect).toBeInTheDocument();
    
    // Verificar opciones disponibles
    expect(screen.getByText('Verde - Normal')).toBeInTheDocument();
    expect(screen.getByText('Amarillo - Precaución')).toBeInTheDocument();
    expect(screen.getByText('Naranja - Alerta')).toBeInTheDocument();
    expect(screen.getByText('Rojo - Emergencia')).toBeInTheDocument();
  });

  it('validates coordinate inputs', async () => {
    const user = userEvent.setup();
    render(<AlertDialog {...mockProps} />);
    
    const latInput = screen.getByPlaceholderText('Latitud');
    const lngInput = screen.getByPlaceholderText('Longitud');
    
    await user.type(latInput, '100'); // Latitud inválida
    await user.type(lngInput, '200'); // Longitud inválida
    
    // Trigger validation
    await user.tab();
    
    await waitFor(() => {
      expect(screen.getByText('Latitud debe estar entre -90 y 90')).toBeInTheDocument();
      expect(screen.getByText('Longitud debe estar entre -180 y 180')).toBeInTheDocument();
    });
  });

  it('handles expiration date input', async () => {
    const user = userEvent.setup();
    render(<AlertDialog {...mockProps} />);
    
    const expirationInput = screen.getByLabelText('Fecha de expiración');
    await user.type(expirationInput, '2024-12-31T23:59');
    
    expect(expirationInput).toHaveValue('2024-12-31T23:59');
  });

  it('validates expiration date is in the future', async () => {
    const user = userEvent.setup();
    render(<AlertDialog {...mockProps} />);
    
    const expirationInput = screen.getByLabelText('Fecha de expiración');
    await user.type(expirationInput, '2020-01-01T00:00'); // Fecha pasada
    
    // Trigger validation
    await user.tab();
    
    await waitFor(() => {
      expect(screen.getByText('La fecha de expiración debe ser futura')).toBeInTheDocument();
    });
  });

  it('shows loading state during submission', async () => {
    const user = userEvent.setup();
    render(<AlertDialog {...mockProps} isSubmitting={true} />);
    
    const saveButton = screen.getByText('Creando...');
    expect(saveButton).toBeDisabled();
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  it('handles active status toggle', async () => {
    const user = userEvent.setup();
    render(<AlertDialog {...mockProps} />);
    
    const activeToggle = screen.getByLabelText('Alerta activa');
    expect(activeToggle).toBeChecked(); // Por defecto activa
    
    await user.click(activeToggle);
    expect(activeToggle).not.toBeChecked();
  });

  it('shows character count for message field', async () => {
    const user = userEvent.setup();
    render(<AlertDialog {...mockProps} />);
    
    const messageInput = screen.getByPlaceholderText('Mensaje de la alerta');
    await user.type(messageInput, 'Mensaje de prueba');
    
    expect(screen.getByText('18/500')).toBeInTheDocument();
  });

  it('validates message length', async () => {
    const user = userEvent.setup();
    render(<AlertDialog {...mockProps} />);
    
    const messageInput = screen.getByPlaceholderText('Mensaje de la alerta');
    const longMessage = 'a'.repeat(501); // Excede el límite
    await user.type(messageInput, longMessage);
    
    await waitFor(() => {
      expect(screen.getByText('El mensaje no puede exceder 500 caracteres')).toBeInTheDocument();
    });
  });

  it('resets form when dialog is closed and reopened', async () => {
    const { rerender } = render(<AlertDialog {...mockProps} />);
    
    // Llenar formulario
    const titleInput = screen.getByPlaceholderText('Título de la alerta');
    await userEvent.type(titleInput, 'Test Alert');
    
    // Cerrar dialog
    rerender(<AlertDialog {...mockProps} open={false} />);
    
    // Reabrir dialog
    rerender(<AlertDialog {...mockProps} open={true} />);
    
    // Verificar que el formulario se resetea
    expect(screen.getByPlaceholderText('Título de la alerta')).toHaveValue('');
  });

  it('populates form with alert data when editing', () => {
    render(<AlertDialog {...mockProps} alert={mockAlert} />);
    
    expect(screen.getByDisplayValue('Alerta Volcánica Amarilla')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Actividad volcánica moderada detectada en el Volcán Villarrica')).toBeInTheDocument();
    expect(screen.getByDisplayValue('YELLOW')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Villarrica')).toBeInTheDocument();
    expect(screen.getByDisplayValue('-39.2904')).toBeInTheDocument();
    expect(screen.getByDisplayValue('-71.9048')).toBeInTheDocument();
  });

  it('shows alert level color preview', () => {
    render(<AlertDialog {...mockProps} alert={mockAlert} />);
    
    const colorPreview = screen.getByTestId('alert-level-preview');
    expect(colorPreview).toHaveStyle('background-color: #fbbf24'); // Yellow color
  });

  it('handles keyboard shortcuts', () => {
    render(<AlertDialog {...mockProps} />);
    
    // Simular Ctrl+S para guardar
    fireEvent.keyDown(document, { key: 's', ctrlKey: true });
    
    // Simular Escape para cerrar
    fireEvent.keyDown(document, { key: 'Escape' });
    expect(mockProps.onClose).toHaveBeenCalledTimes(1);
  });
});
