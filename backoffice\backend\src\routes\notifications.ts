/**
 * 🌋 Volcano App Backend - Rutas de Notificaciones
 * Endpoints para gestión de notificaciones push y alertas en tiempo real
 */

import { Router } from 'express';
import { requireMinimumRole } from '@/middleware/auth';
import { asyncHandler } from '@/middleware/errorHandler';
import { UserRole } from '@/types';
import { body, query } from 'express-validator';
import { handleValidationErrors } from '@/middleware/validation';

// Importar controladores
import {
  sendManualNotification,
  sendTestNotification,
  getNotificationHistory,
  getNotificationStats,
  getNotificationConfig
} from '@/controllers/notifications';

// =====================================================
// VALIDACIONES
// =====================================================

const validateSendNotification = [
  body('title')
    .notEmpty()
    .withMessage('Title is required')
    .isLength({ max: 100 })
    .withMessage('Title must be less than 100 characters'),
  body('body')
    .notEmpty()
    .withMessage('Body is required')
    .isLength({ max: 500 })
    .withMessage('Body must be less than 500 characters'),
  body('target_type')
    .optional()
    .isIn(['all', 'zone', 'location', 'device'])
    .withMessage('Invalid target type'),
  body('sound')
    .optional()
    .isBoolean()
    .withMessage('Sound must be a boolean'),
  body('vibration')
    .optional()
    .isBoolean()
    .withMessage('Vibration must be a boolean'),
  body('priority')
    .optional()
    .isIn(['normal', 'high'])
    .withMessage('Priority must be normal or high'),
  handleValidationErrors()
];

const validateTestNotification = [
  body('device_id')
    .notEmpty()
    .withMessage('Device ID is required')
    .isString()
    .withMessage('Device ID must be a string'),
  handleValidationErrors()
];

const validateHistoryQuery = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('type')
    .optional()
    .isString()
    .withMessage('Type must be a string'),
  query('start_date')
    .optional()
    .isISO8601()
    .withMessage('Start date must be a valid ISO 8601 date'),
  query('end_date')
    .optional()
    .isISO8601()
    .withMessage('End date must be a valid ISO 8601 date'),
  handleValidationErrors()
];

const validateStatsQuery = [
  query('period')
    .optional()
    .isIn(['24h', '7d', '30d'])
    .withMessage('Period must be 24h, 7d, or 30d'),
  handleValidationErrors()
];

// =====================================================
// ROUTER DE NOTIFICACIONES
// =====================================================

const router = Router();

// =====================================================
// RUTAS DE ENVÍO (REQUIEREN PERMISOS ALTOS)
// =====================================================

/**
 * Enviar notificación manual
 * POST /notifications/send
 * Requiere rol mínimo: OPERATOR
 */
router.post('/send', 
  requireMinimumRole(UserRole.OPERATOR),
  validateSendNotification,
  asyncHandler(sendManualNotification)
);

/**
 * Enviar notificación de prueba
 * POST /notifications/test
 * Requiere rol mínimo: OPERATOR
 */
router.post('/test', 
  requireMinimumRole(UserRole.OPERATOR),
  validateTestNotification,
  asyncHandler(sendTestNotification)
);

// =====================================================
// RUTAS DE CONSULTA (REQUIEREN PERMISOS BÁSICOS)
// =====================================================

/**
 * Obtener historial de notificaciones
 * GET /notifications/history
 * Requiere rol mínimo: VIEWER
 */
router.get('/history', 
  requireMinimumRole(UserRole.VIEWER),
  validateHistoryQuery,
  asyncHandler(getNotificationHistory)
);

/**
 * Obtener estadísticas de notificaciones
 * GET /notifications/stats
 * Requiere rol mínimo: VIEWER
 */
router.get('/stats', 
  requireMinimumRole(UserRole.VIEWER),
  validateStatsQuery,
  asyncHandler(getNotificationStats)
);

/**
 * Obtener configuración de notificaciones
 * GET /notifications/config
 * Requiere rol mínimo: VIEWER
 */
router.get('/config', 
  requireMinimumRole(UserRole.VIEWER),
  asyncHandler(getNotificationConfig)
);

// =====================================================
// RUTA DE INFORMACIÓN
// =====================================================

/**
 * Información sobre notificaciones
 * GET /notifications/_info
 */
router.get('/_info', (req, res) => {
  res.json({
    success: true,
    data: {
      name: 'Volcano App Notifications API',
      version: '1.0.0',
      endpoints: {
        send: 'POST /api/notifications/send',
        test: 'POST /api/notifications/test',
        history: 'GET /api/notifications/history',
        stats: 'GET /api/notifications/stats',
        config: 'GET /api/notifications/config'
      },
      target_types: [
        'all',      // Todos los dispositivos
        'zone',     // Dispositivos en una zona específica
        'location', // Dispositivos en un radio específico
        'device'    // Dispositivos específicos
      ],
      notification_types: [
        'volcano_alert',      // Alertas volcánicas
        'zone_update',        // Actualizaciones de zonas
        'manual_notification', // Notificaciones manuales
        'test_notification'   // Notificaciones de prueba
      ],
      permissions: {
        send: 'OPERATOR or higher',
        test: 'OPERATOR or higher',
        view: 'VIEWER or higher'
      },
      features: {
        push_notifications: true,
        websocket_integration: true,
        audit_logging: true,
        statistics: true,
        targeting: true
      },
      supported_platforms: [
        'iOS',
        'Android',
        'Web (via WebSocket)'
      ]
    },
    timestamp: new Date()
  });
});

// =====================================================
// EXPORTACIONES
// =====================================================

export default router;
