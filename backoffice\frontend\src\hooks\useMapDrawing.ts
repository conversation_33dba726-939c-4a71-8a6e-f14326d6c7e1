/**
 * 🌋 Volcano App Backoffice - Hook para Gestión de Dibujo en Mapas
 * Hook personalizado para manejar el estado y eventos de dibujo con Leaflet Draw
 */

import { useCallback, useEffect, useRef, useState } from 'react';
import L from 'leaflet';
import 'leaflet-draw';
import { useToast } from '@/hooks/use-toast';
import { 
  leafletToGeoJSON, 
  validateGeometry, 
  ZoneGeometry, 
  GeometryValidationResult,
  formatArea,
  formatDistance
} from '@/services/geometry';

// =====================================================
// TIPOS Y INTERFACES
// =====================================================

export interface Zone {
  id: string;
  name: string;
  description: string;
  zone_type: 'SAFE' | 'EMERGENCY' | 'DANGER' | 'EVACUATION' | 'RESTRICTED';
  geometry: ZoneGeometry;
  is_active: boolean;
}

export interface DrawingState {
  isDrawing: boolean;
  currentTool: string | null;
  selectedZone: Zone | null;
  tempGeometry: ZoneGeometry | null;
  validationResult: GeometryValidationResult | null;
}

export interface DrawingCallbacks {
  onZoneCreate: (geometry: ZoneGeometry) => void;
  onZoneEdit: (zone: Zone, newGeometry: ZoneGeometry) => void;
  onZoneDelete: (zoneId: string) => void;
}

export interface UseMapDrawingOptions {
  enabledTools?: {
    polygon?: boolean;
    rectangle?: boolean;
    circle?: boolean;
    edit?: boolean;
    delete?: boolean;
  };
  zoneColors?: Record<string, string>;
  autoValidate?: boolean;
}

// =====================================================
// CONFIGURACIÓN POR DEFECTO
// =====================================================

const DEFAULT_OPTIONS: UseMapDrawingOptions = {
  enabledTools: {
    polygon: true,
    rectangle: true,
    circle: true,
    edit: true,
    delete: true
  },
  zoneColors: {
    SAFE: '#22C55E',
    EMERGENCY: '#3B82F6',
    EVACUATION: '#F59E0B',
    DANGER: '#EF4444',
    RESTRICTED: '#6B7280'
  },
  autoValidate: true
};

// =====================================================
// HOOK PRINCIPAL
// =====================================================

export function useMapDrawing(
  map: L.Map | null,
  zones: Zone[],
  callbacks: DrawingCallbacks,
  options: UseMapDrawingOptions = {}
) {
  const { toast } = useToast();
  const mergedOptions = { ...DEFAULT_OPTIONS, ...options };
  
  // Referencias
  const drawnItemsRef = useRef<L.FeatureGroup | null>(null);
  const drawControlRef = useRef<L.Control.Draw | null>(null);
  
  // Estados
  const [drawingState, setDrawingState] = useState<DrawingState>({
    isDrawing: false,
    currentTool: null,
    selectedZone: null,
    tempGeometry: null,
    validationResult: null
  });

  // =====================================================
  // FUNCIONES DE UTILIDAD
  // =====================================================

  const getZoneColor = useCallback((type: string): string => {
    return mergedOptions.zoneColors?.[type] || '#6B7280';
  }, [mergedOptions.zoneColors]);

  const validateAndSetGeometry = useCallback((geometry: ZoneGeometry) => {
    if (mergedOptions.autoValidate) {
      const validation = validateGeometry(geometry);
      setDrawingState(prev => ({
        ...prev,
        tempGeometry: geometry,
        validationResult: validation
      }));

      if (!validation.isValid) {
        toast({
          title: 'Geometría inválida',
          description: validation.errors.join(', '),
          variant: 'destructive',
        });
      } else if (validation.warnings.length > 0) {
        toast({
          title: 'Advertencias de geometría',
          description: validation.warnings.join(', '),
          variant: 'default',
        });
      }

      return validation.isValid;
    }
    return true;
  }, [mergedOptions.autoValidate, toast]);

  // =====================================================
  // CONFIGURACIÓN DE LEAFLET DRAW
  // =====================================================

  const setupDrawControl = useCallback(() => {
    if (!map || !drawnItemsRef.current) return;

    const drawnItems = drawnItemsRef.current;
    const { enabledTools } = mergedOptions;

    const drawControl = new L.Control.Draw({
      edit: {
        featureGroup: drawnItems,
        remove: enabledTools?.delete ?? true,
        edit: enabledTools?.edit ?? true
      },
      draw: {
        polygon: enabledTools?.polygon ? {
          allowIntersection: false,
          drawError: {
            color: '#e1e100',
            message: '<strong>Error:</strong> Las líneas no pueden cruzarse!'
          },
          shapeOptions: {
            color: getZoneColor('SAFE'),
            fillOpacity: 0.3,
            weight: 2
          }
        } : false,
        rectangle: enabledTools?.rectangle ? {
          shapeOptions: {
            color: getZoneColor('SAFE'),
            fillOpacity: 0.3,
            weight: 2
          }
        } : false,
        circle: enabledTools?.circle ? {
          shapeOptions: {
            color: getZoneColor('SAFE'),
            fillOpacity: 0.3,
            weight: 2
          }
        } : false,
        polyline: false,
        marker: false,
        circlemarker: false
      }
    });

    map.addControl(drawControl);
    drawControlRef.current = drawControl;

    return drawControl;
  }, [map, mergedOptions, getZoneColor]);

  // =====================================================
  // MANEJADORES DE EVENTOS
  // =====================================================

  const handleDrawCreated = useCallback((e: any) => {
    const layer = e.layer;
    const layerType = e.layerType;

    // Convertir a GeoJSON
    const geometry = leafletToGeoJSON(layer);
    if (!geometry) {
      toast({
        title: 'Error',
        description: 'No se pudo convertir la geometría dibujada',
        variant: 'destructive',
      });
      return;
    }

    // Validar geometría
    if (!validateAndSetGeometry(geometry)) {
      return;
    }

    // Agregar al mapa temporalmente
    if (drawnItemsRef.current) {
      drawnItemsRef.current.addLayer(layer);
      
      // Marcar como temporal
      (layer as any).isTemp = true;
      (layer as any).tempGeometry = geometry;

      // Crear popup de confirmación
      const validation = validateGeometry(geometry);
      const popupContent = createConfirmationPopup(geometry, validation, layerType);
      layer.bindPopup(popupContent).openPopup();

      setDrawingState(prev => ({
        ...prev,
        isDrawing: false,
        currentTool: null,
        tempGeometry: geometry
      }));

      toast({
        title: 'Zona dibujada',
        description: `Área: ${formatArea(validation.area || 0)}. Confirma para guardar.`,
      });
    }
  }, [validateAndSetGeometry, toast]);

  const handleDrawEdited = useCallback((e: any) => {
    const layers = e.layers;
    layers.eachLayer((layer: any) => {
      if (layer.zoneData) {
        const geometry = leafletToGeoJSON(layer);
        if (geometry && validateAndSetGeometry(geometry)) {
          callbacks.onZoneEdit(layer.zoneData, geometry);
        }
      }
    });
  }, [callbacks, validateAndSetGeometry]);

  const handleDrawDeleted = useCallback((e: any) => {
    const layers = e.layers;
    layers.eachLayer((layer: any) => {
      if (layer.zoneData) {
        callbacks.onZoneDelete(layer.zoneData.id);
      }
    });
  }, [callbacks]);

  const handleDrawStart = useCallback((e: any) => {
    setDrawingState(prev => ({
      ...prev,
      isDrawing: true,
      currentTool: e.layerType
    }));
  }, []);

  const handleDrawStop = useCallback(() => {
    setDrawingState(prev => ({
      ...prev,
      isDrawing: false,
      currentTool: null
    }));
  }, []);

  // =====================================================
  // FUNCIONES DE POPUP
  // =====================================================

  const createConfirmationPopup = useCallback((
    geometry: ZoneGeometry, 
    validation: GeometryValidationResult,
    layerType: string
  ): string => {
    const area = validation.area ? formatArea(validation.area) : 'N/A';
    const perimeter = validation.perimeter ? formatDistance(validation.perimeter) : 'N/A';
    
    return `
      <div style="min-width: 250px; font-family: system-ui;">
        <h4 style="margin: 0 0 10px 0; color: #1f2937;">Nueva Zona de Seguridad</h4>
        <div style="margin-bottom: 10px; padding: 8px; background: #f3f4f6; border-radius: 4px;">
          <p style="margin: 0; font-size: 12px; color: #6b7280;"><strong>Tipo:</strong> ${layerType}</p>
          <p style="margin: 0; font-size: 12px; color: #6b7280;"><strong>Área:</strong> ${area}</p>
          <p style="margin: 0; font-size: 12px; color: #6b7280;"><strong>Perímetro:</strong> ${perimeter}</p>
        </div>
        ${validation.warnings.length > 0 ? `
          <div style="margin-bottom: 10px; padding: 6px; background: #fef3c7; border-radius: 4px; border-left: 3px solid #f59e0b;">
            <p style="margin: 0; font-size: 11px; color: #92400e;">⚠️ ${validation.warnings.join(', ')}</p>
          </div>
        ` : ''}
        <div style="display: flex; gap: 8px; margin-top: 12px;">
          <button onclick="window.confirmZone()" 
                  style="flex: 1; background: #22c55e; color: white; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer; font-size: 12px;">
            ✓ Confirmar
          </button>
          <button onclick="window.cancelZone()" 
                  style="flex: 1; background: #ef4444; color: white; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer; font-size: 12px;">
            ✗ Cancelar
          </button>
        </div>
      </div>
    `;
  }, []);

  // =====================================================
  // FUNCIONES GLOBALES PARA POPUPS
  // =====================================================

  const setupGlobalFunctions = useCallback(() => {
    (window as any).confirmZone = () => {
      if (drawingState.tempGeometry) {
        callbacks.onZoneCreate(drawingState.tempGeometry);
        setDrawingState(prev => ({
          ...prev,
          tempGeometry: null,
          validationResult: null
        }));
      }
      if (map) map.closePopup();
    };

    (window as any).cancelZone = () => {
      // Remover layer temporal
      if (drawnItemsRef.current) {
        drawnItemsRef.current.eachLayer((layer: any) => {
          if (layer.isTemp) {
            drawnItemsRef.current?.removeLayer(layer);
          }
        });
      }
      setDrawingState(prev => ({
        ...prev,
        tempGeometry: null,
        validationResult: null
      }));
      if (map) map.closePopup();
    };
  }, [map, drawingState.tempGeometry, callbacks]);

  return {
    drawingState,
    drawnItemsRef,
    drawControlRef,
    setupDrawControl,
    setupGlobalFunctions,
    getZoneColor,
    handlers: {
      onDrawCreated: handleDrawCreated,
      onDrawEdited: handleDrawEdited,
      onDrawDeleted: handleDrawDeleted,
      onDrawStart: handleDrawStart,
      onDrawStop: handleDrawStop
    }
  };
}
