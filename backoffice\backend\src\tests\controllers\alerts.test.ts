/**
 * 🌋 Volcano App Backend - Tests para controlador de alertas
 * Tests de integración para el controlador de alertas volcánicas
 */

import request from 'supertest';
import express from 'express';
import { jest } from '@jest/globals';
import { supabaseAdmin } from '@/services/supabase';
import alertRoutes from '@/routes/alerts';

// Mock para Supabase
jest.mock('@/services/supabase', () => ({
  supabaseAdmin: {
    from: jest.fn(),
    rpc: jest.fn()
  },
  logAuditAction: jest.fn()
}));

// Mock para middleware de autenticación
jest.mock('@/middleware/auth', () => ({
  authenticateToken: () => (req: any, res: any, next: any) => {
    req.user = {
      id: 'test-user-id',
      email: '<EMAIL>',
      role: 'ADMIN',
      full_name: 'Test User'
    };
    next();
  },
  requireMinimumRole: () => (req: any, res: any, next: any) => next()
}));

// Mock para servicio de notificaciones
jest.mock('@/services/notifications', () => ({
  notificationService: {
    sendAlertNotification: jest.fn()
  }
}));

// Configurar app de prueba
const app = express();
app.use(express.json());
app.use('/alerts', alertRoutes);

describe('Alerts Controller', () => {
  const mockSupabaseFrom = supabaseAdmin.from as jest.MockedFunction<typeof supabaseAdmin.from>;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /alerts', () => {
    it('should fetch all alerts successfully', async () => {
      const mockAlerts = [
        {
          id: '1',
          title: 'Alerta Volcánica Amarilla',
          message: 'Actividad volcánica moderada detectada',
          alert_level: 'YELLOW',
          volcano_name: 'Villarrica',
          volcano_lat: -39.2904,
          volcano_lng: -71.9048,
          is_active: true,
          created_at: '2024-01-01T00:00:00Z',
          expires_at: '2024-12-31T23:59:59Z'
        }
      ];

      mockSupabaseFrom.mockReturnValue({
        select: jest.fn().mockReturnValue({
          order: jest.fn().mockResolvedValue({
            data: mockAlerts,
            error: null
          })
        })
      } as any);

      const response = await request(app)
        .get('/alerts')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toEqual(mockAlerts);
      expect(mockSupabaseFrom).toHaveBeenCalledWith('volcano_alerts');
    });

    it('should support filtering by alert level', async () => {
      const mockYellowAlerts = [
        {
          id: '1',
          title: 'Alerta Amarilla',
          alert_level: 'YELLOW',
          is_active: true
        }
      ];

      mockSupabaseFrom.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            order: jest.fn().mockResolvedValue({
              data: mockYellowAlerts,
              error: null
            })
          })
        })
      } as any);

      const response = await request(app)
        .get('/alerts?alert_level=YELLOW')
        .expect(200);

      expect(response.body.data).toEqual(mockYellowAlerts);
    });

    it('should support filtering by active status', async () => {
      const mockActiveAlerts = [
        {
          id: '1',
          title: 'Alerta Activa',
          is_active: true
        }
      ];

      mockSupabaseFrom.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            order: jest.fn().mockResolvedValue({
              data: mockActiveAlerts,
              error: null
            })
          })
        })
      } as any);

      const response = await request(app)
        .get('/alerts?is_active=true')
        .expect(200);

      expect(response.body.data).toEqual(mockActiveAlerts);
    });
  });

  describe('GET /alerts/:id', () => {
    it('should fetch alert by ID successfully', async () => {
      const mockAlert = {
        id: '1',
        title: 'Alerta Volcánica',
        alert_level: 'YELLOW',
        is_active: true
      };

      mockSupabaseFrom.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: mockAlert,
              error: null
            })
          })
        })
      } as any);

      const response = await request(app)
        .get('/alerts/1')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toEqual(mockAlert);
    });

    it('should return 404 for non-existent alert', async () => {
      mockSupabaseFrom.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: null,
              error: { code: 'PGRST116' }
            })
          })
        })
      } as any);

      const response = await request(app)
        .get('/alerts/999')
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Alert not found');
    });
  });

  describe('POST /alerts', () => {
    it('should create alert successfully', async () => {
      const newAlert = {
        title: 'Nueva Alerta Volcánica',
        message: 'Actividad volcánica detectada en el Volcán Villarrica',
        alert_level: 'YELLOW',
        volcano_name: 'Villarrica',
        volcano_lat: -39.2904,
        volcano_lng: -71.9048,
        is_active: true,
        expires_at: '2024-12-31T23:59:59Z'
      };

      const createdAlert = {
        id: 'new-alert-id',
        ...newAlert,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
      };

      mockSupabaseFrom.mockReturnValue({
        insert: jest.fn().mockReturnValue({
          select: jest.fn().mockResolvedValue({
            data: [createdAlert],
            error: null
          })
        })
      } as any);

      const response = await request(app)
        .post('/alerts')
        .send(newAlert)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toEqual(createdAlert);
    });

    it('should validate required fields', async () => {
      const invalidAlert = {
        message: 'Alerta sin título'
      };

      const response = await request(app)
        .post('/alerts')
        .send(invalidAlert)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.errors).toBeDefined();
    });

    it('should validate alert level', async () => {
      const invalidAlert = {
        title: 'Alerta Inválida',
        message: 'Mensaje',
        alert_level: 'INVALID_LEVEL',
        volcano_name: 'Villarrica'
      };

      const response = await request(app)
        .post('/alerts')
        .send(invalidAlert)
        .expect(400);

      expect(response.body.success).toBe(false);
    });

    it('should validate coordinates', async () => {
      const invalidAlert = {
        title: 'Alerta con Coordenadas Inválidas',
        message: 'Mensaje',
        alert_level: 'YELLOW',
        volcano_name: 'Villarrica',
        volcano_lat: 100, // Inválida
        volcano_lng: 200  // Inválida
      };

      const response = await request(app)
        .post('/alerts')
        .send(invalidAlert)
        .expect(400);

      expect(response.body.success).toBe(false);
    });

    it('should validate expiration date', async () => {
      const invalidAlert = {
        title: 'Alerta con Fecha Inválida',
        message: 'Mensaje',
        alert_level: 'YELLOW',
        volcano_name: 'Villarrica',
        expires_at: '2020-01-01T00:00:00Z' // Fecha pasada
      };

      const response = await request(app)
        .post('/alerts')
        .send(invalidAlert)
        .expect(400);

      expect(response.body.success).toBe(false);
    });

    it('should send notification when alert is created', async () => {
      const { notificationService } = require('@/services/notifications');
      
      const newAlert = {
        title: 'Alerta de Emergencia',
        message: 'Actividad volcánica crítica',
        alert_level: 'RED',
        volcano_name: 'Villarrica',
        is_active: true
      };

      const createdAlert = {
        id: 'emergency-alert-id',
        ...newAlert,
        created_at: '2024-01-01T00:00:00Z'
      };

      mockSupabaseFrom.mockReturnValue({
        insert: jest.fn().mockReturnValue({
          select: jest.fn().mockResolvedValue({
            data: [createdAlert],
            error: null
          })
        })
      } as any);

      await request(app)
        .post('/alerts')
        .send(newAlert)
        .expect(201);

      expect(notificationService.sendAlertNotification).toHaveBeenCalledWith(createdAlert);
    });
  });

  describe('PUT /alerts/:id', () => {
    it('should update alert successfully', async () => {
      const updates = {
        title: 'Alerta Actualizada',
        message: 'Mensaje actualizado',
        alert_level: 'ORANGE'
      };

      const updatedAlert = {
        id: '1',
        ...updates,
        volcano_name: 'Villarrica',
        is_active: true,
        updated_at: '2024-01-01T00:00:00Z'
      };

      mockSupabaseFrom.mockReturnValue({
        update: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            select: jest.fn().mockResolvedValue({
              data: [updatedAlert],
              error: null
            })
          })
        })
      } as any);

      const response = await request(app)
        .put('/alerts/1')
        .send(updates)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toEqual(updatedAlert);
    });

    it('should return 404 for non-existent alert', async () => {
      mockSupabaseFrom.mockReturnValue({
        update: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            select: jest.fn().mockResolvedValue({
              data: [],
              error: null
            })
          })
        })
      } as any);

      const response = await request(app)
        .put('/alerts/999')
        .send({ title: 'Updated Title' })
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Alert not found');
    });
  });

  describe('DELETE /alerts/:id', () => {
    it('should delete alert successfully', async () => {
      mockSupabaseFrom.mockReturnValue({
        delete: jest.fn().mockReturnValue({
          eq: jest.fn().mockResolvedValue({
            data: null,
            error: null,
            count: 1
          })
        })
      } as any);

      const response = await request(app)
        .delete('/alerts/1')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Alert deleted successfully');
    });

    it('should return 404 for non-existent alert', async () => {
      mockSupabaseFrom.mockReturnValue({
        delete: jest.fn().mockReturnValue({
          eq: jest.fn().mockResolvedValue({
            data: null,
            error: null,
            count: 0
          })
        })
      } as any);

      const response = await request(app)
        .delete('/alerts/999')
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Alert not found');
    });
  });

  describe('GET /alerts/active', () => {
    it('should fetch only active alerts', async () => {
      const mockActiveAlerts = [
        {
          id: '1',
          title: 'Alerta Activa 1',
          alert_level: 'YELLOW',
          is_active: true
        },
        {
          id: '2',
          title: 'Alerta Activa 2',
          alert_level: 'ORANGE',
          is_active: true
        }
      ];

      mockSupabaseFrom.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            order: jest.fn().mockResolvedValue({
              data: mockActiveAlerts,
              error: null
            })
          })
        })
      } as any);

      const response = await request(app)
        .get('/alerts/active')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toEqual(mockActiveAlerts);
      expect(response.body.data.every((alert: any) => alert.is_active)).toBe(true);
    });

    it('should return empty array when no active alerts', async () => {
      mockSupabaseFrom.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            order: jest.fn().mockResolvedValue({
              data: [],
              error: null
            })
          })
        })
      } as any);

      const response = await request(app)
        .get('/alerts/active')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toEqual([]);
    });
  });
});
