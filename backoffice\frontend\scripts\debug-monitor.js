#!/usr/bin/env node

/**
 * 🔧 Monitor de Debugging para Volcano App Frontend
 * Script para capturar y analizar errores del servidor Vite en tiempo real
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

class ViteDebugMonitor {
  constructor() {
    this.logFile = path.join(__dirname, '../logs/vite-debug.log');
    this.errorFile = path.join(__dirname, '../logs/vite-errors.log');
    this.viteProcess = null;
    this.isRunning = false;
    
    // Crear directorio de logs si no existe
    const logsDir = path.dirname(this.logFile);
    if (!fs.existsSync(logsDir)) {
      fs.mkdirSync(logsDir, { recursive: true });
    }
    
    this.setupSignalHandlers();
  }

  /**
   * Configurar manejadores de señales para limpieza
   */
  setupSignalHandlers() {
    process.on('SIGINT', () => {
      console.log('\n🛑 Deteniendo monitor de debugging...');
      this.stop();
      process.exit(0);
    });

    process.on('SIGTERM', () => {
      this.stop();
      process.exit(0);
    });
  }

  /**
   * Iniciar el monitor
   */
  start() {
    if (this.isRunning) {
      console.log('⚠️  El monitor ya está ejecutándose');
      return;
    }

    console.log('🚀 Iniciando Volcano App Frontend con monitor de debugging...');
    console.log(`📝 Logs guardados en: ${this.logFile}`);
    console.log(`🚨 Errores guardados en: ${this.errorFile}`);
    console.log('');

    // Detectar el comando correcto para el sistema operativo
    const isWindows = process.platform === 'win32';
    const npmCommand = isWindows ? 'npm.cmd' : 'npm';

    // Iniciar Vite con configuración de debugging
    this.viteProcess = spawn(npmCommand, ['run', 'dev'], {
      cwd: path.join(__dirname, '..'),
      stdio: ['inherit', 'pipe', 'pipe'],
      shell: isWindows,
      env: {
        ...process.env,
        NODE_ENV: 'development',
        VITE_DEBUG: 'true',
        DEBUG: 'vite:*'
      }
    });

    this.isRunning = true;

    // Procesar stdout (logs normales)
    this.viteProcess.stdout.on('data', (data) => {
      const output = data.toString();
      this.processOutput(output, 'info');
      process.stdout.write(output);
    });

    // Procesar stderr (errores y warnings)
    this.viteProcess.stderr.on('data', (data) => {
      const output = data.toString();
      this.processOutput(output, 'error');
      process.stderr.write(output);
    });

    // Manejar cierre del proceso
    this.viteProcess.on('close', (code) => {
      this.isRunning = false;
      if (code !== 0) {
        console.log(`\n❌ Vite terminó con código de error: ${code}`);
        this.logError(`Vite process exited with code ${code}`);
      } else {
        console.log('\n✅ Vite terminó correctamente');
      }
    });

    this.viteProcess.on('error', (error) => {
      console.error('❌ Error al iniciar Vite:', error);
      this.logError(`Failed to start Vite: ${error.message}`);
      this.isRunning = false;
    });
  }

  /**
   * Procesar salida del servidor Vite
   */
  processOutput(output, level) {
    const timestamp = new Date().toISOString();
    const logEntry = `[${timestamp}] [${level.toUpperCase()}] ${output}`;

    // Guardar en archivo de log general
    fs.appendFileSync(this.logFile, logEntry);

    // Analizar y categorizar errores
    this.analyzeOutput(output, timestamp);
  }

  /**
   * Analizar salida para detectar errores específicos
   */
  analyzeOutput(output, timestamp) {
    const errorPatterns = [
      {
        pattern: /Error:/i,
        type: 'javascript_error',
        severity: 'high'
      },
      {
        pattern: /Warning:/i,
        type: 'warning',
        severity: 'medium'
      },
      {
        pattern: /Failed to resolve/i,
        type: 'module_resolution',
        severity: 'high'
      },
      {
        pattern: /Cannot find module/i,
        type: 'missing_module',
        severity: 'high'
      },
      {
        pattern: /Syntax error/i,
        type: 'syntax_error',
        severity: 'high'
      },
      {
        pattern: /Type error/i,
        type: 'type_error',
        severity: 'medium'
      },
      {
        pattern: /\[vite\] Internal server error/i,
        type: 'vite_internal_error',
        severity: 'critical'
      },
      {
        pattern: /ECONNREFUSED/i,
        type: 'connection_refused',
        severity: 'high'
      },
      {
        pattern: /A Select\.Item .* must have a value prop/i,
        type: 'react_select_error',
        severity: 'medium'
      }
    ];

    for (const { pattern, type, severity } of errorPatterns) {
      if (pattern.test(output)) {
        this.logStructuredError({
          timestamp,
          type,
          severity,
          message: output.trim(),
          raw: output
        });
        break;
      }
    }
  }

  /**
   * Registrar error estructurado
   */
  logStructuredError(errorData) {
    const errorEntry = {
      ...errorData,
      id: `error_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
    };

    // Guardar en archivo de errores
    fs.appendFileSync(
      this.errorFile,
      JSON.stringify(errorEntry, null, 2) + '\n---\n'
    );

    // Mostrar en consola con formato
    this.displayError(errorEntry);
  }

  /**
   * Mostrar error formateado en consola
   */
  displayError(error) {
    const severityColors = {
      critical: '\x1b[41m\x1b[37m', // Fondo rojo, texto blanco
      high: '\x1b[31m',             // Texto rojo
      medium: '\x1b[33m',           // Texto amarillo
      low: '\x1b[36m'               // Texto cyan
    };

    const resetColor = '\x1b[0m';
    const color = severityColors[error.severity] || severityColors.medium;

    console.log(`\n${color}🚨 ERROR DETECTADO [${error.severity.toUpperCase()}]${resetColor}`);
    console.log(`${color}Tipo: ${error.type}${resetColor}`);
    console.log(`${color}ID: ${error.id}${resetColor}`);
    console.log(`${color}Timestamp: ${error.timestamp}${resetColor}`);
    console.log(`${color}Mensaje:${resetColor}`);
    console.log(`${color}${error.message}${resetColor}`);
    console.log('');
  }

  /**
   * Registrar error simple
   */
  logError(message) {
    const timestamp = new Date().toISOString();
    const errorEntry = `[${timestamp}] [ERROR] ${message}\n`;
    fs.appendFileSync(this.errorFile, errorEntry);
  }

  /**
   * Detener el monitor
   */
  stop() {
    if (this.viteProcess && this.isRunning) {
      this.viteProcess.kill('SIGTERM');
      this.isRunning = false;
      console.log('🛑 Monitor de debugging detenido');
    }
  }

  /**
   * Obtener estadísticas de errores
   */
  getErrorStats() {
    if (!fs.existsSync(this.errorFile)) {
      return { totalErrors: 0, errorsByType: {}, errorsBySeverity: {} };
    }

    const content = fs.readFileSync(this.errorFile, 'utf8');
    const errorBlocks = content.split('---\n').filter(block => block.trim());
    
    const errors = errorBlocks.map(block => {
      try {
        return JSON.parse(block.trim());
      } catch {
        return null;
      }
    }).filter(Boolean);

    const errorsByType = {};
    const errorsBySeverity = {};

    errors.forEach(error => {
      errorsByType[error.type] = (errorsByType[error.type] || 0) + 1;
      errorsBySeverity[error.severity] = (errorsBySeverity[error.severity] || 0) + 1;
    });

    return {
      totalErrors: errors.length,
      errorsByType,
      errorsBySeverity,
      recentErrors: errors.slice(-10)
    };
  }

  /**
   * Mostrar estadísticas
   */
  showStats() {
    const stats = this.getErrorStats();
    
    console.log('\n📊 ESTADÍSTICAS DE DEBUGGING');
    console.log('================================');
    console.log(`Total de errores: ${stats.totalErrors}`);
    
    if (Object.keys(stats.errorsByType).length > 0) {
      console.log('\nErrores por tipo:');
      Object.entries(stats.errorsByType).forEach(([type, count]) => {
        console.log(`  ${type}: ${count}`);
      });
    }
    
    if (Object.keys(stats.errorsBySeverity).length > 0) {
      console.log('\nErrores por severidad:');
      Object.entries(stats.errorsBySeverity).forEach(([severity, count]) => {
        console.log(`  ${severity}: ${count}`);
      });
    }
    
    console.log('');
  }
}

// Ejecutar si es llamado directamente
if (require.main === module) {
  const monitor = new ViteDebugMonitor();
  
  // Manejar argumentos de línea de comandos
  const args = process.argv.slice(2);
  
  if (args.includes('--stats')) {
    monitor.showStats();
  } else {
    monitor.start();
    
    // Mostrar estadísticas cada 5 minutos
    setInterval(() => {
      monitor.showStats();
    }, 5 * 60 * 1000);
  }
}

module.exports = ViteDebugMonitor;
