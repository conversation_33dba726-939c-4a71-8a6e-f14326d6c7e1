/**
 * 🌋 Volcano App Backend - Tests para middleware de autenticación
 * Tests unitarios para el middleware de autenticación y autorización
 */

import { Request, Response, NextFunction } from 'express';
import { jest } from '@jest/globals';
import jwt from 'jsonwebtoken';
import { authenticateToken, requireMinimumRole } from '@/middleware/auth';
import { supabaseAdmin } from '@/services/supabase';

// Mock para jsonwebtoken
jest.mock('jsonwebtoken', () => ({
  verify: jest.fn(),
  sign: jest.fn()
}));

// Mock para Supabase
jest.mock('@/services/supabase', () => ({
  supabaseAdmin: {
    from: jest.fn()
  }
}));

// Mock para variables de entorno
process.env.JWT_SECRET = 'test-jwt-secret';

describe('Auth Middleware', () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockNext: NextFunction;

  beforeEach(() => {
    mockRequest = {
      headers: {},
      user: undefined
    };
    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis()
    };
    mockNext = jest.fn();
    jest.clearAllMocks();
  });

  describe('authenticateToken', () => {
    it('should authenticate valid token successfully', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        role: 'ADMIN',
        full_name: 'Test User'
      };

      const mockToken = 'valid-jwt-token';
      const mockDecodedToken = {
        userId: 'user-123',
        email: '<EMAIL>',
        iat: Date.now() / 1000,
        exp: (Date.now() / 1000) + 3600
      };

      mockRequest.headers = {
        authorization: `Bearer ${mockToken}`
      };

      (jwt.verify as jest.MockedFunction<typeof jwt.verify>).mockReturnValue(mockDecodedToken);

      const mockSupabaseFrom = supabaseAdmin.from as jest.MockedFunction<typeof supabaseAdmin.from>;
      mockSupabaseFrom.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: mockUser,
              error: null
            })
          })
        })
      } as any);

      await authenticateToken(mockRequest as Request, mockResponse as Response, mockNext);

      expect(jwt.verify).toHaveBeenCalledWith(mockToken, process.env.JWT_SECRET);
      expect(mockRequest.user).toEqual(mockUser);
      expect(mockNext).toHaveBeenCalled();
    });

    it('should reject request without authorization header', async () => {
      mockRequest.headers = {};

      await authenticateToken(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: 'Access token required'
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should reject request with invalid authorization format', async () => {
      mockRequest.headers = {
        authorization: 'InvalidFormat token'
      };

      await authenticateToken(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: 'Invalid token format'
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should reject expired token', async () => {
      const mockToken = 'expired-jwt-token';
      mockRequest.headers = {
        authorization: `Bearer ${mockToken}`
      };

      const tokenError = new Error('jwt expired');
      tokenError.name = 'TokenExpiredError';

      (jwt.verify as jest.MockedFunction<typeof jwt.verify>).mockImplementation(() => {
        throw tokenError;
      });

      await authenticateToken(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: 'Token expired'
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should reject invalid token', async () => {
      const mockToken = 'invalid-jwt-token';
      mockRequest.headers = {
        authorization: `Bearer ${mockToken}`
      };

      const tokenError = new Error('invalid signature');
      tokenError.name = 'JsonWebTokenError';

      (jwt.verify as jest.MockedFunction<typeof jwt.verify>).mockImplementation(() => {
        throw tokenError;
      });

      await authenticateToken(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: 'Invalid token'
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should reject when user not found in database', async () => {
      const mockToken = 'valid-jwt-token';
      const mockDecodedToken = {
        userId: 'non-existent-user',
        email: '<EMAIL>',
        iat: Date.now() / 1000,
        exp: (Date.now() / 1000) + 3600
      };

      mockRequest.headers = {
        authorization: `Bearer ${mockToken}`
      };

      (jwt.verify as jest.MockedFunction<typeof jwt.verify>).mockReturnValue(mockDecodedToken);

      const mockSupabaseFrom = supabaseAdmin.from as jest.MockedFunction<typeof supabaseAdmin.from>;
      mockSupabaseFrom.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: null,
              error: { code: 'PGRST116' }
            })
          })
        })
      } as any);

      await authenticateToken(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: 'User not found'
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle database errors', async () => {
      const mockToken = 'valid-jwt-token';
      const mockDecodedToken = {
        userId: 'user-123',
        email: '<EMAIL>',
        iat: Date.now() / 1000,
        exp: (Date.now() / 1000) + 3600
      };

      mockRequest.headers = {
        authorization: `Bearer ${mockToken}`
      };

      (jwt.verify as jest.MockedFunction<typeof jwt.verify>).mockReturnValue(mockDecodedToken);

      const mockSupabaseFrom = supabaseAdmin.from as jest.MockedFunction<typeof supabaseAdmin.from>;
      mockSupabaseFrom.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: null,
              error: new Error('Database connection failed')
            })
          })
        })
      } as any);

      await authenticateToken(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: 'Authentication failed'
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle malformed JWT payload', async () => {
      const mockToken = 'malformed-jwt-token';
      const mockDecodedToken = {
        // Missing required fields
        iat: Date.now() / 1000,
        exp: (Date.now() / 1000) + 3600
      };

      mockRequest.headers = {
        authorization: `Bearer ${mockToken}`
      };

      (jwt.verify as jest.MockedFunction<typeof jwt.verify>).mockReturnValue(mockDecodedToken);

      await authenticateToken(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: 'Invalid token payload'
      });
      expect(mockNext).not.toHaveBeenCalled();
    });
  });

  describe('requireMinimumRole', () => {
    beforeEach(() => {
      mockRequest.user = {
        id: 'user-123',
        email: '<EMAIL>',
        role: 'USER',
        full_name: 'Test User'
      };
    });

    it('should allow access for exact role match', () => {
      const middleware = requireMinimumRole('USER');
      
      middleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalled();
    });

    it('should allow access for higher role', () => {
      mockRequest.user!.role = 'ADMIN';
      const middleware = requireMinimumRole('USER');
      
      middleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalled();
    });

    it('should deny access for lower role', () => {
      mockRequest.user!.role = 'USER';
      const middleware = requireMinimumRole('ADMIN');
      
      middleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: 'Insufficient permissions'
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should deny access when user is not authenticated', () => {
      mockRequest.user = undefined;
      const middleware = requireMinimumRole('USER');
      
      middleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: 'Authentication required'
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle invalid role', () => {
      mockRequest.user!.role = 'INVALID_ROLE' as any;
      const middleware = requireMinimumRole('USER');
      
      middleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(403);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: 'Invalid user role'
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should correctly handle role hierarchy', () => {
      const testCases = [
        { userRole: 'SUPER_ADMIN', requiredRole: 'ADMIN', shouldPass: true },
        { userRole: 'ADMIN', requiredRole: 'MODERATOR', shouldPass: true },
        { userRole: 'MODERATOR', requiredRole: 'USER', shouldPass: true },
        { userRole: 'USER', requiredRole: 'MODERATOR', shouldPass: false },
        { userRole: 'MODERATOR', requiredRole: 'ADMIN', shouldPass: false },
        { userRole: 'ADMIN', requiredRole: 'SUPER_ADMIN', shouldPass: false }
      ];

      testCases.forEach(({ userRole, requiredRole, shouldPass }) => {
        // Reset mocks
        jest.clearAllMocks();
        
        mockRequest.user!.role = userRole as any;
        const middleware = requireMinimumRole(requiredRole as any);
        
        middleware(mockRequest as Request, mockResponse as Response, mockNext);

        if (shouldPass) {
          expect(mockNext).toHaveBeenCalled();
        } else {
          expect(mockResponse.status).toHaveBeenCalledWith(403);
          expect(mockNext).not.toHaveBeenCalled();
        }
      });
    });
  });
});
