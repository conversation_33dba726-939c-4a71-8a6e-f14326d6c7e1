/**
 * 🌋 Volcano App Backend - Redis Cache Service
 * High-performance caching for API responses and real-time data
 */

import Redis from 'ioredis';
import { logger } from '@/utils/logger';
import { CONFIG } from '@/config/env';

// =====================================================
// REDIS CLIENT CONFIGURATION
// =====================================================

let redis: Redis | null = null;

// Redis configuration
const redisConfig = {
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379'),
  password: process.env.REDIS_PASSWORD || undefined,
  db: parseInt(process.env.REDIS_DB || '0'),
  retryDelayOnFailover: 100,
  maxRetriesPerRequest: 3,
  lazyConnect: true,
  keepAlive: 30000,
  connectTimeout: 10000,
  commandTimeout: 5000
};

// =====================================================
// CACHE KEYS AND TTL CONSTANTS
// =====================================================

export const CACHE_KEYS = {
  // Alerts
  CURRENT_ALERT: 'volcano:alert:current',
  ACTIVE_ALERTS: 'volcano:alerts:active',
  ALERT_BY_ID: (id: string) => `volcano:alert:${id}`,
  
  // Zones
  ACTIVE_ZONES: 'volcano:zones:active',
  ZONE_BY_ID: (id: string) => `volcano:zone:${id}`,
  ZONES_BY_TYPE: (type: string) => `volcano:zones:type:${type}`,
  
  // Mobile
  MOBILE_CONFIG: 'volcano:mobile:config',
  USER_LOCATION: (anonymousId: string) => `volcano:location:${anonymousId}`,
  
  // System
  SYSTEM_STATS: 'volcano:system:stats',
  API_HEALTH: 'volcano:api:health',
  
  // User sessions
  USER_SESSION: (userId: string) => `volcano:session:${userId}`,
  REFRESH_TOKEN: (token: string) => `volcano:refresh:${token}`,
  
  // Rate limiting
  RATE_LIMIT: (ip: string) => `volcano:ratelimit:${ip}`,
  
  // Real-time subscriptions
  WEBSOCKET_ROOMS: 'volcano:websocket:rooms',
  ACTIVE_CONNECTIONS: 'volcano:websocket:connections'
} as const;

export const CACHE_TTL = {
  SHORT: 60, // 1 minute
  MEDIUM: 300, // 5 minutes
  LONG: 1800, // 30 minutes
  VERY_LONG: 3600, // 1 hour
  SESSION: 86400, // 24 hours
  REFRESH_TOKEN: 604800 // 7 days
} as const;

// =====================================================
// REDIS CLIENT INITIALIZATION
// =====================================================

/**
 * Initialize Redis connection
 */
export async function initializeRedis(): Promise<void> {
  try {
    // Skip Redis in test environment or if disabled
    if (process.env.NODE_ENV === 'test' || process.env.REDIS_DISABLED === 'true') {
      logger.info('Redis disabled for test environment or by configuration');
      return;
    }

    redis = new Redis(redisConfig);

    redis.on('connect', () => {
      logger.info('Redis client connected successfully');
    });

    redis.on('ready', () => {
      logger.info('Redis client ready to receive commands');
    });

    redis.on('error', (error) => {
      logger.error('Redis connection error:', error);
    });

    redis.on('close', () => {
      logger.warn('Redis connection closed');
    });

    redis.on('reconnecting', () => {
      logger.info('Redis client reconnecting...');
    });

    // Test connection
    await redis.ping();
    logger.info('Redis connection established and tested');

  } catch (error) {
    logger.error('Failed to initialize Redis:', error);
    // Don't throw error - app should work without Redis
    redis = null;
  }
}

/**
 * Get Redis client instance
 */
export function getRedisClient(): Redis | null {
  return redis;
}

/**
 * Check if Redis is available
 */
export function isRedisAvailable(): boolean {
  return redis !== null && redis.status === 'ready';
}

// =====================================================
// CACHE OPERATIONS
// =====================================================

/**
 * Set cache value with TTL
 */
export async function setCache(
  key: string, 
  value: any, 
  ttl: number = CACHE_TTL.MEDIUM
): Promise<boolean> {
  try {
    if (!isRedisAvailable()) {
      return false;
    }

    const serializedValue = JSON.stringify(value);
    await redis!.setex(key, ttl, serializedValue);
    
    logger.debug(`Cache set: ${key} (TTL: ${ttl}s)`);
    return true;
  } catch (error) {
    logger.error(`Cache set error for key ${key}:`, error);
    return false;
  }
}

/**
 * Get cache value
 */
export async function getCache<T = any>(key: string): Promise<T | null> {
  try {
    if (!isRedisAvailable()) {
      return null;
    }

    const value = await redis!.get(key);
    if (!value) {
      return null;
    }

    const parsedValue = JSON.parse(value);
    logger.debug(`Cache hit: ${key}`);
    return parsedValue as T;
  } catch (error) {
    logger.error(`Cache get error for key ${key}:`, error);
    return null;
  }
}

/**
 * Delete cache key
 */
export async function deleteCache(key: string): Promise<boolean> {
  try {
    if (!isRedisAvailable()) {
      return false;
    }

    const result = await redis!.del(key);
    logger.debug(`Cache deleted: ${key}`);
    return result > 0;
  } catch (error) {
    logger.error(`Cache delete error for key ${key}:`, error);
    return false;
  }
}

/**
 * Delete multiple cache keys by pattern
 */
export async function deleteCachePattern(pattern: string): Promise<number> {
  try {
    if (!isRedisAvailable()) {
      return 0;
    }

    const keys = await redis!.keys(pattern);
    if (keys.length === 0) {
      return 0;
    }

    const result = await redis!.del(...keys);
    logger.debug(`Cache pattern deleted: ${pattern} (${result} keys)`);
    return result;
  } catch (error) {
    logger.error(`Cache pattern delete error for pattern ${pattern}:`, error);
    return 0;
  }
}

/**
 * Check if cache key exists
 */
export async function cacheExists(key: string): Promise<boolean> {
  try {
    if (!isRedisAvailable()) {
      return false;
    }

    const result = await redis!.exists(key);
    return result === 1;
  } catch (error) {
    logger.error(`Cache exists check error for key ${key}:`, error);
    return false;
  }
}

/**
 * Get cache TTL
 */
export async function getCacheTTL(key: string): Promise<number> {
  try {
    if (!isRedisAvailable()) {
      return -1;
    }

    return await redis!.ttl(key);
  } catch (error) {
    logger.error(`Cache TTL check error for key ${key}:`, error);
    return -1;
  }
}

/**
 * Increment cache value (for counters)
 */
export async function incrementCache(key: string, ttl?: number): Promise<number> {
  try {
    if (!isRedisAvailable()) {
      return 0;
    }

    const result = await redis!.incr(key);
    
    if (ttl && result === 1) {
      await redis!.expire(key, ttl);
    }
    
    return result;
  } catch (error) {
    logger.error(`Cache increment error for key ${key}:`, error);
    return 0;
  }
}

// =====================================================
// SPECIALIZED CACHE FUNCTIONS
// =====================================================

/**
 * Cache current volcano alert
 */
export async function cacheCurrentAlert(alert: any): Promise<void> {
  await setCache(CACHE_KEYS.CURRENT_ALERT, alert, CACHE_TTL.SHORT);
}

/**
 * Get cached current alert
 */
export async function getCachedCurrentAlert(): Promise<any> {
  return await getCache(CACHE_KEYS.CURRENT_ALERT);
}

/**
 * Cache active zones
 */
export async function cacheActiveZones(zones: any[]): Promise<void> {
  await setCache(CACHE_KEYS.ACTIVE_ZONES, zones, CACHE_TTL.MEDIUM);
}

/**
 * Get cached active zones
 */
export async function getCachedActiveZones(): Promise<any[]> {
  return await getCache(CACHE_KEYS.ACTIVE_ZONES) || [];
}

/**
 * Cache mobile configuration
 */
export async function cacheMobileConfig(config: any): Promise<void> {
  await setCache(CACHE_KEYS.MOBILE_CONFIG, config, CACHE_TTL.LONG);
}

/**
 * Get cached mobile configuration
 */
export async function getCachedMobileConfig(): Promise<any> {
  return await getCache(CACHE_KEYS.MOBILE_CONFIG);
}

/**
 * Invalidate all alert-related cache
 */
export async function invalidateAlertCache(): Promise<void> {
  await Promise.all([
    deleteCache(CACHE_KEYS.CURRENT_ALERT),
    deleteCachePattern('volcano:alert:*'),
    deleteCachePattern('volcano:alerts:*')
  ]);
}

/**
 * Invalidate all zone-related cache
 */
export async function invalidateZoneCache(): Promise<void> {
  await Promise.all([
    deleteCache(CACHE_KEYS.ACTIVE_ZONES),
    deleteCachePattern('volcano:zone:*'),
    deleteCachePattern('volcano:zones:*')
  ]);
}

// =====================================================
// CLEANUP AND SHUTDOWN
// =====================================================

/**
 * Close Redis connection
 */
export async function closeRedis(): Promise<void> {
  if (redis) {
    await redis.quit();
    redis = null;
    logger.info('Redis connection closed');
  }
}

// =====================================================
// EXPORTS
// =====================================================

export default {
  initializeRedis,
  getRedisClient,
  isRedisAvailable,
  setCache,
  getCache,
  deleteCache,
  deleteCachePattern,
  cacheExists,
  getCacheTTL,
  incrementCache,
  cacheCurrentAlert,
  getCachedCurrentAlert,
  cacheActiveZones,
  getCachedActiveZones,
  cacheMobileConfig,
  getCachedMobileConfig,
  invalidateAlertCache,
  invalidateZoneCache,
  closeRedis,
  CACHE_KEYS,
  CACHE_TTL
};
