# 📱 Volcano App - Mobile Integration Guide

## Overview

This guide provides everything needed to integrate the Expo mobile application with the Volcano App backend API.

## 🔗 API Base URL

```javascript
const API_BASE_URL = 'http://localhost:3001/api';
const WEBSOCKET_URL = 'ws://localhost:3001';
```

## 🚀 Quick Start

### 1. Install Dependencies

```bash
npm install axios socket.io-client @react-native-async-storage/async-storage
```

### 2. Basic API Client Setup

```javascript
// api/client.js
import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';

const apiClient = axios.create({
  baseURL: 'http://localhost:3001/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add auth token to requests
apiClient.interceptors.request.use(async (config) => {
  const token = await AsyncStorage.getItem('auth_token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

export default apiClient;
```

### 3. WebSocket Connection

```javascript
// services/websocket.js
import io from 'socket.io-client';
import AsyncStorage from '@react-native-async-storage/async-storage';

class WebSocketService {
  constructor() {
    this.socket = null;
  }

  async connect() {
    const token = await AsyncStorage.getItem('auth_token');
    const deviceId = await AsyncStorage.getItem('device_id');
    
    this.socket = io('ws://localhost:3001', {
      auth: {
        token,
        deviceId,
        appVersion: '1.0.0'
      },
      transports: ['websocket', 'polling']
    });

    this.setupEventListeners();
  }

  setupEventListeners() {
    this.socket.on('alert:updated', (data) => {
      // Handle alert updates
      console.log('Alert updated:', data);
    });

    this.socket.on('zone:updated', (data) => {
      // Handle zone updates
      console.log('Zone updated:', data);
    });
  }

  sendHeartbeat() {
    if (this.socket) {
      this.socket.emit('mobile:heartbeat');
    }
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
    }
  }
}

export default new WebSocketService();
```

## 📡 Key API Endpoints for Mobile

### 1. Get Current Alert

```javascript
// Get the current active volcano alert
const getCurrentAlert = async () => {
  try {
    const response = await apiClient.get('/mobile/alerts/current');
    return response.data.data; // Alert object or null
  } catch (error) {
    console.error('Error fetching current alert:', error);
    return null;
  }
};
```

### 2. Get All Safety Zones

```javascript
// Get all active safety zones
const getSafetyZones = async () => {
  try {
    const response = await apiClient.get('/mobile/zones/all');
    return response.data.data; // Array of zones
  } catch (error) {
    console.error('Error fetching zones:', error);
    return [];
  }
};
```

### 3. Report User Location

```javascript
// Report user location (anonymous)
const reportLocation = async (latitude, longitude, accuracy = null) => {
  try {
    const deviceId = await AsyncStorage.getItem('device_id');
    const response = await apiClient.post('/mobile/location/report', {
      anonymous_id: deviceId,
      latitude,
      longitude,
      accuracy,
      app_version: '1.0.0',
      device_type: Platform.OS
    });
    return response.data;
  } catch (error) {
    console.error('Error reporting location:', error);
    return null;
  }
};
```

### 4. Check Location Safety

```javascript
// Check if location is in safety zones
const checkLocationSafety = async (latitude, longitude) => {
  try {
    const response = await apiClient.post('/mobile/location/check', {
      latitude,
      longitude
    });
    return response.data.data; // Safety status and recommendations
  } catch (error) {
    console.error('Error checking location safety:', error);
    return null;
  }
};
```

### 5. Bulk Data Synchronization

```javascript
// Sync data for offline-first app
const syncData = async (lastSync = null) => {
  try {
    const deviceInfo = {
      platform: Platform.OS,
      version: Platform.Version,
      model: await DeviceInfo.getModel()
    };

    const response = await apiClient.post('/mobile/sync', {
      lastSync,
      deviceInfo,
      appVersion: '1.0.0',
      requestedData: ['alerts', 'zones', 'config']
    });

    return response.data.data;
  } catch (error) {
    console.error('Error syncing data:', error);
    return null;
  }
};
```

### 6. Check App Version

```javascript
// Check if app version is supported
const checkAppVersion = async () => {
  try {
    const response = await apiClient.get('/mobile/version/check', {
      params: {
        version: '1.0.0',
        platform: Platform.OS
      }
    });
    return response.data.data;
  } catch (error) {
    console.error('Error checking app version:', error);
    return null;
  }
};
```

### 7. Batch Location Reporting (Offline Sync)

```javascript
// Report multiple locations (for offline sync)
const reportLocationBatch = async (locations) => {
  try {
    const deviceInfo = {
      appVersion: '1.0.0',
      platform: Platform.OS
    };

    const response = await apiClient.post('/mobile/location/batch', {
      locations,
      deviceInfo
    });

    return response.data.data;
  } catch (error) {
    console.error('Error reporting location batch:', error);
    return null;
  }
};
```

## 🔄 Offline-First Implementation

### Data Synchronization Strategy

```javascript
// services/syncService.js
class SyncService {
  constructor() {
    this.lastSync = null;
    this.pendingLocations = [];
  }

  async initialize() {
    this.lastSync = await AsyncStorage.getItem('last_sync');
    this.pendingLocations = JSON.parse(
      await AsyncStorage.getItem('pending_locations') || '[]'
    );
  }

  async syncData() {
    try {
      // Sync server data
      const syncData = await this.syncFromServer();
      if (syncData) {
        await this.saveLocalData(syncData);
        this.lastSync = syncData.syncTimestamp;
        await AsyncStorage.setItem('last_sync', this.lastSync);
      }

      // Sync pending locations
      if (this.pendingLocations.length > 0) {
        await this.syncPendingLocations();
      }

    } catch (error) {
      console.error('Sync failed:', error);
    }
  }

  async syncFromServer() {
    return await syncData(this.lastSync);
  }

  async syncPendingLocations() {
    if (this.pendingLocations.length === 0) return;

    const result = await reportLocationBatch(this.pendingLocations);
    if (result && result.inserted > 0) {
      // Remove successfully synced locations
      this.pendingLocations = [];
      await AsyncStorage.setItem('pending_locations', '[]');
    }
  }

  async addPendingLocation(location) {
    this.pendingLocations.push({
      ...location,
      timestamp: new Date().toISOString()
    });
    await AsyncStorage.setItem(
      'pending_locations', 
      JSON.stringify(this.pendingLocations)
    );
  }
}

export default new SyncService();
```

## 🗺️ Location Services Integration

### React Native Location Setup

```javascript
// services/locationService.js
import * as Location from 'expo-location';
import SyncService from './syncService';

class LocationService {
  constructor() {
    this.watchId = null;
    this.lastReportedLocation = null;
  }

  async requestPermissions() {
    const { status } = await Location.requestForegroundPermissionsAsync();
    return status === 'granted';
  }

  async startLocationTracking() {
    const hasPermission = await this.requestPermissions();
    if (!hasPermission) return false;

    this.watchId = await Location.watchPositionAsync(
      {
        accuracy: Location.Accuracy.High,
        timeInterval: 60000, // 1 minute
        distanceInterval: 100, // 100 meters
      },
      this.handleLocationUpdate.bind(this)
    );

    return true;
  }

  async handleLocationUpdate(location) {
    const { latitude, longitude, accuracy } = location.coords;
    
    // Check if location changed significantly
    if (this.shouldReportLocation(latitude, longitude)) {
      // Try to report immediately
      const result = await reportLocation(latitude, longitude, accuracy);
      
      if (!result) {
        // If failed, add to pending locations for offline sync
        await SyncService.addPendingLocation({
          anonymous_id: await AsyncStorage.getItem('device_id'),
          latitude,
          longitude,
          accuracy
        });
      }

      this.lastReportedLocation = { latitude, longitude };
    }
  }

  shouldReportLocation(lat, lng) {
    if (!this.lastReportedLocation) return true;
    
    // Calculate distance from last reported location
    const distance = this.calculateDistance(
      this.lastReportedLocation.latitude,
      this.lastReportedLocation.longitude,
      lat,
      lng
    );
    
    return distance > 50; // Report if moved more than 50 meters
  }

  calculateDistance(lat1, lon1, lat2, lon2) {
    // Haversine formula implementation
    const R = 6371e3; // Earth's radius in meters
    const φ1 = lat1 * Math.PI/180;
    const φ2 = lat2 * Math.PI/180;
    const Δφ = (lat2-lat1) * Math.PI/180;
    const Δλ = (lon2-lon1) * Math.PI/180;

    const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
              Math.cos(φ1) * Math.cos(φ2) *
              Math.sin(Δλ/2) * Math.sin(Δλ/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

    return R * c;
  }

  stopLocationTracking() {
    if (this.watchId) {
      this.watchId.remove();
      this.watchId = null;
    }
  }
}

export default new LocationService();
```

## 🔔 Real-time Notifications

### Push Notification Setup

```javascript
// services/notificationService.js
import * as Notifications from 'expo-notifications';
import WebSocketService from './websocket';

class NotificationService {
  async initialize() {
    await this.requestPermissions();
    this.setupWebSocketListeners();
  }

  async requestPermissions() {
    const { status } = await Notifications.requestPermissionsAsync();
    return status === 'granted';
  }

  setupWebSocketListeners() {
    WebSocketService.socket?.on('alert:updated', (data) => {
      this.showAlertNotification(data.data);
    });
  }

  async showAlertNotification(alert) {
    await Notifications.scheduleNotificationAsync({
      content: {
        title: `🌋 ${alert.alert_level} Alert`,
        body: alert.message,
        data: { alertId: alert.id, type: 'volcano_alert' },
      },
      trigger: null, // Show immediately
    });
  }
}

export default new NotificationService();
```

## 🧪 Testing the Integration

### Test API Connectivity

```javascript
// utils/apiTest.js
export const testAPIConnectivity = async () => {
  try {
    const response = await apiClient.get('/mobile/health');
    console.log('API Health:', response.data);
    return response.data.status === 'healthy';
  } catch (error) {
    console.error('API connectivity test failed:', error);
    return false;
  }
};
```

### Test WebSocket Connection

```javascript
// utils/websocketTest.js
export const testWebSocketConnection = () => {
  return new Promise((resolve) => {
    WebSocketService.connect();
    
    WebSocketService.socket.on('connect', () => {
      console.log('WebSocket connected successfully');
      resolve(true);
    });

    WebSocketService.socket.on('connect_error', (error) => {
      console.error('WebSocket connection failed:', error);
      resolve(false);
    });

    // Timeout after 5 seconds
    setTimeout(() => resolve(false), 5000);
  });
};
```

## 📱 Example App Component

```javascript
// components/VolcanoMonitor.jsx
import React, { useEffect, useState } from 'react';
import { View, Text, Alert } from 'react-native';
import LocationService from '../services/locationService';
import WebSocketService from '../services/websocket';
import SyncService from '../services/syncService';

const VolcanoMonitor = () => {
  const [currentAlert, setCurrentAlert] = useState(null);
  const [safetyStatus, setSafetyStatus] = useState('unknown');
  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    initializeServices();
    return () => cleanup();
  }, []);

  const initializeServices = async () => {
    // Initialize sync service
    await SyncService.initialize();
    
    // Start location tracking
    await LocationService.startLocationTracking();
    
    // Connect WebSocket
    await WebSocketService.connect();
    setIsConnected(true);
    
    // Initial data sync
    await SyncService.syncData();
    
    // Get current alert
    const alert = await getCurrentAlert();
    setCurrentAlert(alert);
  };

  const cleanup = () => {
    LocationService.stopLocationTracking();
    WebSocketService.disconnect();
  };

  return (
    <View style={{ padding: 20 }}>
      <Text style={{ fontSize: 24, fontWeight: 'bold' }}>
        🌋 Volcano Monitor
      </Text>
      
      <Text>Connection: {isConnected ? '✅ Connected' : '❌ Disconnected'}</Text>
      
      {currentAlert && (
        <View style={{ marginTop: 20, padding: 15, backgroundColor: '#ffebee' }}>
          <Text style={{ fontWeight: 'bold', color: '#d32f2f' }}>
            {currentAlert.alert_level} ALERT
          </Text>
          <Text>{currentAlert.message}</Text>
        </View>
      )}
      
      <Text style={{ marginTop: 20 }}>
        Safety Status: {safetyStatus}
      </Text>
    </View>
  );
};

export default VolcanoMonitor;
```

## 🔧 Configuration

### Environment Variables

```javascript
// config/api.js
const config = {
  development: {
    API_BASE_URL: 'http://localhost:3001/api',
    WEBSOCKET_URL: 'ws://localhost:3001',
  },
  production: {
    API_BASE_URL: 'https://api.volcanoapp.com/api',
    WEBSOCKET_URL: 'wss://api.volcanoapp.com',
  }
};

export default config[__DEV__ ? 'development' : 'production'];
```

## 📚 Additional Resources

- **API Documentation**: http://localhost:3001/api-docs
- **WebSocket Events**: See IMPLEMENTATION_SUMMARY.md
- **Error Handling**: All endpoints return consistent error formats
- **Rate Limiting**: 100 requests per 15 minutes per IP

## 🆘 Troubleshooting

### Common Issues

1. **Connection Refused**: Ensure backend server is running on port 3001
2. **CORS Errors**: Check CORS_ORIGIN includes your development URL
3. **WebSocket Fails**: Verify WebSocket is enabled in backend config
4. **Location Permissions**: Ensure location permissions are granted
5. **Offline Sync**: Check AsyncStorage for pending data

### Debug Mode

```javascript
// Enable debug logging
const DEBUG = __DEV__;

if (DEBUG) {
  console.log('API calls, WebSocket events, and sync operations');
}
```

This guide provides everything needed to integrate the Expo mobile app with the Volcano App backend API. The implementation supports offline-first functionality, real-time updates, and comprehensive error handling.
