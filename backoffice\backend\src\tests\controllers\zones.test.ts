/**
 * 🌋 Volcano App Backend - Tests para controlador de zonas
 * Tests de integración para el controlador de zonas de seguridad
 */

import request from 'supertest';
import express from 'express';
import { jest } from '@jest/globals';
import { supabaseAdmin } from '@/services/supabase';
import { authenticateToken } from '@/middleware/auth';
import zoneRoutes from '@/routes/zones';

// Mock para Supabase
jest.mock('@/services/supabase', () => ({
  supabaseAdmin: {
    from: jest.fn(),
    rpc: jest.fn()
  },
  logAuditAction: jest.fn()
}));

// Mock para middleware de autenticación
jest.mock('@/middleware/auth', () => ({
  authenticateToken: () => (req: any, res: any, next: any) => {
    req.user = {
      id: 'test-user-id',
      email: '<EMAIL>',
      role: 'ADMIN',
      full_name: 'Test User'
    };
    next();
  },
  requireMinimumRole: () => (req: any, res: any, next: any) => next()
}));

// Configurar app de prueba
const app = express();
app.use(express.json());
app.use('/zones', zoneRoutes);

describe('Zones Controller', () => {
  const mockSupabaseFrom = supabaseAdmin.from as jest.MockedFunction<typeof supabaseAdmin.from>;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /zones', () => {
    it('should fetch all zones successfully', async () => {
      const mockZones = [
        {
          id: '1',
          name: 'Zona Segura Centro',
          description: 'Zona de evacuación principal',
          zone_type: 'SAFE',
          geometry: {
            type: 'Polygon',
            coordinates: [[[-71.9048, -39.2904]]]
          },
          is_active: true,
          created_at: '2024-01-01T00:00:00Z'
        }
      ];

      mockSupabaseFrom.mockReturnValue({
        select: jest.fn().mockReturnValue({
          order: jest.fn().mockResolvedValue({
            data: mockZones,
            error: null
          })
        })
      } as any);

      const response = await request(app)
        .get('/zones')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toEqual(mockZones);
      expect(mockSupabaseFrom).toHaveBeenCalledWith('safety_zones');
    });

    it('should handle database error', async () => {
      const mockError = new Error('Database connection failed');

      mockSupabaseFrom.mockReturnValue({
        select: jest.fn().mockReturnValue({
          order: jest.fn().mockResolvedValue({
            data: null,
            error: mockError
          })
        })
      } as any);

      const response = await request(app)
        .get('/zones')
        .expect(500);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Failed to fetch zones');
    });

    it('should support pagination', async () => {
      const mockZones = Array.from({ length: 5 }, (_, i) => ({
        id: `${i + 1}`,
        name: `Zona ${i + 1}`,
        zone_type: 'SAFE',
        is_active: true
      }));

      mockSupabaseFrom.mockReturnValue({
        select: jest.fn().mockReturnValue({
          range: jest.fn().mockReturnValue({
            order: jest.fn().mockResolvedValue({
              data: mockZones.slice(0, 3),
              error: null
            })
          })
        })
      } as any);

      const response = await request(app)
        .get('/zones?page=1&limit=3')
        .expect(200);

      expect(response.body.data).toHaveLength(3);
    });

    it('should support search filtering', async () => {
      const mockZones = [
        {
          id: '1',
          name: 'Zona Segura Centro',
          zone_type: 'SAFE'
        }
      ];

      mockSupabaseFrom.mockReturnValue({
        select: jest.fn().mockReturnValue({
          ilike: jest.fn().mockReturnValue({
            order: jest.fn().mockResolvedValue({
              data: mockZones,
              error: null
            })
          })
        })
      } as any);

      const response = await request(app)
        .get('/zones?search=Centro')
        .expect(200);

      expect(response.body.data).toEqual(mockZones);
    });
  });

  describe('GET /zones/:id', () => {
    it('should fetch zone by ID successfully', async () => {
      const mockZone = {
        id: '1',
        name: 'Zona Segura Centro',
        zone_type: 'SAFE',
        is_active: true
      };

      mockSupabaseFrom.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: mockZone,
              error: null
            })
          })
        })
      } as any);

      const response = await request(app)
        .get('/zones/1')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toEqual(mockZone);
    });

    it('should return 404 for non-existent zone', async () => {
      mockSupabaseFrom.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: null,
              error: { code: 'PGRST116' }
            })
          })
        })
      } as any);

      const response = await request(app)
        .get('/zones/999')
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Zone not found');
    });

    it('should validate UUID format', async () => {
      const response = await request(app)
        .get('/zones/invalid-uuid')
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Invalid zone ID format');
    });
  });

  describe('POST /zones', () => {
    it('should create zone successfully', async () => {
      const newZone = {
        name: 'Nueva Zona Segura',
        description: 'Descripción de la zona',
        zone_type: 'SAFE',
        geometry: {
          type: 'Polygon',
          coordinates: [[[-71.9048, -39.2904], [-71.9000, -39.2904], [-71.9000, -39.2850], [-71.9048, -39.2850], [-71.9048, -39.2904]]]
        },
        capacity: 1000,
        is_active: true
      };

      const createdZone = {
        id: 'new-zone-id',
        ...newZone,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
      };

      mockSupabaseFrom.mockReturnValue({
        insert: jest.fn().mockReturnValue({
          select: jest.fn().mockResolvedValue({
            data: [createdZone],
            error: null
          })
        })
      } as any);

      const response = await request(app)
        .post('/zones')
        .send(newZone)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toEqual(createdZone);
    });

    it('should validate required fields', async () => {
      const invalidZone = {
        description: 'Zona sin nombre'
      };

      const response = await request(app)
        .post('/zones')
        .send(invalidZone)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.errors).toBeDefined();
    });

    it('should validate zone type', async () => {
      const invalidZone = {
        name: 'Zona Inválida',
        description: 'Descripción',
        zone_type: 'INVALID_TYPE',
        geometry: {
          type: 'Polygon',
          coordinates: [[[-71.9048, -39.2904]]]
        }
      };

      const response = await request(app)
        .post('/zones')
        .send(invalidZone)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.errors).toBeDefined();
    });

    it('should validate geometry format', async () => {
      const invalidZone = {
        name: 'Zona con Geometría Inválida',
        description: 'Descripción',
        zone_type: 'SAFE',
        geometry: {
          type: 'InvalidType',
          coordinates: 'invalid'
        }
      };

      const response = await request(app)
        .post('/zones')
        .send(invalidZone)
        .expect(400);

      expect(response.body.success).toBe(false);
    });

    it('should handle database constraint violations', async () => {
      const newZone = {
        name: 'Zona Duplicada',
        description: 'Descripción',
        zone_type: 'SAFE',
        geometry: {
          type: 'Polygon',
          coordinates: [[[-71.9048, -39.2904]]]
        }
      };

      mockSupabaseFrom.mockReturnValue({
        insert: jest.fn().mockReturnValue({
          select: jest.fn().mockResolvedValue({
            data: null,
            error: { code: '23505', message: 'duplicate key value' }
          })
        })
      } as any);

      const response = await request(app)
        .post('/zones')
        .send(newZone)
        .expect(409);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('already exists');
    });
  });

  describe('PUT /zones/:id', () => {
    it('should update zone successfully', async () => {
      const updates = {
        name: 'Zona Actualizada',
        description: 'Nueva descripción',
        capacity: 1500
      };

      const updatedZone = {
        id: '1',
        ...updates,
        zone_type: 'SAFE',
        is_active: true,
        updated_at: '2024-01-01T00:00:00Z'
      };

      mockSupabaseFrom.mockReturnValue({
        update: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            select: jest.fn().mockResolvedValue({
              data: [updatedZone],
              error: null
            })
          })
        })
      } as any);

      const response = await request(app)
        .put('/zones/1')
        .send(updates)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toEqual(updatedZone);
    });

    it('should return 404 for non-existent zone', async () => {
      mockSupabaseFrom.mockReturnValue({
        update: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            select: jest.fn().mockResolvedValue({
              data: [],
              error: null
            })
          })
        })
      } as any);

      const response = await request(app)
        .put('/zones/999')
        .send({ name: 'Updated Name' })
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Zone not found');
    });

    it('should validate update data', async () => {
      const invalidUpdates = {
        zone_type: 'INVALID_TYPE'
      };

      const response = await request(app)
        .put('/zones/1')
        .send(invalidUpdates)
        .expect(400);

      expect(response.body.success).toBe(false);
    });
  });

  describe('DELETE /zones/:id', () => {
    it('should delete zone successfully', async () => {
      mockSupabaseFrom.mockReturnValue({
        delete: jest.fn().mockReturnValue({
          eq: jest.fn().mockResolvedValue({
            data: null,
            error: null,
            count: 1
          })
        })
      } as any);

      const response = await request(app)
        .delete('/zones/1')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Zone deleted successfully');
    });

    it('should return 404 for non-existent zone', async () => {
      mockSupabaseFrom.mockReturnValue({
        delete: jest.fn().mockReturnValue({
          eq: jest.fn().mockResolvedValue({
            data: null,
            error: null,
            count: 0
          })
        })
      } as any);

      const response = await request(app)
        .delete('/zones/999')
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Zone not found');
    });
  });

  describe('GET /zones/active', () => {
    it('should fetch only active zones', async () => {
      const mockActiveZones = [
        {
          id: '1',
          name: 'Zona Activa 1',
          zone_type: 'SAFE',
          is_active: true
        },
        {
          id: '2',
          name: 'Zona Activa 2',
          zone_type: 'EMERGENCY',
          is_active: true
        }
      ];

      mockSupabaseFrom.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            order: jest.fn().mockResolvedValue({
              data: mockActiveZones,
              error: null
            })
          })
        })
      } as any);

      const response = await request(app)
        .get('/zones/active')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toEqual(mockActiveZones);
      expect(response.body.data.every((zone: any) => zone.is_active)).toBe(true);
    });
  });
});
