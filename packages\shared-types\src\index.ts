/**
 * 🌋 Volcano App - Tipos Compartidos
 * Tipos TypeScript utilizados en toda la aplicación
 */

// Re-exportar todos los tipos
export * from './auth';
export * from './alerts';
export * from './zones';
export * from './api';
export * from './common';

// Tipos base comunes
export interface BaseEntity {
  id: string;
  created_at: string;
  updated_at: string;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  timestamp: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface QueryFilters {
  page?: number;
  limit?: number;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
  search?: string;
  filters?: Record<string, any>;
}

// Constantes compartidas
export const DEFAULT_PAGINATION = {
  page: 1,
  limit: 20,
  max_limit: 100
} as const;

export const VOLCANO_COORDINATES = {
  lat: -39.420000,
  lng: -71.939167
} as const;
