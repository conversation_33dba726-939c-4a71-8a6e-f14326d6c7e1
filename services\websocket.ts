/**
 * 🌋 Volcano App - WebSocket Service
 * Servicio para conexiones en tiempo real con el backend
 */

import Constants from 'expo-constants';
import { Platform } from 'react-native';
import { io, Socket } from 'socket.io-client';
import { SafetyZone, VolcanoAlert } from './api';
import { getWebSocketURL } from './network-config';

// Configuración del WebSocket
const WEBSOCKET_CONFIG = {
  url: getWebSocketURL(),
  options: {
    transports: ['websocket', 'polling'], // Agregar polling como fallback
    timeout: 5000,
    reconnection: true,
    reconnectionAttempts: 5,
    reconnectionDelay: 1000,
    forceNew: true,
    autoConnect: false, // No conectar automáticamente
  },
};

// Log de la configuración para debugging
try {
  logNetworkConfig('WebSocket Service');
} catch (error) {
  console.log('🔧 WebSocket Service Network Configuration:', {
    platform: 'unknown',
    wsURL: WEBSOCKET_CONFIG.url,
    isDev: __DEV__
  });
}

// Tipos de eventos del WebSocket
export interface WebSocketEvents {
  // Eventos del servidor
  'alert:created': (alert: VolcanoAlert) => void;
  'alert:updated': (alert: VolcanoAlert) => void;
  'zone:updated': (zone: SafetyZone) => void;
  'system:status': (status: any) => void;
  'mobile:sync:response': (data: any) => void;
  
  // Eventos del cliente
  'mobile:heartbeat': () => void;
  'mobile:sync:request': (data: any) => void;
  'location:update': (location: { lat: number; lng: number }) => void;
}

export type WebSocketEventName = keyof WebSocketEvents;

class WebSocketService {
  private socket: Socket | null = null;
  private isConnected = false;
  private reconnectAttempts = 0;
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private eventListeners: Map<string, Set<Function>> = new Map();
  private connectionAttempted = false;

  constructor() {
    // Log de la configuración para debugging
    try {
      console.log(`🔧 WebSocket Configuration:`, {
        platform: Platform.OS,
        url: WEBSOCKET_CONFIG.url,
        hostUri: Constants.expoConfig?.hostUri,
        isDev: __DEV__
      });
    } catch (error) {
      console.log(`🔧 WebSocket Configuration:`, {
        platform: 'unknown',
        url: WEBSOCKET_CONFIG.url,
        isDev: __DEV__
      });
    }
    // No conectar automáticamente en el constructor
  }

  // Conectar al WebSocket
  connect(): void {
    if (this.socket?.connected || this.connectionAttempted) {
      return;
    }

    this.connectionAttempted = true;
    console.log('🔌 Connecting to WebSocket...');

    try {
      this.socket = io(WEBSOCKET_CONFIG.url, WEBSOCKET_CONFIG.options);
      this.socket.connect();
    } catch (error) {
      console.error('🚨 Failed to create WebSocket connection:', error);
      this.connectionAttempted = false;
      return;
    }

    // Eventos de conexión
    this.socket.on('connect', () => {
      console.log('✅ WebSocket connected');
      this.isConnected = true;
      this.reconnectAttempts = 0;
      this.startHeartbeat();
      this.notifyListeners('connected', null);
    });

    this.socket.on('disconnect', (reason) => {
      console.log('❌ WebSocket disconnected:', reason);
      this.isConnected = false;
      this.stopHeartbeat();
      this.notifyListeners('disconnected', reason);
    });

    this.socket.on('connect_error', (error) => {
      console.error('🚨 WebSocket connection error:', error);
      this.reconnectAttempts++;
      this.notifyListeners('error', error);
    });

    this.socket.on('reconnect', (attemptNumber) => {
      console.log('🔄 WebSocket reconnected after', attemptNumber, 'attempts');
      this.notifyListeners('reconnected', attemptNumber);
    });

    // Eventos específicos de la aplicación
    this.socket.on('alert:created', (alert: VolcanoAlert) => {
      console.log('🚨 New alert received:', alert.title);
      this.notifyListeners('alert:created', alert);
    });

    this.socket.on('alert:updated', (alert: VolcanoAlert) => {
      console.log('📝 Alert updated:', alert.title);
      this.notifyListeners('alert:updated', alert);
    });

    this.socket.on('zone:updated', (zone: SafetyZone) => {
      console.log('🗺️ Zone updated:', zone.name);
      this.notifyListeners('zone:updated', zone);
    });

    this.socket.on('system:status', (status: any) => {
      console.log('📊 System status update:', status);
      this.notifyListeners('system:status', status);
    });

    this.socket.on('mobile:sync:response', (data: any) => {
      console.log('🔄 Sync response received');
      this.notifyListeners('mobile:sync:response', data);
    });
  }

  // Desconectar del WebSocket
  disconnect(): void {
    if (this.socket) {
      console.log('🔌 Disconnecting WebSocket...');
      this.stopHeartbeat();
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
    }
  }

  // Verificar si está conectado
  getConnectionStatus(): boolean {
    return this.isConnected && this.socket?.connected === true;
  }

  // Enviar evento al servidor
  emit<T extends WebSocketEventName>(event: T, data?: any): void {
    if (this.socket?.connected) {
      this.socket.emit(event, data);
      console.log(`📤 Sent event: ${event}`);
    } else {
      console.warn(`⚠️ Cannot send event ${event}: WebSocket not connected`);
    }
  }

  // Escuchar eventos
  on(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, new Set());
    }
    this.eventListeners.get(event)!.add(callback);
  }

  // Dejar de escuchar eventos
  off(event: string, callback?: Function): void {
    if (callback) {
      this.eventListeners.get(event)?.delete(callback);
    } else {
      this.eventListeners.delete(event);
    }
  }

  // Notificar a los listeners
  private notifyListeners(event: string, data: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in event listener for ${event}:`, error);
        }
      });
    }
  }

  // Iniciar heartbeat
  private startHeartbeat(): void {
    this.stopHeartbeat();
    this.heartbeatInterval = setInterval(() => {
      this.emit('mobile:heartbeat');
    }, 30000); // Cada 30 segundos
  }

  // Detener heartbeat
  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  // Solicitar sincronización
  requestSync(data?: any): void {
    this.emit('mobile:sync:request', data);
  }

  // Reportar ubicación en tiempo real
  reportLocation(location: { lat: number; lng: number }): void {
    this.emit('location:update', location);
  }

  // Obtener estadísticas de conexión
  getStats(): {
    isConnected: boolean;
    reconnectAttempts: number;
    hasHeartbeat: boolean;
  } {
    return {
      isConnected: this.isConnected,
      reconnectAttempts: this.reconnectAttempts,
      hasHeartbeat: this.heartbeatInterval !== null,
    };
  }
}

// Instancia singleton del servicio WebSocket
export const webSocketService = new WebSocketService();

// Hook de React para usar WebSocket
import { useEffect, useState } from 'react';

export function useWebSocket() {
  const [isConnected, setIsConnected] = useState(false);
  const [lastEvent, setLastEvent] = useState<{ type: string; data: any } | null>(null);
  const [connectionError, setConnectionError] = useState<string | null>(null);

  useEffect(() => {
    const handleConnection = () => {
      setIsConnected(true);
      setConnectionError(null);
    };

    const handleDisconnection = () => {
      setIsConnected(false);
    };

    const handleError = (error: any) => {
      setConnectionError(error?.message || 'Connection error');
      setIsConnected(false);
    };

    const handleEvent = (type: string) => (data: any) => {
      setLastEvent({ type, data });
    };

    // Escuchar eventos de conexión
    webSocketService.on('connected', handleConnection);
    webSocketService.on('disconnected', handleDisconnection);
    webSocketService.on('error', handleError);

    // Escuchar eventos de la aplicación
    webSocketService.on('alert:created', handleEvent('alert:created'));
    webSocketService.on('alert:updated', handleEvent('alert:updated'));
    webSocketService.on('zone:updated', handleEvent('zone:updated'));
    webSocketService.on('system:status', handleEvent('system:status'));

    // Intentar conectar solo si no está conectado
    if (!webSocketService.getConnectionStatus()) {
      // Delay la conexión para evitar problemas en el render inicial
      const timer = setTimeout(() => {
        try {
          webSocketService.connect();
        } catch (error) {
          console.warn('WebSocket connection failed:', error);
          setConnectionError('Failed to connect');
        }
      }, 1000);

      return () => {
        clearTimeout(timer);
        webSocketService.off('connected', handleConnection);
        webSocketService.off('disconnected', handleDisconnection);
        webSocketService.off('error', handleError);
        webSocketService.off('alert:created');
        webSocketService.off('alert:updated');
        webSocketService.off('zone:updated');
        webSocketService.off('system:status');
      };
    }

    // Cleanup
    return () => {
      webSocketService.off('connected', handleConnection);
      webSocketService.off('disconnected', handleDisconnection);
      webSocketService.off('error', handleError);
      webSocketService.off('alert:created');
      webSocketService.off('alert:updated');
      webSocketService.off('zone:updated');
      webSocketService.off('system:status');
    };
  }, []);

  return {
    isConnected,
    lastEvent,
    connectionError,
    emit: webSocketService.emit.bind(webSocketService),
    requestSync: webSocketService.requestSync.bind(webSocketService),
    reportLocation: webSocketService.reportLocation.bind(webSocketService),
    stats: webSocketService.getStats(),
    connect: () => webSocketService.connect(),
    disconnect: () => webSocketService.disconnect(),
  };
}

export default webSocketService;
