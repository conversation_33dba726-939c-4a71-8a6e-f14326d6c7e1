import {
  Activity,
  AlertTriangle,
  Clock,
  Edit,
  MapPin,
  Plus,
  Trash2
} from 'lucide-react'
import React from 'react'
import { cn } from '../../lib/utils'
import { Badge } from '../ui/badge'
import { Button } from '../ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../ui/table'

interface Alert {
  id: string
  title: string
  message: string
  alert_level: 'NORMAL' | 'ADVISORY' | 'WATCH' | 'WARNING' | 'EMERGENCY'
  volcano_name: string
  volcano_lat: number
  volcano_lng: number
  is_active: boolean
  is_scheduled: boolean
  scheduled_for?: string
  expires_at?: string
  created_by?: string
  created_at: string
  updated_at: string
  metadata: any
}

interface AlertsTableProps {
  alerts: Alert[]
  onCreateAlert: () => void
  onEditAlert: (alert: Alert) => void
  onDeleteAlert: (id: string) => void
}

function getAlertLevelVariant(level: string): "default" | "secondary" | "destructive" | "outline" | "success" | "warning" | "info" {
  switch (level.toUpperCase()) {
    case 'NORMAL': return 'success'
    case 'ADVISORY': return 'info'
    case 'WATCH': return 'warning'
    case 'WARNING': return 'destructive'
    case 'EMERGENCY': return 'destructive'
    default: return 'default'
  }
}

function getAlertIcon(level: string) {
  switch (level.toUpperCase()) {
    case 'NORMAL': return '✅'
    case 'ADVISORY': return '🟡'
    case 'WATCH': return '🟠'
    case 'WARNING': return '🔴'
    case 'EMERGENCY': return '🚨'
    default: return '📢'
  }
}

export function AlertsTable({ alerts, onCreateAlert, onEditAlert, onDeleteAlert }: AlertsTableProps) {
  const activeAlerts = alerts.filter(alert => alert.is_active)
  const inactiveAlerts = alerts.filter(alert => !alert.is_active)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Gestión de Alertas Volcánicas</h2>
          <p className="text-muted-foreground mt-2">
            Administra las alertas del sistema de monitoreo volcánico
          </p>
        </div>
        <Button onClick={onCreateAlert} className="gap-2">
          <Plus className="h-4 w-4" />
          Nueva Alerta
        </Button>
      </div>

      {/* Estadísticas rápidas */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total de Alertas</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{alerts.length}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Alertas Activas</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{activeAlerts.length}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Alertas de Emergencia</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {alerts.filter(a => a.alert_level === 'EMERGENCY' && a.is_active).length}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Volcanes Monitoreados</CardTitle>
            <MapPin className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {new Set(alerts.map(a => a.volcano_name)).size}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tabla de alertas */}
      <Card>
        <CardHeader>
          <CardTitle>Alertas Registradas ({alerts.length})</CardTitle>
          <CardDescription>
            Lista completa de todas las alertas volcánicas del sistema
          </CardDescription>
        </CardHeader>
        <CardContent>
          {alerts.length === 0 ? (
            <div className="text-center py-12">
              <AlertTriangle className="mx-auto h-12 w-12 text-muted-foreground" />
              <h3 className="mt-4 text-lg font-semibold">No hay alertas</h3>
              <p className="text-muted-foreground mt-2">
                Comienza creando una nueva alerta volcánica.
              </p>
              <Button onClick={onCreateAlert} className="mt-4 gap-2">
                <Plus className="h-4 w-4" />
                Nueva Alerta
              </Button>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Alerta</TableHead>
                  <TableHead>Nivel</TableHead>
                  <TableHead>Volcán</TableHead>
                  <TableHead>Estado</TableHead>
                  <TableHead>Fecha</TableHead>
                  <TableHead className="text-right">Acciones</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {alerts.map((alert) => (
                  <TableRow key={alert.id}>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          <span className="text-lg">{getAlertIcon(alert.alert_level)}</span>
                          <div>
                            <p className="font-medium">{alert.title}</p>
                            <p className="text-sm text-muted-foreground line-clamp-1">
                              {alert.message}
                            </p>
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={getAlertLevelVariant(alert.alert_level)}>
                        {alert.alert_level}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <MapPin className="h-3 w-3 text-muted-foreground" />
                        <span className="text-sm">{alert.volcano_name}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <div className={cn(
                          "h-2 w-2 rounded-full",
                          alert.is_active ? "bg-green-500" : "bg-gray-400"
                        )} />
                        <span className="text-sm">
                          {alert.is_active ? 'Activa' : 'Inactiva'}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <Clock className="h-3 w-3 text-muted-foreground" />
                        <span className="text-sm">
                          {new Date(alert.created_at).toLocaleDateString()}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex items-center justify-end gap-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => onEditAlert(alert)}
                          className="h-8 w-8"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => onDeleteAlert(alert.id)}
                          className="h-8 w-8 text-destructive hover:text-destructive"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
