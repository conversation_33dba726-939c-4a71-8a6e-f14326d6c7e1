/**
 * AccessibleButton Component
 * Provides consistent, accessible button with haptic feedback and proper touch targets
 */

import React from 'react';
import {
  TouchableOpacity,
  TouchableOpacityProps,
  View,
  ActivityIndicator,
  Platform,
} from 'react-native';
import * as Haptics from 'expo-haptics';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Colors, AlertLevel, AlertLevels } from '@/constants/Colors';
import { Layout, TouchTargets } from '@/constants/Layout';
import { AccessibleText } from './AccessibleText';

export interface AccessibleButtonProps extends Omit<TouchableOpacityProps, 'style'> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'alert';
  size?: 'small' | 'medium' | 'large' | 'emergency';
  alertLevel?: AlertLevel;
  loading?: boolean;
  disabled?: boolean;
  fullWidth?: boolean;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  hapticFeedback?: boolean;
  children: React.ReactNode;
  style?: any;
}

export function AccessibleButton({
  variant = 'primary',
  size = 'medium',
  alertLevel,
  loading = false,
  disabled = false,
  fullWidth = false,
  icon,
  iconPosition = 'left',
  hapticFeedback = true,
  children,
  style,
  onPress,
  ...props
}: AccessibleButtonProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  
  // Handle press with haptic feedback
  const handlePress = (event: any) => {
    if (disabled || loading) return;
    
    if (hapticFeedback) {
      if (Platform.OS === 'ios') {
        Haptics.impactAsync(
          variant === 'primary' || alertLevel 
            ? Haptics.ImpactFeedbackStyle.Medium 
            : Haptics.ImpactFeedbackStyle.Light
        );
      }
    }
    
    onPress?.(event);
  };
  
  // Get button colors based on variant and alert level
  const getButtonColors = () => {
    if (alertLevel) {
      const alertColor = colors[`alert${AlertLevels[alertLevel].charAt(0).toUpperCase() + AlertLevels[alertLevel].slice(1)}` as keyof typeof colors];
      return {
        background: alertColor,
        text: colors.textInverse,
        border: alertColor,
      };
    }
    
    switch (variant) {
      case 'primary':
        return {
          background: colors.tint,
          text: colors.textInverse,
          border: colors.tint,
        };
      case 'secondary':
        return {
          background: colors.backgroundSecondary,
          text: colors.text,
          border: colors.border,
        };
      case 'outline':
        return {
          background: 'transparent',
          text: colors.tint,
          border: colors.tint,
        };
      case 'ghost':
        return {
          background: 'transparent',
          text: colors.tint,
          border: 'transparent',
        };
      case 'alert':
        return {
          background: colors.error,
          text: colors.textInverse,
          border: colors.error,
        };
      default:
        return {
          background: colors.tint,
          text: colors.textInverse,
          border: colors.tint,
        };
    }
  };
  
  // Get button size styles
  const getSizeStyles = () => {
    switch (size) {
      case 'small':
        return {
          minHeight: TouchTargets.minimum,
          paddingHorizontal: 16,
          paddingVertical: 8,
          borderRadius: 8,
        };
      case 'medium':
        return Layout.button;
      case 'large':
        return Layout.buttonLarge;
      case 'emergency':
        return Layout.emergencyButton;
      default:
        return Layout.button;
    }
  };
  
  const buttonColors = getButtonColors();
  const sizeStyles = getSizeStyles();
  
  // Disabled state
  const isDisabled = disabled || loading;
  const opacity = isDisabled ? 0.5 : 1;
  
  const buttonStyle = [
    sizeStyles,
    {
      backgroundColor: buttonColors.background,
      borderWidth: variant === 'outline' ? 2 : 0,
      borderColor: buttonColors.border,
      opacity,
      flexDirection: 'row' as const,
      alignItems: 'center' as const,
      justifyContent: 'center' as const,
      width: fullWidth ? '100%' : undefined,
    },
    style,
  ];
  
  // Text variant based on button size
  const getTextVariant = () => {
    switch (size) {
      case 'small':
        return 'bodySmall';
      case 'large':
      case 'emergency':
        return 'buttonLarge';
      default:
        return 'button';
    }
  };
  
  const renderContent = () => {
    if (loading) {
      return (
        <ActivityIndicator 
          size="small" 
          color={buttonColors.text}
          accessibilityLabel="Cargando"
        />
      );
    }
    
    const textElement = (
      <AccessibleText
        variant={getTextVariant() as any}
        style={{ color: buttonColors.text }}
        emergency={size === 'emergency'}
      >
        {children}
      </AccessibleText>
    );
    
    if (!icon) return textElement;
    
    return (
      <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
        {iconPosition === 'left' && icon}
        {textElement}
        {iconPosition === 'right' && icon}
      </View>
    );
  };
  
  // Accessibility props
  const accessibilityProps = {
    accessible: true,
    accessibilityRole: 'button' as const,
    accessibilityState: {
      disabled: isDisabled,
      busy: loading,
    },
    accessibilityHint: loading ? 'Cargando, por favor espere' : undefined,
    ...props,
  };
  
  return (
    <TouchableOpacity
      style={buttonStyle}
      onPress={handlePress}
      disabled={isDisabled}
      activeOpacity={0.7}
      {...accessibilityProps}
    >
      {renderContent()}
    </TouchableOpacity>
  );
}

// Convenience components for common button types
export function PrimaryButton(props: Omit<AccessibleButtonProps, 'variant'>) {
  return <AccessibleButton variant="primary" {...props} />;
}

export function SecondaryButton(props: Omit<AccessibleButtonProps, 'variant'>) {
  return <AccessibleButton variant="secondary" {...props} />;
}

export function OutlineButton(props: Omit<AccessibleButtonProps, 'variant'>) {
  return <AccessibleButton variant="outline" {...props} />;
}

export function AlertButton(props: Omit<AccessibleButtonProps, 'variant'>) {
  return <AccessibleButton variant="alert" {...props} />;
}

export function EmergencyButton(props: Omit<AccessibleButtonProps, 'variant' | 'size'>) {
  return <AccessibleButton variant="alert" size="emergency" {...props} />;
}
