{"name": "volcanoapp", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"dev": "expo start", "start": "expo start", "start:all": "node scripts/start-all.js", "dev:all": "node scripts/start-all.js", "reset-project": "node ./scripts/reset-project.js", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "lint": "expo lint", "dev:backend": "cd backoffice/backend && npm run dev", "dev:frontend": "cd backoffice/frontend && npm run dev", "dev:frontend:debug": "cd backoffice/frontend && npm run dev:debug", "dev:mobile": "expo start", "diagnose": "node scripts/diagnose-network.js", "check-network": "node scripts/diagnose-network.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:mobile": "jest", "test:backend": "cd backoffice/backend && npm test", "test:frontend": "cd backoffice/frontend && npm test", "test:all": "node scripts/run-all-tests.js", "test:all:mobile": "node scripts/run-all-tests.js mobile", "test:all:frontend": "node scripts/run-all-tests.js frontend", "test:all:backend": "node scripts/run-all-tests.js backend", "test:ci": "npm run test:mobile -- --ci --coverage --watchAll=false", "test:parallel": "npm run test:mobile & npm run test:backend & npm run test:frontend", "test:coverage:all": "npm run test:coverage && npm run test:backend -- --coverage && npm run test:frontend -- --coverage"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.1.2", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "@supabase/supabase-js": "^2.49.8", "@tanstack/react-query": "^5.59.0", "axios": "^1.7.7", "buffer": "^6.0.3", "expo": "~53.0.9", "expo-blur": "~14.1.4", "expo-constants": "~17.1.6", "expo-device": "~7.1.1", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image": "~2.1.7", "expo-linking": "~7.1.5", "expo-location": "~18.1.5", "expo-notifications": "^0.31.2", "expo-router": "~5.0.6", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.4", "expo-system-ui": "~5.0.7", "expo-web-browser": "~14.1.6", "lucide-react-native": "^0.511.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.2", "react-native-crypto-js": "^1.0.0", "react-native-gesture-handler": "~2.24.0", "react-native-get-random-values": "^1.11.0", "react-native-maps": "^1.20.1", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-svg": "15.11.2", "react-native-url-polyfill": "^2.0.0", "react-native-web": "~0.20.0", "react-native-webview": "^13.13.5", "socket.io-client": "^4.8.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@testing-library/jest-dom": "^6.6.3", "@testing-library/jest-native": "^5.4.3", "@testing-library/react": "^16.3.0", "@testing-library/react-native": "^13.2.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/react": "~19.0.10", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "happy-dom": "^18.0.1", "jest": "^30.0.0", "jest-expo": "^53.0.7", "jsdom": "^26.1.0", "react-test-renderer": "^19.0.0", "typescript": "~5.8.3"}, "private": true}