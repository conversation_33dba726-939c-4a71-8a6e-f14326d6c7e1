name: 🌋 Volcano App - Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  # Tests para la aplicación móvil
  mobile-tests:
    name: 📱 Mobile App Tests
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci --legacy-peer-deps
      
    - name: Run type check
      run: npx tsc --noEmit
      
    - name: Run linting
      run: npm run lint
      
    - name: Run mobile tests
      run: npm run test:ci
      
    - name: Upload mobile coverage
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage/lcov.info
        flags: mobile
        name: mobile-coverage

  # Tests para el frontend del backoffice
  frontend-tests:
    name: 🖥️ Frontend Tests
    runs-on: ubuntu-latest
    
    defaults:
      run:
        working-directory: ./backoffice/frontend
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: './backoffice/frontend/package-lock.json'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run type check
      run: npm run type-check
      
    - name: Run linting
      run: npm run lint
      
    - name: Run frontend tests
      run: npm run test:ci
      
    - name: Upload frontend coverage
      uses: codecov/codecov-action@v3
      with:
        file: ./backoffice/frontend/coverage/lcov.info
        flags: frontend
        name: frontend-coverage

  # Tests para el backend
  backend-tests:
    name: ⚙️ Backend Tests
    runs-on: ubuntu-latest
    
    defaults:
      run:
        working-directory: ./backoffice/backend
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: volcano_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: './backoffice/backend/package-lock.json'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run type check
      run: npm run type-check
      
    - name: Run linting
      run: npm run lint
      
    - name: Run backend tests
      run: npm run test:ci
      env:
        NODE_ENV: test
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/volcano_test
        JWT_SECRET: test-jwt-secret-for-ci
        SUPABASE_URL: https://test.supabase.co
        SUPABASE_SERVICE_ROLE_KEY: test-service-role-key
        
    - name: Upload backend coverage
      uses: codecov/codecov-action@v3
      with:
        file: ./backoffice/backend/coverage/lcov.info
        flags: backend
        name: backend-coverage

  # Tests de integración E2E (opcional)
  e2e-tests:
    name: 🔄 E2E Tests
    runs-on: ubuntu-latest
    needs: [mobile-tests, frontend-tests, backend-tests]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: |
        npm ci --legacy-peer-deps
        cd backoffice/frontend && npm ci
        cd ../backend && npm ci
        
    - name: Build applications
      run: |
        npm run build || echo "Mobile build not available"
        cd backoffice/frontend && npm run build
        cd ../backend && npm run build
        
    - name: Run E2E tests (placeholder)
      run: echo "E2E tests would run here"

  # Análisis de calidad de código
  code-quality:
    name: 📊 Code Quality
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
        
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: |
        npm ci --legacy-peer-deps
        cd backoffice/frontend && npm ci
        cd ../backend && npm ci
        
    - name: Run security audit
      run: |
        npm audit --audit-level moderate || true
        cd backoffice/frontend && npm audit --audit-level moderate || true
        cd ../backend && npm audit --audit-level moderate || true
        
    - name: Check for outdated packages
      run: |
        npm outdated || true
        cd backoffice/frontend && npm outdated || true
        cd ../backend && npm outdated || true

  # Reporte de cobertura consolidado
  coverage-report:
    name: 📈 Coverage Report
    runs-on: ubuntu-latest
    needs: [mobile-tests, frontend-tests, backend-tests]
    if: always()
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Download coverage reports
      uses: actions/download-artifact@v3
      with:
        path: ./coverage-reports
        
    - name: Generate consolidated coverage report
      run: |
        echo "## 🌋 Volcano App - Test Coverage Report" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "| Component | Status | Coverage |" >> $GITHUB_STEP_SUMMARY
        echo "|-----------|--------|----------|" >> $GITHUB_STEP_SUMMARY
        
        if [ "${{ needs.mobile-tests.result }}" == "success" ]; then
          echo "| 📱 Mobile App | ✅ Passed | [View Report](https://codecov.io/gh/${{ github.repository }}/flag/mobile) |" >> $GITHUB_STEP_SUMMARY
        else
          echo "| 📱 Mobile App | ❌ Failed | - |" >> $GITHUB_STEP_SUMMARY
        fi
        
        if [ "${{ needs.frontend-tests.result }}" == "success" ]; then
          echo "| 🖥️ Frontend | ✅ Passed | [View Report](https://codecov.io/gh/${{ github.repository }}/flag/frontend) |" >> $GITHUB_STEP_SUMMARY
        else
          echo "| 🖥️ Frontend | ❌ Failed | - |" >> $GITHUB_STEP_SUMMARY
        fi
        
        if [ "${{ needs.backend-tests.result }}" == "success" ]; then
          echo "| ⚙️ Backend | ✅ Passed | [View Report](https://codecov.io/gh/${{ github.repository }}/flag/backend) |" >> $GITHUB_STEP_SUMMARY
        else
          echo "| ⚙️ Backend | ❌ Failed | - |" >> $GITHUB_STEP_SUMMARY
        fi
        
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "### 📊 Test Summary" >> $GITHUB_STEP_SUMMARY
        echo "- **Total Jobs**: 3" >> $GITHUB_STEP_SUMMARY
        echo "- **Successful**: $(echo '${{ needs.mobile-tests.result }},${{ needs.frontend-tests.result }},${{ needs.backend-tests.result }}' | tr ',' '\n' | grep -c 'success')" >> $GITHUB_STEP_SUMMARY
        echo "- **Failed**: $(echo '${{ needs.mobile-tests.result }},${{ needs.frontend-tests.result }},${{ needs.backend-tests.result }}' | tr ',' '\n' | grep -c 'failure')" >> $GITHUB_STEP_SUMMARY
        echo "- **Commit**: ${{ github.sha }}" >> $GITHUB_STEP_SUMMARY
        echo "- **Branch**: ${{ github.ref_name }}" >> $GITHUB_STEP_SUMMARY
