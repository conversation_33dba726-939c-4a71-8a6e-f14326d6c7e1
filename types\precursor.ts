/**
 * 🌋 Volcano App - Tipos para Análisis de Precursores Volcánicos
 * Tipos TypeScript para el sistema de Índice de Aceleración de Precursores
 */

/**
 * Niveles de alerta basados en aceleración de precursores
 */
export type NivelAlerta = 'Verde' | 'Amarillo' | 'Rojo';

/**
 * Datos originales de precursores volcánicos
 */
export interface DatosPrecursor {
  /** Valores numéricos de los datos de precursores */
  valores: number[];
  /** Timestamp de cada medición */
  timestamps?: Date[];
  /** Tipo de precursor (sísmica, deformación, gases, etc.) */
  tipo?: string;
  /** Unidad de medida */
  unidad?: string;
  /** Nombre del volcán */
  volcan?: string;
}

/**
 * Resultado del análisis matemático de precursores
 */
export interface AnalisisPrecursor {
  /** Datos originales */
  datosOriginales: number[];
  /** Primera derivada (tasa de cambio) */
  primeraderivada: (number | null)[];
  /** Segunda derivada (aceleración) */
  segundaDerivada: (number | null)[];
  /** Timestamps correspondientes */
  timestamps?: Date[];
}

/**
 * Resultado de la determinación del nivel de alerta
 */
export interface ResultadoAlerta {
  /** Nivel de alerta determinado */
  nivel: NivelAlerta;
  /** Mensaje descriptivo del nivel */
  mensaje: string;
  /** Valor de la segunda derivada que determinó el nivel */
  valor: number | null;
  /** Timestamp del análisis */
  timestamp: Date;
  /** Índice en el array donde se detectó el valor crítico */
  indice?: number;
}

/**
 * Configuración de umbrales para alertas
 */
export interface UmbralesAlerta {
  /** Umbral para nivel Verde (≤ valor) */
  verde: number;
  /** Umbral para nivel Amarillo (> verde y ≤ valor) */
  amarillo: number;
  /** Umbral para nivel Rojo (> amarillo) */
  rojo: number;
}

/**
 * Configuración por defecto de umbrales
 */
export const UMBRALES_DEFAULT: UmbralesAlerta = {
  verde: 1,
  amarillo: 5,
  rojo: 5, // Cualquier valor > 5 es Rojo
};

/**
 * Mensajes por defecto para cada nivel de alerta
 */
export const MENSAJES_ALERTA: Record<NivelAlerta, string> = {
  Verde: 'Actividad estable',
  Amarillo: 'Precaución: La actividad está acelerando',
  Rojo: 'Alerta: Aceleración peligrosa detectada',
};

/**
 * Colores asociados a cada nivel de alerta
 */
export const COLORES_ALERTA: Record<NivelAlerta, string> = {
  Verde: '#22c55e',   // green-500
  Amarillo: '#eab308', // yellow-500
  Rojo: '#ef4444',    // red-500
};

/**
 * Props para el componente de visualización de precursores
 */
export interface PrecursorAccelerationProps {
  /** Datos de precursores a analizar */
  datos: DatosPrecursor;
  /** Configuración personalizada de umbrales */
  umbrales?: UmbralesAlerta;
  /** Callback cuando se detecta una alerta */
  onAlerta?: (resultado: ResultadoAlerta) => void;
  /** Título del gráfico */
  titulo?: string;
  /** Altura del componente */
  altura?: number;
  /** Si debe mostrar los controles de configuración */
  mostrarControles?: boolean;
}

/**
 * Configuración de visualización para gráficos
 */
export interface ConfiguracionGrafico {
  /** Mostrar líneas de tendencia */
  mostrarTendencia: boolean;
  /** Mostrar puntos en los gráficos */
  mostrarPuntos: boolean;
  /** Suavizar las líneas */
  suavizar: boolean;
  /** Colores personalizados */
  colores?: {
    datosOriginales?: string;
    primeraderivada?: string;
    segundaDerivada?: string;
  };
}

/**
 * Estado del componente de análisis de precursores
 */
export interface EstadoPrecursorAnalysis {
  /** Datos cargados */
  datos: DatosPrecursor | null;
  /** Resultado del análisis */
  analisis: AnalisisPrecursor | null;
  /** Resultado de la alerta */
  alerta: ResultadoAlerta | null;
  /** Estado de carga */
  cargando: boolean;
  /** Error si existe */
  error: string | null;
  /** Configuración de visualización */
  configuracion: ConfiguracionGrafico;
}

/**
 * Datos para pruebas y testing
 */
export const DATOS_PRUEBA: DatosPrecursor = {
  valores: [2, 3, 3, 4, 6, 9, 14, 22, 35, 50, 68],
  tipo: 'Actividad Sísmica',
  unidad: 'Eventos/hora',
  volcan: 'Volcán de Prueba',
};

/**
 * Opciones para el análisis de precursores
 */
export interface OpcionesAnalisis {
  /** Usar umbrales personalizados */
  umbrales?: UmbralesAlerta;
  /** Incluir timestamps en el análisis */
  incluirTimestamps?: boolean;
  /** Validar datos antes del análisis */
  validarDatos?: boolean;
  /** Filtrar valores nulos o inválidos */
  filtrarInvalidos?: boolean;
}
