/**
 * ModernIcon Component
 * Wrapper para iconos modernos usando Lucide React Native
 * Proporciona iconos consistentes y accesibles para toda la app
 */

import React from 'react';
import { ViewStyle } from 'react-native';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Colors } from '@/constants/Colors';

// Importar iconos específicos de Lucide
import {
  Home,
  Map,
  MapPin,
  Mountain,
  Shield,
  AlertTriangle,
  Navigation,
  Compass,
  Route,
  Users,
  Phone,
  RefreshCw,
  Settings,
  Bell,
  Info,
  CheckCircle,
  XCircle,
  AlertCircle,
  Zap,
  Eye,
  EyeOff,
  ChevronRight,
  ChevronLeft,
  ChevronUp,
  ChevronDown,
  Plus,
  Minus,
  X,
  Check,
  Search,
  Filter,
  Download,
  Upload,
  Share,
  Heart,
  Star,
  Bookmark,
  Calendar,
  Clock,
  User,
  Mail,
  Lock,
  Unlock,
  Camera,
  Image,
  Video,
  Mic,
  Volume2,
  VolumeX,
  Wifi,
  WifiOff,
  Battery,
  BatteryLow,
  Signal,
  Bluetooth,
  Gps,
  Target,
  Crosshair,
  Layers,
  Globe,
  Satellite,
  Car,
  Truck,
  Plane,
  Ship,
  Train,
  Bike,
  Walk,
  Run,
  Activity,
  TrendingUp,
  TrendingDown,
  BarChart,
  PieChart,
  LineChart,
  Database,
  Server,
  Cloud,
  CloudOff,
  Folder,
  File,
  FileText,
  Image as ImageIcon,
  Video as VideoIcon,
  Music,
  Headphones,
  Gamepad2,
  Tv,
  Monitor,
  Smartphone,
  Tablet,
  Laptop,
  Desktop,
  Watch,
  Printer,
  Scanner,
  Keyboard,
  Mouse,
  Cpu,
  HardDrive,
  MemoryStick,
  Usb,
  Bluetooth as BluetoothIcon,
  Wifi as WifiIcon,
} from 'lucide-react-native';

// Mapeo de nombres de iconos a componentes
const iconMap = {
  // Navegación y UI básica
  home: Home,
  map: Map,
  'map-pin': MapPin,
  mountain: Mountain,
  shield: Shield,
  'alert-triangle': AlertTriangle,
  navigation: Navigation,
  compass: Compass,
  route: Route,
  users: Users,
  phone: Phone,
  'refresh-cw': RefreshCw,
  settings: Settings,
  bell: Bell,
  info: Info,
  
  // Estados y feedback
  'check-circle': CheckCircle,
  'x-circle': XCircle,
  'alert-circle': AlertCircle,
  zap: Zap,
  eye: Eye,
  'eye-off': EyeOff,
  
  // Navegación direccional
  'chevron-right': ChevronRight,
  'chevron-left': ChevronLeft,
  'chevron-up': ChevronUp,
  'chevron-down': ChevronDown,
  
  // Acciones básicas
  plus: Plus,
  minus: Minus,
  x: X,
  check: Check,
  search: Search,
  filter: Filter,
  download: Download,
  upload: Upload,
  share: Share,
  
  // Interacciones sociales
  heart: Heart,
  star: Star,
  bookmark: Bookmark,
  
  // Tiempo y calendario
  calendar: Calendar,
  clock: Clock,
  
  // Usuario y autenticación
  user: User,
  mail: Mail,
  lock: Lock,
  unlock: Unlock,
  
  // Media
  camera: Camera,
  image: ImageIcon,
  video: VideoIcon,
  mic: Mic,
  'volume-2': Volume2,
  'volume-x': VolumeX,
  music: Music,
  headphones: Headphones,
  
  // Conectividad
  wifi: WifiIcon,
  'wifi-off': WifiOff,
  battery: Battery,
  'battery-low': BatteryLow,
  signal: Signal,
  bluetooth: BluetoothIcon,
  
  // Ubicación y mapas
  gps: Gps,
  target: Target,
  crosshair: Crosshair,
  layers: Layers,
  globe: Globe,
  satellite: Satellite,
  
  // Transporte
  car: Car,
  truck: Truck,
  plane: Plane,
  ship: Ship,
  train: Train,
  bike: Bike,
  walk: Walk,
  run: Run,
  
  // Datos y análisis
  activity: Activity,
  'trending-up': TrendingUp,
  'trending-down': TrendingDown,
  'bar-chart': BarChart,
  'pie-chart': PieChart,
  'line-chart': LineChart,
  
  // Tecnología
  database: Database,
  server: Server,
  cloud: Cloud,
  'cloud-off': CloudOff,
  
  // Archivos
  folder: Folder,
  file: File,
  'file-text': FileText,
  
  // Dispositivos
  gamepad2: Gamepad2,
  tv: Tv,
  monitor: Monitor,
  smartphone: Smartphone,
  tablet: Tablet,
  laptop: Laptop,
  desktop: Desktop,
  watch: Watch,
  printer: Printer,
  scanner: Scanner,
  keyboard: Keyboard,
  mouse: Mouse,
  cpu: Cpu,
  'hard-drive': HardDrive,
  'memory-stick': MemoryStick,
  usb: Usb,
} as const;

export type IconName = keyof typeof iconMap;

export interface ModernIconProps {
  name: IconName;
  size?: number;
  color?: string;
  style?: ViewStyle;
  strokeWidth?: number;
}

export function ModernIcon({
  name,
  size = 24,
  color,
  style,
  strokeWidth = 2,
}: ModernIconProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  
  // Obtener el componente del icono
  const IconComponent = iconMap[name];
  
  if (!IconComponent) {
    console.warn(`Icon "${name}" not found in iconMap`);
    return null;
  }
  
  // Color por defecto basado en el tema
  const defaultColor = color || colors.text;
  
  return (
    <IconComponent
      size={size}
      color={defaultColor}
      strokeWidth={strokeWidth}
      style={style}
    />
  );
}

// Componentes de conveniencia para iconos comunes
export function HomeIcon(props: Omit<ModernIconProps, 'name'>) {
  return <ModernIcon name="home" {...props} />;
}

export function MapIcon(props: Omit<ModernIconProps, 'name'>) {
  return <ModernIcon name="map" {...props} />;
}

export function VolcanoIcon(props: Omit<ModernIconProps, 'name'>) {
  return <ModernIcon name="mountain" {...props} />;
}

export function ShieldIcon(props: Omit<ModernIconProps, 'name'>) {
  return <ModernIcon name="shield" {...props} />;
}

export function AlertIcon(props: Omit<ModernIconProps, 'name'>) {
  return <ModernIcon name="alert-triangle" {...props} />;
}

export function NavigationIcon(props: Omit<ModernIconProps, 'name'>) {
  return <ModernIcon name="navigation" {...props} />;
}

export function PhoneIcon(props: Omit<ModernIconProps, 'name'>) {
  return <ModernIcon name="phone" {...props} />;
}

export function SettingsIcon(props: Omit<ModernIconProps, 'name'>) {
  return <ModernIcon name="settings" {...props} />;
}

export function RefreshIcon(props: Omit<ModernIconProps, 'name'>) {
  return <ModernIcon name="refresh-cw" {...props} />;
}

export function LocationIcon(props: Omit<ModernIconProps, 'name'>) {
  return <ModernIcon name="map-pin" {...props} />;
}

export function CompassIcon(props: Omit<ModernIconProps, 'name'>) {
  return <ModernIcon name="compass" {...props} />;
}

export function RouteIcon(props: Omit<ModernIconProps, 'name'>) {
  return <ModernIcon name="route" {...props} />;
}

export function UsersIcon(props: Omit<ModernIconProps, 'name'>) {
  return <ModernIcon name="users" {...props} />;
}

export function BellIcon(props: Omit<ModernIconProps, 'name'>) {
  return <ModernIcon name="bell" {...props} />;
}

export function InfoIcon(props: Omit<ModernIconProps, 'name'>) {
  return <ModernIcon name="info" {...props} />;
}

// Iconos con colores semánticos predefinidos
export function SuccessIcon(props: Omit<ModernIconProps, 'name' | 'color'>) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  return <ModernIcon name="check-circle" color={colors.success} {...props} />;
}

export function ErrorIcon(props: Omit<ModernIconProps, 'name' | 'color'>) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  return <ModernIcon name="x-circle" color={colors.error} {...props} />;
}

export function WarningIcon(props: Omit<ModernIconProps, 'name' | 'color'>) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  return <ModernIcon name="alert-circle" color={colors.warning} {...props} />;
}
