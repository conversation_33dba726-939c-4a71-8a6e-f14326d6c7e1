/**
 * 🌋 Volcano App Backoffice - Estadísticas de Zonas en Mapa
 * Componente para mostrar estadísticas y métricas de las zonas dibujadas
 */

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { MapPin, Ruler, Shield, AlertTriangle } from 'lucide-react';
import { formatArea, formatDistance, ZoneGeometry } from '@/services/geometry';

// =====================================================
// TIPOS Y INTERFACES
// =====================================================

interface Zone {
  id: string;
  name: string;
  description: string;
  zone_type: 'SAFE' | 'EMERGENCY' | 'DANGER' | 'EVACUATION' | 'RESTRICTED';
  geometry: ZoneGeometry;
  is_active: boolean;
}

interface MapZoneStatsProps {
  zones: Zone[];
  selectedZone?: Zone | null;
  tempGeometry?: ZoneGeometry | null;
}

// =====================================================
// COMPONENTE PRINCIPAL
// =====================================================

export function MapZoneStats({ zones, selectedZone, tempGeometry }: MapZoneStatsProps) {
  // Calcular estadísticas
  const stats = calculateZoneStats(zones);
  
  return (
    <div className="space-y-4">
      {/* Estadísticas generales */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm flex items-center gap-2">
            <MapPin className="h-4 w-4" />
            Resumen de Zonas
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="grid grid-cols-2 gap-3">
            <div className="text-center">
              <div className="text-lg font-bold text-blue-600">{stats.total}</div>
              <div className="text-xs text-gray-500">Total</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-green-600">{stats.active}</div>
              <div className="text-xs text-gray-500">Activas</div>
            </div>
          </div>
          
          <div className="space-y-2">
            <div className="text-xs font-medium text-gray-700">Por Tipo:</div>
            <div className="flex flex-wrap gap-1">
              {Object.entries(stats.byType).map(([type, count]) => (
                <Badge 
                  key={type} 
                  variant="outline" 
                  className={`text-xs ${getZoneTypeColor(type)}`}
                >
                  {getZoneTypeLabel(type)}: {count}
                </Badge>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Área total */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm flex items-center gap-2">
            <Ruler className="h-4 w-4" />
            Métricas
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Área Total:</span>
              <span className="font-medium">{formatArea(stats.totalArea)}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Área Promedio:</span>
              <span className="font-medium">{formatArea(stats.averageArea)}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Zona seleccionada */}
      {selectedZone && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm flex items-center gap-2">
              <Shield className="h-4 w-4" />
              Zona Seleccionada
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="font-medium text-sm">{selectedZone.name}</div>
              <Badge className={getZoneTypeColor(selectedZone.zone_type)}>
                {getZoneTypeLabel(selectedZone.zone_type)}
              </Badge>
              <div className="text-xs text-gray-600 space-y-1">
                <div>Área: {formatArea(calculateZoneArea(selectedZone.geometry))}</div>
                <div>Estado: {selectedZone.is_active ? 'Activa' : 'Inactiva'}</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Geometría temporal */}
      {tempGeometry && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm flex items-center gap-2">
              <AlertTriangle className="h-4 w-4 text-yellow-500" />
              Zona en Creación
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="text-sm text-gray-600">
                Área: {formatArea(calculateZoneArea(tempGeometry))}
              </div>
              <div className="text-xs text-yellow-600">
                Confirma para guardar esta zona
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Leyenda de colores */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm">Leyenda</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {[
              { type: 'SAFE', color: '#22C55E', label: 'Segura' },
              { type: 'EMERGENCY', color: '#3B82F6', label: 'Emergencia' },
              { type: 'EVACUATION', color: '#F59E0B', label: 'Evacuación' },
              { type: 'DANGER', color: '#EF4444', label: 'Peligro' },
              { type: 'RESTRICTED', color: '#6B7280', label: 'Restringida' }
            ].map(({ type, color, label }) => (
              <div key={type} className="flex items-center gap-2 text-xs">
                <div 
                  className="w-3 h-3 rounded-full border"
                  style={{ backgroundColor: color }}
                />
                <span>{label}</span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

// =====================================================
// FUNCIONES AUXILIARES
// =====================================================

function calculateZoneStats(zones: Zone[]) {
  const total = zones.length;
  const active = zones.filter(z => z.is_active).length;
  
  const byType = zones.reduce((acc, zone) => {
    acc[zone.zone_type] = (acc[zone.zone_type] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const totalArea = zones.reduce((sum, zone) => {
    return sum + calculateZoneArea(zone.geometry);
  }, 0);

  const averageArea = total > 0 ? totalArea / total : 0;

  return {
    total,
    active,
    byType,
    totalArea,
    averageArea
  };
}

function calculateZoneArea(geometry: ZoneGeometry): number {
  try {
    const coordinates = geometry.coordinates[0];
    if (coordinates.length < 4) return 0;

    let area = 0;
    const earthRadius = 6371000; // Radio de la Tierra en metros

    for (let i = 0; i < coordinates.length - 1; i++) {
      const [lng1, lat1] = coordinates[i];
      const [lng2, lat2] = coordinates[i + 1];
      
      const lat1Rad = lat1 * Math.PI / 180;
      const lat2Rad = lat2 * Math.PI / 180;
      const deltaLng = (lng2 - lng1) * Math.PI / 180;
      
      area += deltaLng * (2 + Math.sin(lat1Rad) + Math.sin(lat2Rad));
    }
    
    area = Math.abs(area * earthRadius * earthRadius / 2);
    return area;
  } catch (error) {
    console.error('Error calculating zone area:', error);
    return 0;
  }
}

function getZoneTypeColor(type: string): string {
  switch (type) {
    case 'SAFE': return 'bg-green-100 text-green-800';
    case 'EMERGENCY': return 'bg-blue-100 text-blue-800';
    case 'EVACUATION': return 'bg-yellow-100 text-yellow-800';
    case 'DANGER': return 'bg-red-100 text-red-800';
    case 'RESTRICTED': return 'bg-gray-100 text-gray-800';
    default: return 'bg-gray-100 text-gray-800';
  }
}

function getZoneTypeLabel(type: string): string {
  switch (type) {
    case 'SAFE': return 'Segura';
    case 'EMERGENCY': return 'Emergencia';
    case 'EVACUATION': return 'Evacuación';
    case 'DANGER': return 'Peligro';
    case 'RESTRICTED': return 'Restringida';
    default: return type;
  }
}

export default MapZoneStats;
