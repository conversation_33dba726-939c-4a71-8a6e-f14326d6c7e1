const { getDefaultConfig } = require('expo/metro-config');

/** @type {import('expo/metro-config').MetroConfig} */
const config = getDefaultConfig(__dirname);

// Configuración básica para React Native
config.resolver.alias = {
  ...config.resolver.alias,
  // Solo los polyfills esenciales
  crypto: 'react-native-get-random-values',
  stream: false, // Deshabilitar stream
  buffer: 'buffer',
};

// Configuración para resolver módulos
config.resolver.platforms = ['native', 'ios', 'android', 'web'];

// Configuración para transformar archivos
config.transformer = {
  ...config.transformer,
  getTransformOptions: async () => ({
    transform: {
      experimentalImportSupport: false,
      inlineRequires: true,
    },
  }),
};

module.exports = config;
