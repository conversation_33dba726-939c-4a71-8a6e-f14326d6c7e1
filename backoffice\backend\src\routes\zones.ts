/**
 * 🌋 Volcano App Backend - Rutas de Zonas de Seguridad
 * Endpoints para gestión completa de zonas de seguridad
 */

import { Router } from 'express';
import { requireMinimumRole } from '@/middleware/auth';
import { 
  validateCreateZone, 
  validateUpdateZone, 
  validateZoneId,
  validatePagination,
  validateSearch
} from '@/middleware/validation';
import { asyncHandler } from '@/middleware/errorHandler';
import { UserRole } from '@/types';

// Importar controladores
import {
  getZones,
  getZoneById,
  createZone,
  updateZone,
  deleteZone,
  getActiveZones
} from '@/controllers/zones';

// =====================================================
// ROUTER DE ZONAS
// =====================================================

const router = Router();

// =====================================================
// RUTAS DE CONSULTA
// =====================================================

/**
 * Obtener todas las zonas con paginación y filtros
 * GET /zones
 */
router.get('/', 
  validatePagination,
  validateSearch,
  asyncHandler(getZones)
);

/**
 * Obtener zonas activas (para app móvil)
 * GET /zones/active
 */
router.get('/active', 
  asyncHandler(getActiveZones)
);

/**
 * Obtener una zona específica por ID
 * GET /zones/:id
 */
router.get('/:id', 
  validateZoneId,
  asyncHandler(getZoneById)
);

// =====================================================
// RUTAS DE MODIFICACIÓN (REQUIEREN PERMISOS)
// =====================================================

/**
 * Crear nueva zona de seguridad
 * POST /zones
 * Requiere rol mínimo: OPERATOR
 */
router.post('/', 
  requireMinimumRole(UserRole.OPERATOR),
  validateCreateZone,
  asyncHandler(createZone)
);

/**
 * Actualizar zona existente
 * PUT /zones/:id
 * Requiere rol mínimo: OPERATOR
 */
router.put('/:id', 
  requireMinimumRole(UserRole.OPERATOR),
  validateUpdateZone,
  asyncHandler(updateZone)
);

/**
 * Eliminar zona (soft delete)
 * DELETE /zones/:id
 * Requiere rol mínimo: OPERATOR
 */
router.delete('/:id', 
  requireMinimumRole(UserRole.OPERATOR),
  validateZoneId,
  asyncHandler(deleteZone)
);

// =====================================================
// RUTAS DE INFORMACIÓN
// =====================================================

/**
 * Información sobre zonas
 * GET /zones/_info
 */
router.get('/_info', (req, res) => {
  res.json({
    success: true,
    data: {
      endpoints: {
        list: 'GET /api/zones',
        get: 'GET /api/zones/:id',
        create: 'POST /api/zones',
        update: 'PUT /api/zones/:id',
        delete: 'DELETE /api/zones/:id',
        active: 'GET /api/zones/active'
      },
      zone_types: [
        'SAFE',
        'EMERGENCY',
        'DANGER',
        'EVACUATION',
        'RESTRICTED'
      ],
      permissions: {
        view: 'All authenticated users',
        create: 'OPERATOR or higher',
        update: 'OPERATOR or higher',
        delete: 'OPERATOR or higher'
      },
      pagination: {
        default_page: 1,
        default_limit: 20,
        max_limit: 100
      },
      filters: {
        search: 'Search in name and description',
        zone_type: 'Filter by zone type',
        is_active: 'Filter by active status'
      },
      sorting: {
        fields: ['created_at', 'updated_at', 'zone_type', 'name'],
        orders: ['asc', 'desc']
      },
      geometry: {
        supported_types: ['Point', 'Polygon', 'LineString'],
        format: 'GeoJSON',
        coordinate_system: 'WGS84 (EPSG:4326)'
      }
    },
    timestamp: new Date()
  });
});

// =====================================================
// EXPORTACIÓN
// =====================================================

export default router;
