/**
 * 🌋 Volcano App Backend - Tests para servicio de WebSocket
 * Tests unitarios para el servicio de WebSocket y notificaciones en tiempo real
 */

import { Server as SocketIOServer } from 'socket.io';
import { createServer } from 'http';
import { jest } from '@jest/globals';
import { WebSocketService } from '@/services/websocket';

// Mock para socket.io
jest.mock('socket.io', () => ({
  Server: jest.fn().mockImplementation(() => ({
    on: jest.fn(),
    emit: jest.fn(),
    to: jest.fn().mockReturnThis(),
    join: jest.fn(),
    leave: jest.fn(),
    use: jest.fn(),
    close: jest.fn()
  }))
}));

// Mock para http server
const mockHttpServer = createServer();

describe('WebSocket Service', () => {
  let webSocketService: WebSocketService;
  let mockIo: any;

  beforeEach(() => {
    jest.clearAllMocks();
    
    mockIo = {
      on: jest.fn(),
      emit: jest.fn(),
      to: jest.fn().mockReturnThis(),
      join: jest.fn(),
      leave: jest.fn(),
      use: jest.fn(),
      close: jest.fn(),
      sockets: {
        sockets: new Map()
      }
    };

    (SocketIOServer as jest.MockedClass<typeof SocketIOServer>).mockImplementation(() => mockIo);
    
    webSocketService = new WebSocketService(mockHttpServer);
  });

  describe('Initialization', () => {
    it('should initialize WebSocket server correctly', () => {
      expect(SocketIOServer).toHaveBeenCalledWith(mockHttpServer, {
        cors: {
          origin: expect.any(Array),
          methods: ['GET', 'POST']
        },
        transports: ['websocket', 'polling']
      });
    });

    it('should set up connection handler', () => {
      expect(mockIo.on).toHaveBeenCalledWith('connection', expect.any(Function));
    });

    it('should set up authentication middleware', () => {
      expect(mockIo.use).toHaveBeenCalledWith(expect.any(Function));
    });
  });

  describe('Connection Handling', () => {
    let mockSocket: any;

    beforeEach(() => {
      mockSocket = {
        id: 'socket-123',
        handshake: {
          auth: {
            token: 'valid-jwt-token'
          },
          headers: {
            'user-agent': 'Test Client'
          }
        },
        user: {
          id: 'user-123',
          email: '<EMAIL>',
          role: 'ADMIN'
        },
        on: jest.fn(),
        emit: jest.fn(),
        join: jest.fn(),
        leave: jest.fn(),
        disconnect: jest.fn()
      };
    });

    it('should handle new connection correctly', () => {
      const connectionHandler = mockIo.on.mock.calls.find(call => call[0] === 'connection')[1];
      
      connectionHandler(mockSocket);

      expect(mockSocket.on).toHaveBeenCalledWith('join-room', expect.any(Function));
      expect(mockSocket.on).toHaveBeenCalledWith('leave-room', expect.any(Function));
      expect(mockSocket.on).toHaveBeenCalledWith('disconnect', expect.any(Function));
    });

    it('should handle room joining', () => {
      const connectionHandler = mockIo.on.mock.calls.find(call => call[0] === 'connection')[1];
      connectionHandler(mockSocket);

      const joinRoomHandler = mockSocket.on.mock.calls.find((call: any) => call[0] === 'join-room')[1];
      joinRoomHandler('alerts-room');

      expect(mockSocket.join).toHaveBeenCalledWith('alerts-room');
      expect(mockSocket.emit).toHaveBeenCalledWith('room-joined', { room: 'alerts-room' });
    });

    it('should handle room leaving', () => {
      const connectionHandler = mockIo.on.mock.calls.find(call => call[0] === 'connection')[1];
      connectionHandler(mockSocket);

      const leaveRoomHandler = mockSocket.on.mock.calls.find((call: any) => call[0] === 'leave-room')[1];
      leaveRoomHandler('alerts-room');

      expect(mockSocket.leave).toHaveBeenCalledWith('alerts-room');
      expect(mockSocket.emit).toHaveBeenCalledWith('room-left', { room: 'alerts-room' });
    });

    it('should handle disconnection', () => {
      const connectionHandler = mockIo.on.mock.calls.find(call => call[0] === 'connection')[1];
      connectionHandler(mockSocket);

      const disconnectHandler = mockSocket.on.mock.calls.find((call: any) => call[0] === 'disconnect')[1];
      disconnectHandler('client disconnect');

      // Verificar que se limpian los recursos
      expect(mockSocket.leave).toHaveBeenCalled();
    });
  });

  describe('Authentication Middleware', () => {
    let mockSocket: any;
    let mockNext: jest.MockedFunction<any>;

    beforeEach(() => {
      mockSocket = {
        handshake: {
          auth: {},
          headers: {}
        }
      };
      mockNext = jest.fn();
    });

    it('should authenticate valid token', () => {
      mockSocket.handshake.auth.token = 'valid-jwt-token';
      
      // Mock JWT verification
      const jwt = require('jsonwebtoken');
      jest.spyOn(jwt, 'verify').mockReturnValue({
        userId: 'user-123',
        email: '<EMAIL>'
      });

      const authMiddleware = mockIo.use.mock.calls[0][0];
      authMiddleware(mockSocket, mockNext);

      expect(mockNext).toHaveBeenCalledWith();
    });

    it('should reject invalid token', () => {
      mockSocket.handshake.auth.token = 'invalid-token';
      
      const jwt = require('jsonwebtoken');
      jest.spyOn(jwt, 'verify').mockImplementation(() => {
        throw new Error('Invalid token');
      });

      const authMiddleware = mockIo.use.mock.calls[0][0];
      authMiddleware(mockSocket, mockNext);

      expect(mockNext).toHaveBeenCalledWith(expect.any(Error));
    });

    it('should reject missing token', () => {
      const authMiddleware = mockIo.use.mock.calls[0][0];
      authMiddleware(mockSocket, mockNext);

      expect(mockNext).toHaveBeenCalledWith(expect.any(Error));
    });
  });

  describe('Broadcasting', () => {
    it('should broadcast alert notification', () => {
      const alertData = {
        id: '1',
        title: 'Alerta Volcánica',
        alert_level: 'YELLOW',
        message: 'Actividad volcánica detectada'
      };

      webSocketService.broadcastAlert(alertData);

      expect(mockIo.to).toHaveBeenCalledWith('alerts-room');
      expect(mockIo.emit).toHaveBeenCalledWith('alert-notification', alertData);
    });

    it('should broadcast zone update', () => {
      const zoneData = {
        id: '1',
        name: 'Zona Actualizada',
        zone_type: 'SAFE',
        is_active: true
      };

      webSocketService.broadcastZoneUpdate(zoneData);

      expect(mockIo.to).toHaveBeenCalledWith('zones-room');
      expect(mockIo.emit).toHaveBeenCalledWith('zone-update', zoneData);
    });

    it('should broadcast system notification', () => {
      const notification = {
        type: 'system',
        title: 'Mantenimiento Programado',
        message: 'El sistema estará en mantenimiento',
        priority: 'medium'
      };

      webSocketService.broadcastSystemNotification(notification);

      expect(mockIo.emit).toHaveBeenCalledWith('system-notification', notification);
    });

    it('should send notification to specific user', () => {
      const userId = 'user-123';
      const notification = {
        type: 'personal',
        title: 'Notificación Personal',
        message: 'Mensaje específico para el usuario'
      };

      webSocketService.sendToUser(userId, notification);

      expect(mockIo.to).toHaveBeenCalledWith(`user-${userId}`);
      expect(mockIo.emit).toHaveBeenCalledWith('personal-notification', notification);
    });
  });

  describe('Room Management', () => {
    it('should get connected clients count', () => {
      // Mock sockets map
      mockIo.sockets.sockets = new Map([
        ['socket-1', { id: 'socket-1' }],
        ['socket-2', { id: 'socket-2' }],
        ['socket-3', { id: 'socket-3' }]
      ]);

      const count = webSocketService.getConnectedClientsCount();

      expect(count).toBe(3);
    });

    it('should get clients in specific room', () => {
      const mockRoom = new Set(['socket-1', 'socket-2']);
      mockIo.sockets.adapter = {
        rooms: new Map([
          ['alerts-room', mockRoom]
        ])
      };

      const clients = webSocketService.getClientsInRoom('alerts-room');

      expect(clients).toBe(2);
    });

    it('should handle room statistics', () => {
      mockIo.sockets.adapter = {
        rooms: new Map([
          ['alerts-room', new Set(['socket-1', 'socket-2'])],
          ['zones-room', new Set(['socket-1', 'socket-3'])],
          ['user-123', new Set(['socket-1'])]
        ])
      };

      const stats = webSocketService.getRoomStatistics();

      expect(stats).toEqual({
        'alerts-room': 2,
        'zones-room': 2,
        'user-123': 1
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle broadcast errors gracefully', () => {
      mockIo.emit.mockImplementation(() => {
        throw new Error('Broadcast failed');
      });

      expect(() => {
        webSocketService.broadcastAlert({
          id: '1',
          title: 'Test Alert',
          alert_level: 'YELLOW'
        });
      }).not.toThrow();
    });

    it('should handle connection errors', () => {
      const mockSocket = {
        id: 'socket-123',
        handshake: { auth: {}, headers: {} },
        on: jest.fn().mockImplementation((event, handler) => {
          if (event === 'error') {
            handler(new Error('Socket error'));
          }
        }),
        emit: jest.fn(),
        join: jest.fn(),
        leave: jest.fn(),
        disconnect: jest.fn()
      };

      const connectionHandler = mockIo.on.mock.calls.find(call => call[0] === 'connection')[1];
      
      expect(() => {
        connectionHandler(mockSocket);
      }).not.toThrow();
    });
  });

  describe('Cleanup', () => {
    it('should close WebSocket server', () => {
      webSocketService.close();

      expect(mockIo.close).toHaveBeenCalled();
    });

    it('should handle cleanup errors', () => {
      mockIo.close.mockImplementation(() => {
        throw new Error('Close failed');
      });

      expect(() => {
        webSocketService.close();
      }).not.toThrow();
    });
  });

  describe('Health Check', () => {
    it('should provide health status', () => {
      mockIo.sockets.sockets = new Map([
        ['socket-1', { id: 'socket-1' }],
        ['socket-2', { id: 'socket-2' }]
      ]);

      const health = webSocketService.getHealthStatus();

      expect(health).toEqual({
        status: 'healthy',
        connectedClients: 2,
        uptime: expect.any(Number),
        memoryUsage: expect.any(Object)
      });
    });

    it('should detect unhealthy status', () => {
      // Simular estado no saludable
      mockIo.sockets = null;

      const health = webSocketService.getHealthStatus();

      expect(health.status).toBe('unhealthy');
    });
  });
});
