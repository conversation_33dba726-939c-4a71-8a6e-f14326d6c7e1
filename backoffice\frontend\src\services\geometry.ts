/**
 * 🌋 Volcano App Backoffice - Servicios de Geometría
 * Utilidades para manejo de geometrías GeoJSON y validaciones
 */

import L from 'leaflet';

// =====================================================
// TIPOS Y INTERFACES
// =====================================================

export interface GeoJSONGeometry {
  type: 'Polygon' | 'Point' | 'LineString' | 'MultiPolygon';
  coordinates: number[][] | number[][][] | number[][][][];
}

export interface ZoneGeometry extends GeoJSONGeometry {
  type: 'Polygon';
  coordinates: number[][][];
}

export interface GeometryValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  area?: number;
  perimeter?: number;
}

// =====================================================
// CONSTANTES
// =====================================================

// Coordenadas del Volcán Villarrica para referencia
export const VOLCANO_COORDS = {
  lat: -39.420000,
  lng: -71.939167
};

// Límites aproximados de la región (para validación)
export const REGION_BOUNDS = {
  north: -38.5,
  south: -40.5,
  east: -71.0,
  west: -73.0
};

// =====================================================
// FUNCIONES DE VALIDACIÓN
// =====================================================

/**
 * Valida una geometría GeoJSON
 */
export function validateGeometry(geometry: GeoJSONGeometry): GeometryValidationResult {
  const result: GeometryValidationResult = {
    isValid: true,
    errors: [],
    warnings: []
  };

  try {
    // Validar estructura básica
    if (!geometry || !geometry.type || !geometry.coordinates) {
      result.isValid = false;
      result.errors.push('Geometría incompleta: falta tipo o coordenadas');
      return result;
    }

    // Validar tipo de geometría
    if (geometry.type !== 'Polygon') {
      result.isValid = false;
      result.errors.push(`Tipo de geometría no soportado: ${geometry.type}`);
      return result;
    }

    // Validar coordenadas de polígono
    const coords = geometry.coordinates as number[][][];
    if (!Array.isArray(coords) || coords.length === 0) {
      result.isValid = false;
      result.errors.push('Coordenadas de polígono inválidas');
      return result;
    }

    // Validar anillo exterior
    const outerRing = coords[0];
    if (!Array.isArray(outerRing) || outerRing.length < 4) {
      result.isValid = false;
      result.errors.push('El polígono debe tener al menos 4 puntos');
      return result;
    }

    // Validar que el polígono esté cerrado
    const firstPoint = outerRing[0];
    const lastPoint = outerRing[outerRing.length - 1];
    if (firstPoint[0] !== lastPoint[0] || firstPoint[1] !== lastPoint[1]) {
      result.isValid = false;
      result.errors.push('El polígono debe estar cerrado (primer y último punto iguales)');
      return result;
    }

    // Validar coordenadas individuales
    for (let i = 0; i < outerRing.length; i++) {
      const point = outerRing[i];
      if (!Array.isArray(point) || point.length !== 2) {
        result.isValid = false;
        result.errors.push(`Punto inválido en posición ${i}`);
        return result;
      }

      const [lng, lat] = point;
      if (typeof lng !== 'number' || typeof lat !== 'number') {
        result.isValid = false;
        result.errors.push(`Coordenadas no numéricas en punto ${i}`);
        return result;
      }

      // Validar rango de coordenadas
      if (lng < -180 || lng > 180 || lat < -90 || lat > 90) {
        result.isValid = false;
        result.errors.push(`Coordenadas fuera de rango en punto ${i}: [${lng}, ${lat}]`);
        return result;
      }

      // Advertencia si está fuera de la región esperada
      if (lng < REGION_BOUNDS.west || lng > REGION_BOUNDS.east || 
          lat < REGION_BOUNDS.south || lat > REGION_BOUNDS.north) {
        result.warnings.push(`Punto ${i} está fuera de la región esperada`);
      }
    }

    // Calcular área y perímetro
    result.area = calculatePolygonArea(geometry as ZoneGeometry);
    result.perimeter = calculatePolygonPerimeter(geometry as ZoneGeometry);

    // Validar área mínima
    if (result.area < 100) { // 100 metros cuadrados mínimo
      result.warnings.push('El área de la zona es muy pequeña (< 100 m²)');
    }

    // Validar área máxima
    if (result.area > 50000000) { // 50 km² máximo
      result.warnings.push('El área de la zona es muy grande (> 50 km²)');
    }

  } catch (error) {
    result.isValid = false;
    result.errors.push(`Error al validar geometría: ${error instanceof Error ? error.message : 'Error desconocido'}`);
  }

  return result;
}

// =====================================================
// FUNCIONES DE CONVERSIÓN
// =====================================================

/**
 * Convierte coordenadas de Leaflet a GeoJSON
 */
export function leafletToGeoJSON(layer: L.Layer): ZoneGeometry | null {
  try {
    if (layer instanceof L.Polygon) {
      const latLngs = layer.getLatLngs()[0] as L.LatLng[];
      const coordinates = [latLngs.map(latlng => [latlng.lng, latlng.lat])];
      
      return {
        type: 'Polygon',
        coordinates
      };
    }

    if (layer instanceof L.Rectangle) {
      const bounds = layer.getBounds();
      const coordinates = [[
        [bounds.getWest(), bounds.getSouth()],
        [bounds.getEast(), bounds.getSouth()],
        [bounds.getEast(), bounds.getNorth()],
        [bounds.getWest(), bounds.getNorth()],
        [bounds.getWest(), bounds.getSouth()]
      ]];
      
      return {
        type: 'Polygon',
        coordinates
      };
    }

    if (layer instanceof L.Circle) {
      return circleToPolygon(layer);
    }

    return null;
  } catch (error) {
    console.error('Error converting Leaflet layer to GeoJSON:', error);
    return null;
  }
}

/**
 * Convierte un círculo de Leaflet a polígono GeoJSON
 */
export function circleToPolygon(circle: L.Circle, points: number = 32): ZoneGeometry {
  const center = circle.getLatLng();
  const radius = circle.getRadius();
  const coordinates: number[][] = [];
  
  for (let i = 0; i < points; i++) {
    const angle = (i * 360) / points;
    const lat = center.lat + (radius / 111320) * Math.cos(angle * Math.PI / 180);
    const lng = center.lng + (radius / (111320 * Math.cos(center.lat * Math.PI / 180))) * Math.sin(angle * Math.PI / 180);
    coordinates.push([lng, lat]);
  }
  
  // Cerrar el polígono
  coordinates.push(coordinates[0]);
  
  return {
    type: 'Polygon',
    coordinates: [coordinates]
  };
}

/**
 * Convierte geometría GeoJSON a coordenadas de Leaflet
 */
export function geoJSONToLeaflet(geometry: ZoneGeometry): L.LatLng[][] {
  const coordinates = geometry.coordinates[0];
  return [coordinates.map(coord => new L.LatLng(coord[1], coord[0]))];
}

// =====================================================
// FUNCIONES DE CÁLCULO
// =====================================================

/**
 * Calcula el área de un polígono en metros cuadrados
 * Usa la fórmula del área de polígono en coordenadas geográficas
 */
export function calculatePolygonArea(geometry: ZoneGeometry): number {
  try {
    const coordinates = geometry.coordinates[0];
    if (coordinates.length < 4) return 0;

    let area = 0;
    const earthRadius = 6371000; // Radio de la Tierra en metros

    for (let i = 0; i < coordinates.length - 1; i++) {
      const [lng1, lat1] = coordinates[i];
      const [lng2, lat2] = coordinates[i + 1];

      const lat1Rad = lat1 * Math.PI / 180;
      const lat2Rad = lat2 * Math.PI / 180;
      const deltaLng = (lng2 - lng1) * Math.PI / 180;

      area += deltaLng * (2 + Math.sin(lat1Rad) + Math.sin(lat2Rad));
    }

    area = Math.abs(area * earthRadius * earthRadius / 2);
    return area;
  } catch (error) {
    console.error('Error calculating polygon area:', error);
    return 0;
  }
}

/**
 * Calcula el perímetro de un polígono en metros
 */
export function calculatePolygonPerimeter(geometry: ZoneGeometry): number {
  try {
    const coordinates = geometry.coordinates[0];
    if (coordinates.length < 2) return 0;

    let perimeter = 0;

    for (let i = 0; i < coordinates.length - 1; i++) {
      const [lng1, lat1] = coordinates[i];
      const [lng2, lat2] = coordinates[i + 1];

      perimeter += calculateDistance(lat1, lng1, lat2, lng2);
    }

    return perimeter;
  } catch (error) {
    console.error('Error calculating polygon perimeter:', error);
    return 0;
  }
}

/**
 * Calcula la distancia entre dos puntos usando la fórmula de Haversine
 */
export function calculateDistance(lat1: number, lng1: number, lat2: number, lng2: number): number {
  const earthRadius = 6371000; // Radio de la Tierra en metros
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLng = (lng2 - lng1) * Math.PI / 180;

  const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLng / 2) * Math.sin(dLng / 2);

  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return earthRadius * c;
}

// =====================================================
// FUNCIONES DE UTILIDAD
// =====================================================

/**
 * Formatea un área en metros cuadrados a una representación legible
 */
export function formatArea(area: number): string {
  if (area < 1000) {
    return `${Math.round(area)} m²`;
  } else if (area < 1000000) {
    return `${(area / 1000).toFixed(1)} km²`;
  } else {
    return `${(area / 1000000).toFixed(2)} km²`;
  }
}

/**
 * Formatea una distancia en metros a una representación legible
 */
export function formatDistance(distance: number): string {
  if (distance < 1000) {
    return `${Math.round(distance)} m`;
  } else {
    return `${(distance / 1000).toFixed(2)} km`;
  }
}

/**
 * Obtiene el centro (centroide) de un polígono
 */
export function getPolygonCenter(geometry: ZoneGeometry): [number, number] {
  const coordinates = geometry.coordinates[0];
  let lat = 0;
  let lng = 0;
  const count = coordinates.length - 1; // Excluir el último punto (duplicado)

  for (let i = 0; i < count; i++) {
    lng += coordinates[i][0];
    lat += coordinates[i][1];
  }

  return [lng / count, lat / count];
}

/**
 * Verifica si un punto está dentro de un polígono
 */
export function isPointInPolygon(point: [number, number], geometry: ZoneGeometry): boolean {
  const [lng, lat] = point;
  const coordinates = geometry.coordinates[0];
  let inside = false;

  for (let i = 0, j = coordinates.length - 1; i < coordinates.length; j = i++) {
    const [xi, yi] = coordinates[i];
    const [xj, yj] = coordinates[j];

    if (((yi > lat) !== (yj > lat)) && (lng < (xj - xi) * (lat - yi) / (yj - yi) + xi)) {
      inside = !inside;
    }
  }

  return inside;
}

/**
 * Simplifica una geometría reduciendo el número de puntos
 */
export function simplifyGeometry(geometry: ZoneGeometry, tolerance: number = 0.0001): ZoneGeometry {
  const coordinates = geometry.coordinates[0];
  const simplified = douglasPeucker(coordinates, tolerance);

  // Asegurar que el polígono esté cerrado
  if (simplified.length > 0 &&
      (simplified[0][0] !== simplified[simplified.length - 1][0] ||
       simplified[0][1] !== simplified[simplified.length - 1][1])) {
    simplified.push([...simplified[0]]);
  }

  return {
    type: 'Polygon',
    coordinates: [simplified]
  };
}

/**
 * Algoritmo de Douglas-Peucker para simplificación de líneas
 */
function douglasPeucker(points: number[][], tolerance: number): number[][] {
  if (points.length <= 2) return points;

  let maxDistance = 0;
  let maxIndex = 0;
  const end = points.length - 1;

  for (let i = 1; i < end; i++) {
    const distance = perpendicularDistance(points[i], points[0], points[end]);
    if (distance > maxDistance) {
      maxDistance = distance;
      maxIndex = i;
    }
  }

  if (maxDistance > tolerance) {
    const left = douglasPeucker(points.slice(0, maxIndex + 1), tolerance);
    const right = douglasPeucker(points.slice(maxIndex), tolerance);
    return left.slice(0, -1).concat(right);
  } else {
    return [points[0], points[end]];
  }
}

/**
 * Calcula la distancia perpendicular de un punto a una línea
 */
function perpendicularDistance(point: number[], lineStart: number[], lineEnd: number[]): number {
  const [x, y] = point;
  const [x1, y1] = lineStart;
  const [x2, y2] = lineEnd;

  const A = x - x1;
  const B = y - y1;
  const C = x2 - x1;
  const D = y2 - y1;

  const dot = A * C + B * D;
  const lenSq = C * C + D * D;

  if (lenSq === 0) return Math.sqrt(A * A + B * B);

  const param = dot / lenSq;
  let xx, yy;

  if (param < 0) {
    xx = x1;
    yy = y1;
  } else if (param > 1) {
    xx = x2;
    yy = y2;
  } else {
    xx = x1 + param * C;
    yy = y1 + param * D;
  }

  const dx = x - xx;
  const dy = y - yy;
  return Math.sqrt(dx * dx + dy * dy);
}
