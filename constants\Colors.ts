/**
 * Volcano App Color System
 * Designed for accessibility, clarity, and emergency communication
 * All colors meet WCAG AA contrast requirements
 */

// Alert Level Colors (International Emergency Standards)
const alertColors = {
  green: '#22C55E',    // Normal/Safe - High contrast green
  yellow: '#F59E0B',   // Advisory - Amber warning
  orange: '#F97316',   // Watch - Orange alert
  red: '#EF4444',      // Warning - Critical red
  purple: '#8B5CF6',   // Emergency - Highest alert
};

// Core Brand Colors
const brandColors = {
  primary: '#1E40AF',     // Trustworthy blue
  secondary: '#64748B',   // Neutral slate
  accent: '#0EA5E9',      // Sky blue for highlights
};

// Semantic Colors
const semanticColors = {
  success: '#10B981',
  warning: '#F59E0B',
  error: '#EF4444',
  info: '#3B82F6',
};

// Neutral Palette (High Contrast)
const neutrals = {
  white: '#FFFFFF',
  gray50: '#F8FAFC',
  gray100: '#F1F5F9',
  gray200: '#E2E8F0',
  gray300: '#CBD5E1',
  gray400: '#94A3B8',
  gray500: '#64748B',
  gray600: '#475569',
  gray700: '#334155',
  gray800: '#1E293B',
  gray900: '#0F172A',
  black: '#000000',
};

export const Colors = {
  light: {
    // Text
    text: neutrals.gray900,
    textSecondary: neutrals.gray600,
    textMuted: neutrals.gray500,
    textInverse: neutrals.white,

    // Backgrounds
    background: neutrals.white,
    backgroundSecondary: neutrals.gray50,
    backgroundTertiary: neutrals.gray100,

    // Interactive
    tint: brandColors.primary,
    tintSecondary: brandColors.accent,

    // Navigation
    tabIconDefault: neutrals.gray400,
    tabIconSelected: brandColors.primary,
    tabBackground: neutrals.white,

    // Borders
    border: neutrals.gray200,
    borderFocus: brandColors.primary,

    // Alert Levels
    alertGreen: alertColors.green,
    alertYellow: alertColors.yellow,
    alertOrange: alertColors.orange,
    alertRed: alertColors.red,
    alertPurple: alertColors.purple,

    // Semantic
    success: semanticColors.success,
    warning: semanticColors.warning,
    error: semanticColors.error,
    info: semanticColors.info,

    // Shadows
    shadow: 'rgba(0, 0, 0, 0.1)',
    shadowStrong: 'rgba(0, 0, 0, 0.25)',
  },
  dark: {
    // Text
    text: neutrals.gray100,
    textSecondary: neutrals.gray300,
    textMuted: neutrals.gray400,
    textInverse: neutrals.gray900,

    // Backgrounds
    background: neutrals.gray900,
    backgroundSecondary: neutrals.gray800,
    backgroundTertiary: neutrals.gray700,

    // Interactive
    tint: brandColors.accent,
    tintSecondary: brandColors.primary,

    // Navigation
    tabIconDefault: neutrals.gray500,
    tabIconSelected: brandColors.accent,
    tabBackground: neutrals.gray800,

    // Borders
    border: neutrals.gray700,
    borderFocus: brandColors.accent,

    // Alert Levels (Adjusted for dark mode)
    alertGreen: '#16A34A',
    alertYellow: '#EAB308',
    alertOrange: '#EA580C',
    alertRed: '#DC2626',
    alertPurple: '#7C3AED',

    // Semantic
    success: '#059669',
    warning: '#D97706',
    error: '#DC2626',
    info: '#2563EB',

    // Shadows
    shadow: 'rgba(0, 0, 0, 0.3)',
    shadowStrong: 'rgba(0, 0, 0, 0.5)',
  },
};

// Alert Level Mappings
export const AlertLevels = {
  NORMAL: 'green',
  ADVISORY: 'yellow',
  WATCH: 'orange',
  WARNING: 'red',
  EMERGENCY: 'purple',
} as const;

export type AlertLevel = keyof typeof AlertLevels;
