{"name": "volcano-app-monorepo", "version": "1.0.0", "description": "Sistema completo de Volcano App - App móvil y panel administrativo", "private": true, "packageManager": "pnpm@8.10.0", "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "turbo run dev", "build": "turbo run build", "test": "turbo run test", "lint": "turbo run lint", "lint:fix": "turbo run lint:fix", "type-check": "turbo run type-check", "clean": "turbo run clean && rm -rf node_modules", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,json,md}\"", "changeset": "changeset", "version-packages": "changeset version", "release": "turbo run build && changeset publish", "db:migrate": "cd database && npm run migrate", "db:seed": "cd database && npm run seed", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "mobile:dev": "cd apps/mobile && npm run dev", "mobile:ios": "cd apps/mobile && npm run ios", "mobile:android": "cd apps/mobile && npm run android", "mobile:start": "cd apps/mobile && npm run start", "admin:dev": "cd apps/admin-web && npm run dev", "admin:build": "cd apps/admin-web && npm run build", "admin:preview": "cd apps/admin-web && npm run preview", "api:dev": "cd apps/api && npm run dev", "api:build": "cd apps/api && npm run build", "api:start": "cd apps/api && npm run start", "install:all": "pnpm install", "update:deps": "pnpm update --recursive"}, "devDependencies": {"@changesets/cli": "^2.27.1", "@turbo/gen": "^1.11.2", "turbo": "^1.11.2", "prettier": "^3.1.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0", "typescript": "^5.3.2", "concurrently": "^8.2.2", "cross-env": "^7.0.3"}, "repository": {"type": "git", "url": "https://github.com/your-org/volcano-app.git"}, "keywords": ["volcano", "emergency", "react-native", "react", "typescript", "monorepo", "supabase", "leaflet", "admin-panel", "mobile-app"], "author": "Volcano App Team", "license": "MIT"}