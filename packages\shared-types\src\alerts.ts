/**
 * 🌋 Volcano App - Tipos de Alertas Volcánicas Compartidos
 */

export enum AlertLevel {
  NORMAL = 'NORMAL',
  ADVISORY = 'ADVISORY',
  WATCH = 'WATCH',
  WARNING = 'WARNING',
  EMERGENCY = 'EMERGENCY'
}

export interface VolcanoAlert {
  id: string;
  title: string;
  message: string;
  alert_level: AlertLevel;
  volcano_name: string;
  volcano_lat: number;
  volcano_lng: number;
  is_active: boolean;
  is_scheduled: boolean;
  scheduled_for?: string;
  expires_at?: string;
  created_by?: string;
  created_at: string;
  updated_at: string;
  metadata?: Record<string, any>;
  admin_users?: {
    full_name: string;
    email: string;
  };
}

export interface CreateAlertRequest {
  title: string;
  message: string;
  alert_level: AlertLevel;
  volcano_name?: string;
  volcano_lat?: number;
  volcano_lng?: number;
  is_scheduled?: boolean;
  scheduled_for?: string;
  expires_at?: string;
  metadata?: Record<string, any>;
}

export interface UpdateAlertRequest {
  title?: string;
  message?: string;
  alert_level?: AlertLevel;
  volcano_name?: string;
  volcano_lat?: number;
  volcano_lng?: number;
  is_active?: boolean;
  is_scheduled?: boolean;
  scheduled_for?: string;
  expires_at?: string;
  metadata?: Record<string, any>;
}

export interface AlertFilters {
  page?: number;
  limit?: number;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
  search?: string;
  alert_level?: AlertLevel;
  is_active?: boolean;
  is_scheduled?: boolean;
}

// Configuraciones de UI para alertas
export const ALERT_LEVEL_COLORS = {
  [AlertLevel.NORMAL]: {
    bg: '#10b981',
    text: '#ffffff',
    border: '#059669'
  },
  [AlertLevel.ADVISORY]: {
    bg: '#f59e0b',
    text: '#ffffff',
    border: '#d97706'
  },
  [AlertLevel.WATCH]: {
    bg: '#f97316',
    text: '#ffffff',
    border: '#ea580c'
  },
  [AlertLevel.WARNING]: {
    bg: '#ef4444',
    text: '#ffffff',
    border: '#dc2626'
  },
  [AlertLevel.EMERGENCY]: {
    bg: '#dc2626',
    text: '#ffffff',
    border: '#b91c1c'
  }
} as const;

export const ALERT_LEVEL_LABELS = {
  [AlertLevel.NORMAL]: 'Normal',
  [AlertLevel.ADVISORY]: 'Aviso',
  [AlertLevel.WATCH]: 'Vigilancia',
  [AlertLevel.WARNING]: 'Alerta',
  [AlertLevel.EMERGENCY]: 'Emergencia'
} as const;

export const ALERT_LEVEL_PRIORITY = {
  [AlertLevel.NORMAL]: 1,
  [AlertLevel.ADVISORY]: 2,
  [AlertLevel.WATCH]: 3,
  [AlertLevel.WARNING]: 4,
  [AlertLevel.EMERGENCY]: 5
} as const;
