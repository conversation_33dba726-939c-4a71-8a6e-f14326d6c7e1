# 📱 Configuración de Red para Desarrollo Móvil

## 🚨 Problema Crítico: localhost vs IP de Red

### ⚠️ **IMPORTANTE PARA PRODUCCIÓN**
Este documento describe un problema fundamental que afecta el desarrollo de aplicaciones React Native con Expo y su comunicación con backends locales. **Es esencial entender esto antes de pasar a producción.**

---

## 🔍 Descripción del Problema

### El Problema
Cuando desarrollas una aplicación React Native con Expo, existe una diferencia crítica entre cómo funciona la aplicación en el **navegador web** vs **dispositivos móviles reales**:

- **🌐 En el navegador web**: `localhost` se refiere a tu computadora
- **📱 En dispositivos móviles**: `localhost` se refiere al dispositivo móvil mismo

### Síntomas
```bash
# ✅ Funciona en web
http://localhost:3002/api/health → 200 OK

# ❌ Falla en móvil
http://localhost:3002/api/health → Network Error / Connection Refused
```

---

## 🛠️ Solución Implementada

### 1. Configuración Dinámica de API

**Archivo**: `services/api.ts`

```typescript
import { Platform } from 'react-native';
import Constants from 'expo-constants';

// Función para obtener la URL base correcta según el entorno
function getBaseURL(): string {
  if (!__DEV__) {
    return 'https://your-production-api.com/api';
  }

  // En desarrollo, necesitamos diferentes URLs según la plataforma
  if (Platform.OS === 'web') {
    // En web, localhost funciona normalmente
    return 'http://localhost:3002/api';
  } else {
    // En móvil (iOS/Android), necesitamos la IP de la computadora
    const hostUri = Constants.expoConfig?.hostUri;
    if (hostUri) {
      // Extraer solo la IP (sin el puerto de Expo)
      const ip = hostUri.split(':')[0];
      return `http://${ip}:3002/api`;
    }
    
    // Fallback: IP manual (cambiar por tu IP local)
    return 'http://************:3002/api';
  }
}

const API_CONFIG = {
  baseURL: getBaseURL(),
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
};
```

### 2. Configuración de WebSocket

**Archivo**: `services/websocket.ts`

```typescript
import { Platform } from 'react-native';
import Constants from 'expo-constants';

function getWebSocketURL(): string {
  if (!__DEV__) {
    return 'https://your-production-api.com';
  }

  if (Platform.OS === 'web') {
    return 'http://localhost:3002';
  } else {
    const hostUri = Constants.expoConfig?.hostUri;
    if (hostUri) {
      const ip = hostUri.split(':')[0];
      return `http://${ip}:3002`;
    }
    return 'http://************:3002'; // Fallback
  }
}
```

### 3. Configuración del Backend

**Archivo**: `backend/src/config/env.ts`

```typescript
export const SERVER_CONFIG = {
  PORT: parseInt(process.env['PORT'] || '3002'),
  HOST: process.env['HOST'] || '0.0.0.0', // ⚠️ CRÍTICO: Escuchar en todas las interfaces
  NODE_ENV: process.env['NODE_ENV'] || 'development'
} as const;
```

**Archivo**: `backend/src/index.ts`

```typescript
// ⚠️ CRÍTICO: Forzar escucha en todas las interfaces IPv4
const server = httpServer.listen(PORT, '0.0.0.0', () => {
  logger.info(`🌋 Backend running on http://0.0.0.0:${PORT}`);
  logger.info(`🔍 Local access: http://localhost:${PORT}/health`);
  logger.info(`🔍 Network access: http://[YOUR_IP]:${PORT}/health`);
});
```

### 4. Configuración de CORS

```typescript
export const CORS_CONFIG = {
  CORS_ORIGIN: process.env['CORS_ORIGIN']?.split(',') || [
    'http://localhost:5173',     // Vite dev server
    'http://localhost:3000',     // React dev server
    'http://localhost:8081',     // Expo dev server (web)
    'http://localhost:19006',    // Expo web fallback
    'http://127.0.0.1:8081',     // Alternative localhost
    'http://************:8081',  // Network IP for mobile testing
    'exp://************:8081',   // Expo protocol
  ],
  CORS_CREDENTIALS: process.env['CORS_CREDENTIALS'] === 'true'
} as const;

// En desarrollo, permitir todos los orígenes
const corsOptions = {
  origin: NODE_ENV === 'development' ? true : CONFIG.CORS.CORS_ORIGIN,
  credentials: CONFIG.CORS.CORS_CREDENTIALS,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  allowedHeaders: [
    'Content-Type', 
    'Authorization', 
    'X-Requested-With',
    'Accept',
    'Origin',
    'Cache-Control'
  ],
};
```

---

## 🔧 Cómo Obtener tu IP Local

### Windows
```bash
ipconfig | findstr "IPv4"
# Resultado: IPv4 Address. . . . . . . . . . . : ************
```

### macOS/Linux
```bash
ifconfig | grep "inet " | grep -v 127.0.0.1
# o
ip addr show | grep "inet " | grep -v 127.0.0.1
```

---

## 🧪 Testing y Verificación

### 1. Verificar que el Backend Escuche en Todas las Interfaces

```bash
# Verificar puertos abiertos
netstat -an | findstr :3002

# Debería mostrar:
# TCP    0.0.0.0:3002           0.0.0.0:0              LISTENING
```

### 2. Probar Conectividad desde la Red

```bash
# Desde localhost (debería funcionar)
curl http://localhost:3002/api/mobile/health

# Desde IP de red (debería funcionar)
curl http://************:3002/api/mobile/health
```

### 3. Logs de Debugging

Agregar logs para verificar la configuración:

```typescript
console.log(`🔧 API Configuration:`, {
  platform: Platform.OS,
  baseURL: API_CONFIG.baseURL,
  hostUri: Constants.expoConfig?.hostUri,
  isDev: __DEV__
});
```

---

## 🚀 Configuración para Producción

### Variables de Entorno

```bash
# .env.production
NODE_ENV=production
HOST=0.0.0.0
PORT=3002
CORS_ORIGIN=https://your-app.com,https://your-admin.com
```

### Configuración de API para Producción

```typescript
function getBaseURL(): string {
  if (!__DEV__) {
    // Producción: usar URL absoluta
    return 'https://api.your-volcano-app.com/api';
  }
  // ... resto de la configuración de desarrollo
}
```

---

## ⚠️ Consideraciones de Seguridad

### Desarrollo
- ✅ `HOST=0.0.0.0` está bien para desarrollo local
- ✅ CORS permisivo está bien para desarrollo

### Producción
- ⚠️ Configurar CORS específico para dominios de producción
- ⚠️ Usar HTTPS en producción
- ⚠️ Configurar firewall apropiadamente
- ⚠️ No exponer puertos de desarrollo en producción

---

## 🐛 Troubleshooting

### Problema: "Network Error" en móvil
**Solución**: Verificar que el backend escuche en `0.0.0.0`, no solo `localhost`

### Problema: CORS errors
**Solución**: Agregar la IP de red a CORS_ORIGIN

### Problema: WebSocket timeout
**Solución**: Verificar que WebSocket use la misma IP que la API

### Problema: Funciona en web pero no en móvil
**Solución**: Implementar detección de plataforma como se muestra arriba

---

## 📝 Checklist para Desarrolladores

- [ ] Backend configurado para escuchar en `0.0.0.0`
- [ ] CORS configurado para incluir IP de red
- [ ] API service con detección de plataforma
- [ ] WebSocket con detección de plataforma
- [ ] Logs de debugging implementados
- [ ] Probado en web y móvil
- [ ] Variables de entorno para producción configuradas

---

**💡 Tip**: Siempre probar tanto en el navegador web como en un dispositivo móvil real durante el desarrollo para detectar estos problemas temprano.
