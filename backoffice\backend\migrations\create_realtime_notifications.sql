-- 🌋 Volcano App - Tabla de Notificaciones en Tiempo Real
-- Tabla para gestionar notificaciones que se propagan via Supabase Realtime

-- =====================================================
-- TABLA DE NOTIFICACIONES EN TIEMPO REAL
-- =====================================================

CREATE TABLE IF NOT EXISTS realtime_notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Información básica de la notificación
    type VARCHAR(50) NOT NULL CHECK (type IN (
        'volcano_alert',
        'zone_update', 
        'system_status',
        'manual_notification',
        'test_notification'
    )),
    title VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    
    -- Datos adicionales de la notificación (JSON)
    data JSONB DEFAULT '{}',
    
    -- Configuración de targeting
    target_type VARCHAR(20) NOT NULL DEFAULT 'all' CHECK (target_type IN (
        'all',
        'zone',
        'location', 
        'device',
        'user'
    )),
    target_criteria JSONB DEFAULT '{}',
    
    -- Estado de la notificación
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN (
        'pending',
        'sent',
        'delivered',
        'failed',
        'expired'
    )),
    
    -- Configuración de entrega
    priority VARCHAR(10) NOT NULL DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high')),
    expires_at TIMESTAMPTZ,
    
    -- Metadatos
    sent_count INTEGER DEFAULT 0,
    delivered_count INTEGER DEFAULT 0,
    failed_count INTEGER DEFAULT 0,
    
    -- Auditoría
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    created_by UUID REFERENCES auth.users(id),
    
    -- Índices para búsquedas eficientes
    CONSTRAINT valid_expiry CHECK (expires_at IS NULL OR expires_at > created_at)
);

-- =====================================================
-- ÍNDICES PARA RENDIMIENTO
-- =====================================================

-- Índice para consultas por tipo y estado
CREATE INDEX IF NOT EXISTS idx_realtime_notifications_type_status 
ON realtime_notifications(type, status);

-- Índice para consultas por fecha de creación
CREATE INDEX IF NOT EXISTS idx_realtime_notifications_created_at 
ON realtime_notifications(created_at DESC);

-- Índice para consultas por target_type
CREATE INDEX IF NOT EXISTS idx_realtime_notifications_target_type 
ON realtime_notifications(target_type);

-- Índice para consultas por prioridad y estado
CREATE INDEX IF NOT EXISTS idx_realtime_notifications_priority_status 
ON realtime_notifications(priority, status);

-- Índice para notificaciones no expiradas
CREATE INDEX IF NOT EXISTS idx_realtime_notifications_active 
ON realtime_notifications(status, expires_at) 
WHERE status IN ('pending', 'sent') AND (expires_at IS NULL OR expires_at > NOW());

-- =====================================================
-- TRIGGERS PARA AUDITORÍA
-- =====================================================

-- Trigger para actualizar updated_at automáticamente
CREATE OR REPLACE FUNCTION update_realtime_notifications_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_realtime_notifications_updated_at
    BEFORE UPDATE ON realtime_notifications
    FOR EACH ROW
    EXECUTE FUNCTION update_realtime_notifications_updated_at();

-- =====================================================
-- FUNCIÓN PARA LIMPIAR NOTIFICACIONES EXPIRADAS
-- =====================================================

CREATE OR REPLACE FUNCTION cleanup_expired_notifications()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- Marcar como expiradas las notificaciones que han pasado su fecha de expiración
    UPDATE realtime_notifications 
    SET status = 'expired', updated_at = NOW()
    WHERE status IN ('pending', 'sent') 
    AND expires_at IS NOT NULL 
    AND expires_at <= NOW();
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- Eliminar notificaciones expiradas más antiguas de 30 días
    DELETE FROM realtime_notifications 
    WHERE status = 'expired' 
    AND updated_at < NOW() - INTERVAL '30 days';
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- POLÍTICAS DE SEGURIDAD (RLS)
-- =====================================================

-- Habilitar RLS en la tabla
ALTER TABLE realtime_notifications ENABLE ROW LEVEL SECURITY;

-- Política para que los usuarios autenticados puedan leer sus notificaciones
CREATE POLICY "Users can read their notifications" ON realtime_notifications
    FOR SELECT
    USING (
        -- Permitir a todos los usuarios autenticados leer notificaciones públicas
        target_type = 'all' 
        OR 
        -- O notificaciones específicas para el usuario
        (target_type = 'user' AND (target_criteria->>'user_id')::UUID = auth.uid())
        OR
        -- O si el usuario es administrador (verificar rol en auth.users)
        EXISTS (
            SELECT 1 FROM auth.users 
            WHERE id = auth.uid() 
            AND raw_user_meta_data->>'role' IN ('ADMIN', 'OPERATOR')
        )
    );

-- Política para que solo administradores puedan crear notificaciones
CREATE POLICY "Only admins can create notifications" ON realtime_notifications
    FOR INSERT
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM auth.users 
            WHERE id = auth.uid() 
            AND raw_user_meta_data->>'role' IN ('ADMIN', 'OPERATOR')
        )
    );

-- Política para que solo administradores puedan actualizar notificaciones
CREATE POLICY "Only admins can update notifications" ON realtime_notifications
    FOR UPDATE
    USING (
        EXISTS (
            SELECT 1 FROM auth.users 
            WHERE id = auth.uid() 
            AND raw_user_meta_data->>'role' IN ('ADMIN', 'OPERATOR')
        )
    );

-- =====================================================
-- HABILITAR REALTIME
-- =====================================================

-- Habilitar Realtime para esta tabla
ALTER PUBLICATION supabase_realtime ADD TABLE realtime_notifications;

-- =====================================================
-- FUNCIÓN PARA ENVIAR NOTIFICACIÓN
-- =====================================================

CREATE OR REPLACE FUNCTION send_realtime_notification(
    p_type VARCHAR(50),
    p_title VARCHAR(200),
    p_message TEXT,
    p_data JSONB DEFAULT '{}',
    p_target_type VARCHAR(20) DEFAULT 'all',
    p_target_criteria JSONB DEFAULT '{}',
    p_priority VARCHAR(10) DEFAULT 'normal',
    p_expires_in_minutes INTEGER DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    notification_id UUID;
    expires_at_value TIMESTAMPTZ;
BEGIN
    -- Calcular fecha de expiración si se especifica
    IF p_expires_in_minutes IS NOT NULL THEN
        expires_at_value := NOW() + (p_expires_in_minutes || ' minutes')::INTERVAL;
    END IF;
    
    -- Insertar la notificación
    INSERT INTO realtime_notifications (
        type,
        title,
        message,
        data,
        target_type,
        target_criteria,
        priority,
        expires_at,
        created_by
    ) VALUES (
        p_type,
        p_title,
        p_message,
        p_data,
        p_target_type,
        p_target_criteria,
        p_priority,
        expires_at_value,
        auth.uid()
    ) RETURNING id INTO notification_id;
    
    RETURN notification_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- COMENTARIOS PARA DOCUMENTACIÓN
-- =====================================================

COMMENT ON TABLE realtime_notifications IS 'Tabla para gestionar notificaciones en tiempo real via Supabase Realtime';
COMMENT ON COLUMN realtime_notifications.type IS 'Tipo de notificación (volcano_alert, zone_update, etc.)';
COMMENT ON COLUMN realtime_notifications.target_type IS 'Tipo de destinatario (all, zone, location, device, user)';
COMMENT ON COLUMN realtime_notifications.target_criteria IS 'Criterios específicos para el targeting (JSON)';
COMMENT ON COLUMN realtime_notifications.data IS 'Datos adicionales de la notificación (JSON)';
COMMENT ON COLUMN realtime_notifications.priority IS 'Prioridad de la notificación (low, normal, high)';
COMMENT ON COLUMN realtime_notifications.expires_at IS 'Fecha de expiración de la notificación';

-- =====================================================
-- DATOS DE EJEMPLO (OPCIONAL)
-- =====================================================

-- Insertar una notificación de prueba (comentado por defecto)
/*
SELECT send_realtime_notification(
    'test_notification',
    '🧪 Notificación de Prueba',
    'Esta es una notificación de prueba del sistema Volcano App',
    '{"test": true, "timestamp": "' || NOW()::TEXT || '"}',
    'all',
    '{}',
    'normal',
    60
);
*/
