/**
 * 🌋 Volcano App Backend - Rutas Principales
 * Configuración y organización de todas las rutas de la API
 */

import { authenticateToken, optionalAuth } from '@/middleware/auth';
import { notFoundHandler } from '@/middleware/errorHandler';
import { Router } from 'express';

// Importar rutas específicas
import alertRoutes from './alerts';
import auditRoutes from './audit';
import authRoutes from './auth';
import configRoutes from './config';
import mobileRoutes from './mobile';
import notificationRoutes from './notifications';
import zoneRoutes from './zones';

// =====================================================
// ROUTER PRINCIPAL
// =====================================================

const router = Router();

// =====================================================
// RUTAS DE SALUD Y ESTADO
// =====================================================

/**
 * Health check básico
 * GET /health
 */
router.get('/health', async (req, res) => {
  try {
    // Importar dinámicamente para evitar dependencias circulares
    const { healthCheck } = await import('@/services/supabase');
    
    const health = await healthCheck();
    const allHealthy = Object.values(health).every(Boolean);
    const status = allHealthy ? 'healthy' : 'unhealthy';
    
    res.status(allHealthy ? 200 : 503).json({
      status,
      timestamp: new Date(),
      services: health,
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development'
    });
  } catch (error) {
    console.error('Health check error:', error);
    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date(),
      error: 'Health check failed'
    });
  }
});

/**
 * Status endpoint más detallado
 * GET /status
 */
router.get('/status', async (req, res) => {
  try {
    const { healthCheck } = await import('@/services/supabase');
    const health = await healthCheck();
    
    res.json({
      success: true,
      data: {
        server: {
          status: 'running',
          uptime: process.uptime(),
          memory: process.memoryUsage(),
          version: process.env.npm_package_version || '1.0.0',
          environment: process.env.NODE_ENV || 'development',
          node_version: process.version
        },
        services: health,
        timestamp: new Date()
      }
    });
  } catch (error) {
    console.error('Status check error:', error);
    res.status(500).json({
      success: false,
      error: 'Status check failed',
      timestamp: new Date()
    });
  }
});

// =====================================================
// RUTAS DE API
// =====================================================

// Rutas de autenticación (sin prefijo /api)
router.use('/auth', authRoutes);

// Rutas protegidas con autenticación
router.use('/alerts', authenticateToken(), alertRoutes);
router.use('/zones', authenticateToken(), zoneRoutes);
router.use('/audit', authenticateToken(), auditRoutes);
router.use('/config', authenticateToken(), configRoutes);
router.use('/notifications', authenticateToken(), notificationRoutes);

// Rutas para app móvil (sin autenticación estricta)
router.use('/mobile', optionalAuth(), mobileRoutes);

// =====================================================
// RUTA DE TESTING
// =====================================================

/**
 * Endpoint de testing
 * GET /test
 */
router.get('/test', (req, res) => {
  res.json({
    success: true,
    message: 'Volcano App Backend API is running!',
    timestamp: new Date(),
    environment: process.env.NODE_ENV || 'development',
    endpoints: {
      auth: [
        'POST /api/auth/login',
        'POST /api/auth/logout',
        'POST /api/auth/refresh',
        'GET /api/auth/me',
        'PUT /api/auth/change-password'
      ],
      alerts: [
        'GET /api/alerts',
        'GET /api/alerts/:id',
        'POST /api/alerts',
        'PUT /api/alerts/:id',
        'DELETE /api/alerts/:id',
        'GET /api/alerts/active'
      ],
      zones: [
        'GET /api/zones',
        'GET /api/zones/:id',
        'POST /api/zones',
        'PUT /api/zones/:id',
        'DELETE /api/zones/:id',
        'GET /api/zones/active'
      ],
      audit: [
        'GET /api/audit',
        'GET /api/audit/:id',
        'GET /api/audit/stats',
        'GET /api/audit/recent'
      ],
      mobile: [
        'GET /api/mobile/alerts/current',
        'GET /api/mobile/zones/all',
        'POST /api/mobile/location/report',
        'POST /api/mobile/location/check',
        'GET /api/mobile/config'
      ],
      config: [
        'GET /api/config',
        'GET /api/config/:key',
        'PUT /api/config/:key',
        'DELETE /api/config/:key',
        'GET /api/config/public',
        'POST /api/config/reset'
      ]
    }
  });
});

// =====================================================
// DOCUMENTACIÓN API
// =====================================================

/**
 * Información de la API
 * GET /
 */
router.get('/', (req, res) => {
  res.json({
    name: 'Volcano App Backend API',
    version: process.env.npm_package_version || '1.0.0',
    description: 'API para el sistema de administración de alertas volcánicas',
    environment: process.env.NODE_ENV || 'development',
    endpoints: {
      health: '/health',
      status: '/status',
      test: '/api/test',
      documentation: '/api-docs'
    },
    timestamp: new Date()
  });
});

// =====================================================
// MANEJO DE RUTAS NO ENCONTRADAS
// =====================================================

// Aplicar el handler de rutas no encontradas al final
router.use('*', notFoundHandler());

// =====================================================
// EXPORTACIÓN
// =====================================================

export default router;
