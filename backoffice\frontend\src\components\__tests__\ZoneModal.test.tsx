/**
 * 🌋 Volcano App Frontend - Tests para ZoneModal
 * Tests unitarios para el modal de creación/edición de zonas
 */

import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi } from 'vitest';
import { ZoneModal } from '../ZoneModal';

// Mock para useToast
vi.mock('@/hooks/use-toast', () => ({
  useToast: () => ({
    toast: vi.fn()
  })
}));

const mockZone = {
  id: '1',
  name: 'Zona Segura Centro',
  description: 'Zona de evacuación principal',
  zone_type: 'SAFE' as const,
  geometry: {
    type: 'Polygon' as const,
    coordinates: [[[-71.9048, -39.2904], [-71.9000, -39.2904], [-71.9000, -39.2850], [-71.9048, -39.2850], [-71.9048, -39.2904]]]
  },
  center_lat: -39.2877,
  center_lng: -71.9024,
  capacity: 1000,
  contact_info: {
    phone: '+56912345678',
    email: '<EMAIL>'
  },
  facilities: ['Agua', 'Electricidad', 'Comunicaciones'],
  is_active: true,
  version: 1,
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
  created_by: {
    full_name: 'Admin User',
    email: '<EMAIL>'
  }
};

const mockProps = {
  isOpen: true,
  onClose: vi.fn(),
  zone: null,
  isCreating: true,
  onZoneCreated: vi.fn(),
  onZoneUpdated: vi.fn()
};

describe('ZoneModal', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders create modal correctly', () => {
    render(<ZoneModal {...mockProps} />);
    
    expect(screen.getByText('Crear Nueva Zona')).toBeInTheDocument();
    expect(screen.getByText('Crear zona de seguridad')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Nombre de la zona')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Descripción de la zona')).toBeInTheDocument();
  });

  it('renders edit modal correctly', () => {
    render(<ZoneModal {...mockProps} isCreating={false} zone={mockZone} />);
    
    expect(screen.getByText('Editar Zona')).toBeInTheDocument();
    expect(screen.getByText('Modificar zona de seguridad')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Zona Segura Centro')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Zona de evacuación principal')).toBeInTheDocument();
  });

  it('does not render when closed', () => {
    render(<ZoneModal {...mockProps} isOpen={false} />);
    
    expect(screen.queryByText('Crear Nueva Zona')).not.toBeInTheDocument();
  });

  it('calls onClose when close button is clicked', () => {
    render(<ZoneModal {...mockProps} />);
    
    const closeButton = screen.getByRole('button', { name: /cerrar/i });
    fireEvent.click(closeButton);
    
    expect(mockProps.onClose).toHaveBeenCalledTimes(1);
  });

  it('calls onClose when cancel button is clicked', () => {
    render(<ZoneModal {...mockProps} />);
    
    const cancelButton = screen.getByText('Cancelar');
    fireEvent.click(cancelButton);
    
    expect(mockProps.onClose).toHaveBeenCalledTimes(1);
  });

  it('validates required fields', async () => {
    const user = userEvent.setup();
    render(<ZoneModal {...mockProps} />);
    
    const saveButton = screen.getByText('Crear Zona');
    await user.click(saveButton);
    
    await waitFor(() => {
      expect(screen.getByText('El nombre es requerido')).toBeInTheDocument();
      expect(screen.getByText('La descripción es requerida')).toBeInTheDocument();
    });
  });

  it('handles form submission for creating zone', async () => {
    const user = userEvent.setup();
    render(<ZoneModal {...mockProps} />);
    
    // Llenar formulario
    await user.type(screen.getByPlaceholderText('Nombre de la zona'), 'Nueva Zona');
    await user.type(screen.getByPlaceholderText('Descripción de la zona'), 'Descripción de prueba');
    await user.selectOptions(screen.getByDisplayValue('SAFE'), 'EMERGENCY');
    await user.type(screen.getByPlaceholderText('Capacidad máxima'), '500');
    
    // Enviar formulario
    const saveButton = screen.getByText('Crear Zona');
    await user.click(saveButton);
    
    await waitFor(() => {
      expect(mockProps.onZoneCreated).toHaveBeenCalledWith(
        expect.objectContaining({
          name: 'Nueva Zona',
          description: 'Descripción de prueba',
          zone_type: 'EMERGENCY',
          capacity: 500
        })
      );
    });
  });

  it('handles form submission for updating zone', async () => {
    const user = userEvent.setup();
    render(<ZoneModal {...mockProps} isCreating={false} zone={mockZone} />);
    
    // Modificar nombre
    const nameInput = screen.getByDisplayValue('Zona Segura Centro');
    await user.clear(nameInput);
    await user.type(nameInput, 'Zona Modificada');
    
    // Enviar formulario
    const saveButton = screen.getByText('Actualizar Zona');
    await user.click(saveButton);
    
    await waitFor(() => {
      expect(mockProps.onZoneUpdated).toHaveBeenCalledWith(
        expect.objectContaining({
          id: '1',
          name: 'Zona Modificada'
        })
      );
    });
  });

  it('displays zone type options correctly', () => {
    render(<ZoneModal {...mockProps} />);
    
    const zoneTypeSelect = screen.getByDisplayValue('SAFE');
    expect(zoneTypeSelect).toBeInTheDocument();
    
    // Verificar opciones disponibles
    expect(screen.getByText('Zona Segura')).toBeInTheDocument();
    expect(screen.getByText('Zona de Emergencia')).toBeInTheDocument();
    expect(screen.getByText('Zona de Peligro')).toBeInTheDocument();
    expect(screen.getByText('Zona de Evacuación')).toBeInTheDocument();
    expect(screen.getByText('Zona Restringida')).toBeInTheDocument();
  });

  it('handles capacity input correctly', async () => {
    const user = userEvent.setup();
    render(<ZoneModal {...mockProps} />);
    
    const capacityInput = screen.getByPlaceholderText('Capacidad máxima');
    await user.type(capacityInput, '1500');
    
    expect(capacityInput).toHaveValue(1500);
  });

  it('handles contact information input', async () => {
    const user = userEvent.setup();
    render(<ZoneModal {...mockProps} />);
    
    const phoneInput = screen.getByPlaceholderText('Teléfono de contacto');
    const emailInput = screen.getByPlaceholderText('Email de contacto');
    
    await user.type(phoneInput, '+56987654321');
    await user.type(emailInput, '<EMAIL>');
    
    expect(phoneInput).toHaveValue('+56987654321');
    expect(emailInput).toHaveValue('<EMAIL>');
  });

  it('handles facilities selection', async () => {
    const user = userEvent.setup();
    render(<ZoneModal {...mockProps} />);
    
    // Seleccionar facilidades
    const waterCheckbox = screen.getByLabelText('Agua');
    const electricityCheckbox = screen.getByLabelText('Electricidad');
    
    await user.click(waterCheckbox);
    await user.click(electricityCheckbox);
    
    expect(waterCheckbox).toBeChecked();
    expect(electricityCheckbox).toBeChecked();
  });

  it('shows loading state during submission', async () => {
    const user = userEvent.setup();
    render(<ZoneModal {...mockProps} />);
    
    // Llenar campos requeridos
    await user.type(screen.getByPlaceholderText('Nombre de la zona'), 'Test Zone');
    await user.type(screen.getByPlaceholderText('Descripción de la zona'), 'Test Description');
    
    // Simular envío
    const saveButton = screen.getByText('Crear Zona');
    await user.click(saveButton);
    
    // Verificar estado de carga
    expect(screen.getByText('Creando...')).toBeInTheDocument();
    expect(saveButton).toBeDisabled();
  });

  it('validates email format', async () => {
    const user = userEvent.setup();
    render(<ZoneModal {...mockProps} />);
    
    const emailInput = screen.getByPlaceholderText('Email de contacto');
    await user.type(emailInput, 'invalid-email');
    
    // Trigger validation
    await user.tab();
    
    await waitFor(() => {
      expect(screen.getByText('Email inválido')).toBeInTheDocument();
    });
  });

  it('validates phone format', async () => {
    const user = userEvent.setup();
    render(<ZoneModal {...mockProps} />);
    
    const phoneInput = screen.getByPlaceholderText('Teléfono de contacto');
    await user.type(phoneInput, 'invalid-phone');
    
    // Trigger validation
    await user.tab();
    
    await waitFor(() => {
      expect(screen.getByText('Formato de teléfono inválido')).toBeInTheDocument();
    });
  });

  it('resets form when modal is closed and reopened', async () => {
    const { rerender } = render(<ZoneModal {...mockProps} />);
    
    // Llenar formulario
    const nameInput = screen.getByPlaceholderText('Nombre de la zona');
    await userEvent.type(nameInput, 'Test Zone');
    
    // Cerrar modal
    rerender(<ZoneModal {...mockProps} isOpen={false} />);
    
    // Reabrir modal
    rerender(<ZoneModal {...mockProps} isOpen={true} />);
    
    // Verificar que el formulario se resetea
    expect(screen.getByPlaceholderText('Nombre de la zona')).toHaveValue('');
  });

  it('populates form with zone data when editing', () => {
    render(<ZoneModal {...mockProps} isCreating={false} zone={mockZone} />);
    
    expect(screen.getByDisplayValue('Zona Segura Centro')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Zona de evacuación principal')).toBeInTheDocument();
    expect(screen.getByDisplayValue('SAFE')).toBeInTheDocument();
    expect(screen.getByDisplayValue('1000')).toBeInTheDocument();
    expect(screen.getByDisplayValue('+56912345678')).toBeInTheDocument();
    expect(screen.getByDisplayValue('<EMAIL>')).toBeInTheDocument();
  });
});
