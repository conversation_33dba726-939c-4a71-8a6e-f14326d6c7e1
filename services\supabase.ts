/**
 * 🌋 Volcano App Mobile - Configuración de Supabase
 * Cliente de Supabase para la aplicación móvil con soporte para notificaciones en tiempo real
 */

import { createClient, SupabaseClient } from '@supabase/supabase-js';
import Constants from 'expo-constants';

// =====================================================
// CONFIGURACIÓN DE SUPABASE
// =====================================================

// Obtener configuración desde variables de entorno de Expo
const SUPABASE_URL = Constants.expoConfig?.extra?.supabaseUrl || 
                     process.env.EXPO_PUBLIC_SUPABASE_URL || 
                     'https://gdcbnnlmxwazgnwpfelt.supabase.co';

const SUPABASE_ANON_KEY = Constants.expoConfig?.extra?.supabaseAnonKey || 
                          process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || 
                          'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdkY2JubmxteHdhemdud3BmZWx0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3NDMzODIsImV4cCI6MjA2NDMxOTM4Mn0.MzNjNQiXKDzwL8EL2beFDgTqeN_DJrXILjzRphOPlFg';

// Validar configuración
if (!SUPABASE_URL || !SUPABASE_ANON_KEY) {
  console.error('❌ Missing Supabase configuration for mobile app');
  console.error('Please check your environment variables or app.config.js');
}

// =====================================================
// CLIENTE DE SUPABASE
// =====================================================

/**
 * Cliente principal de Supabase para la aplicación móvil
 * Configurado para notificaciones en tiempo real y autenticación
 */
export const supabase: SupabaseClient = createClient(SUPABASE_URL, SUPABASE_ANON_KEY, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
    storage: undefined, // Usar AsyncStorage por defecto en React Native
  },
  // Deshabilitar realtime temporalmente para evitar problemas con WebSockets
  realtime: Platform.OS === 'web' ? {
    params: {
      eventsPerSecond: 10,
    },
  } : undefined,
  global: {
    headers: {
      'X-Client-Info': 'volcano-app-mobile',
    },
  },
});

// =====================================================
// UTILIDADES DE CONEXIÓN
// =====================================================

/**
 * Verificar conexión con Supabase
 */
export async function checkSupabaseConnection(): Promise<boolean> {
  try {
    const { data, error } = await supabase
      .from('volcano_alerts')
      .select('id')
      .limit(1);

    if (error) {
      console.error('❌ Supabase connection error:', error.message);
      return false;
    }

    console.log('✅ Supabase connection successful');
    return true;
  } catch (error) {
    console.error('❌ Failed to connect to Supabase:', error);
    return false;
  }
}

/**
 * Obtener información de configuración de Supabase
 */
export function getSupabaseConfig() {
  return {
    url: SUPABASE_URL,
    hasAnonKey: !!SUPABASE_ANON_KEY,
    isConfigured: !!(SUPABASE_URL && SUPABASE_ANON_KEY),
  };
}

// =====================================================
// TIPOS PARA NOTIFICACIONES
// =====================================================

export interface RealtimeNotification {
  id: string;
  title: string;
  message: string;
  type: 'volcano_alert' | 'zone_update' | 'system_status' | 'test';
  data: any;
  created_at: string;
  is_active: boolean;
}

// =====================================================
// FUNCIONES DE NOTIFICACIONES EN TIEMPO REAL
// =====================================================

/**
 * Suscribirse a notificaciones en tiempo real usando polling
 * (Alternativa a WebSockets para React Native)
 */
export function subscribeToRealtimeNotifications(
  onNotification: (notification: RealtimeNotification) => void,
  onError?: (error: any) => void
) {
  console.log('🔔 Setting up polling-based notifications for React Native...');

  let lastCheckTime = new Date().toISOString();
  let pollingInterval: NodeJS.Timeout;

  // Función para verificar nuevas notificaciones
  const checkForNewNotifications = async () => {
    try {
      // Verificar nuevas alertas volcánicas
      const { data: alerts, error: alertsError } = await supabase
        .from('volcano_alerts')
        .select('*')
        .eq('is_active', true)
        .gt('created_at', lastCheckTime)
        .order('created_at', { ascending: false });

      if (alertsError) {
        console.error('Error checking for new alerts:', alertsError);
        return;
      }

      // Procesar nuevas alertas
      if (alerts && alerts.length > 0) {
        alerts.forEach((alert) => {
          const notification: RealtimeNotification = {
            id: alert.id,
            title: `🌋 Alerta Volcánica: ${alert.alert_level}`,
            message: alert.message || `${alert.volcano_name} - ${alert.title}`,
            type: 'volcano_alert',
            data: {
              type: 'volcano_alert',
              alertId: alert.id,
              alertLevel: alert.alert_level,
              volcanoName: alert.volcano_name,
              volcanoLat: alert.volcano_lat,
              volcanoLng: alert.volcano_lng,
              timestamp: new Date().toISOString(),
            },
            created_at: alert.created_at,
            is_active: alert.is_active,
          };
          onNotification(notification);
        });
      }

      // Actualizar timestamp de última verificación
      lastCheckTime = new Date().toISOString();

    } catch (error) {
      console.error('Error in polling notifications:', error);
      if (onError) {
        onError(error);
      }
    }
  };

  // Iniciar polling cada 30 segundos
  pollingInterval = setInterval(checkForNewNotifications, 30000);

  // Verificar inmediatamente
  checkForNewNotifications();

  console.log('✅ Polling-based notifications started');

  // Retornar objeto con método de cleanup
  return {
    unsubscribe: () => {
      if (pollingInterval) {
        clearInterval(pollingInterval);
        console.log('🔇 Polling notifications stopped');
      }
    }
  };
}

/**
 * Desuscribirse de notificaciones en tiempo real
 */
export function unsubscribeFromRealtimeNotifications(channel: any) {
  if (channel && channel.unsubscribe) {
    channel.unsubscribe();
  } else {
    console.log('🔇 No active subscription to unsubscribe from');
  }
}

// =====================================================
// INICIALIZACIÓN
// =====================================================

/**
 * Inicializar Supabase para la aplicación móvil
 */
export async function initializeSupabase(): Promise<boolean> {
  try {
    console.log('🔧 Initializing Supabase for mobile app...');
    
    const config = getSupabaseConfig();
    console.log('🔧 Supabase Configuration:', {
      url: config.url,
      hasAnonKey: config.hasAnonKey,
      isConfigured: config.isConfigured,
    });

    if (!config.isConfigured) {
      console.error('❌ Supabase not properly configured');
      return false;
    }

    const isConnected = await checkSupabaseConnection();
    if (!isConnected) {
      console.error('❌ Failed to connect to Supabase');
      return false;
    }

    console.log('✅ Supabase initialized successfully for mobile app');
    return true;
  } catch (error) {
    console.error('❌ Failed to initialize Supabase:', error);
    return false;
  }
}

// =====================================================
// EXPORTACIONES
// =====================================================

export default supabase;
