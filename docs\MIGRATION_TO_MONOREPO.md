# 🔄 Migración a Monorepo - Volcano App

Esta guía te ayudará a migrar el proyecto actual de Volcano App a una estructura de monorepo optimizada.

## 📋 Estado Actual vs Objetivo

### 🔴 Estado Actual
```
volcanoApp/
├── app/                     # App móvil React Native
├── components/
├── constants/
├── hooks/
├── backoffice/              # Sistema de backoffice separado
│   ├── backend/
│   ├── frontend/
│   └── database/
└── package.json
```

### 🟢 Objetivo (Monorepo)
```
volcano-app/
├── apps/
│   ├── mobile/              # App móvil (migrada)
│   ├── admin-web/           # Panel admin (migrado)
│   └── api/                 # Backend API (migrado)
├── packages/
│   ├── shared-types/        # Tipos compartidos
│   ├── utils/               # Utilidades comunes
│   ├── ui-components/       # Componentes UI
│   └── api-client/          # Cliente API
├── database/                # Scripts de BD
├── tools/                   # Herramientas
├── package.json             # Configuración monorepo
├── pnpm-workspace.yaml
└── turbo.json
```

## 🚀 Pasos de Migración

### Paso 1: Preparar Estructura del Monorepo

```bash
# 1. Crear directorios principales
mkdir -p apps/{mobile,admin-web,api}
mkdir -p packages/{shared-types,utils,ui-components,api-client}
mkdir -p tools/scripts

# 2. Copiar archivos de configuración del monorepo
cp monorepo-package.json package.json
cp pnpm-workspace.yaml .
cp turbo.json .
```

### Paso 2: Migrar App Móvil

```bash
# Mover la app móvil actual a apps/mobile/
mv app apps/mobile/app
mv components apps/mobile/components
mv constants apps/mobile/constants
mv hooks apps/mobile/hooks
mv assets apps/mobile/assets

# Actualizar package.json de la app móvil
# (mantener dependencias específicas de React Native)
```

### Paso 3: Migrar Backend API

```bash
# Mover backend a apps/api/
mv backoffice/backend/* apps/api/

# Actualizar imports para usar paquetes compartidos
# Ejemplo: import { AlertLevel } from '@volcano-app/shared-types'
```

### Paso 4: Migrar Frontend Admin

```bash
# Mover frontend a apps/admin-web/
mv backoffice/frontend/* apps/admin-web/

# Actualizar imports para usar paquetes compartidos
```

### Paso 5: Migrar Base de Datos

```bash
# Mover scripts de BD
mv backoffice/database/* database/
```

### Paso 6: Crear Paquetes Compartidos

```bash
# Los paquetes ya están creados en packages/
# Solo necesitas instalar dependencias
pnpm install
```

### Paso 7: Actualizar Imports

#### En la App Móvil (apps/mobile/)
```typescript
// Antes
import { AlertLevel } from '../types/alerts';

// Después
import { AlertLevel } from '@volcano-app/shared-types';
```

#### En el Backend (apps/api/)
```typescript
// Antes
import { VolcanoAlert } from '../types';

// Después
import { VolcanoAlert } from '@volcano-app/shared-types';
```

#### En el Frontend Admin (apps/admin-web/)
```typescript
// Antes
import { calculateDistance } from '../utils/geospatial';

// Después
import { calculateDistance } from '@volcano-app/utils';
```

### Paso 8: Configurar TypeScript

Cada aplicación necesita su propio `tsconfig.json` que extienda la configuración base:

```json
// apps/mobile/tsconfig.json
{
  "extends": "../../tsconfig.json",
  "compilerOptions": {
    "baseUrl": "./src",
    "paths": {
      "@volcano-app/*": ["../../packages/*/src"]
    }
  }
}
```

### Paso 9: Actualizar Scripts de Build

```json
// apps/mobile/package.json
{
  "scripts": {
    "dev": "expo start",
    "build": "expo build",
    "type-check": "tsc --noEmit"
  },
  "dependencies": {
    "@volcano-app/shared-types": "workspace:*",
    "@volcano-app/utils": "workspace:*"
  }
}
```

## 🔧 Comandos de Migración Automatizada

Puedes usar este script para automatizar parte de la migración:

```bash
#!/bin/bash
# migrate-to-monorepo.sh

echo "🌋 Migrando Volcano App a Monorepo..."

# Crear estructura
mkdir -p apps/{mobile,admin-web,api}
mkdir -p packages/{shared-types,utils,ui-components,api-client}

# Migrar app móvil
echo "📱 Migrando app móvil..."
mv app apps/mobile/ 2>/dev/null || echo "app/ no encontrado"
mv components apps/mobile/ 2>/dev/null || echo "components/ no encontrado"
mv constants apps/mobile/ 2>/dev/null || echo "constants/ no encontrado"
mv hooks apps/mobile/ 2>/dev/null || echo "hooks/ no encontrado"

# Migrar backoffice
echo "🖥️ Migrando backoffice..."
if [ -d "backoffice" ]; then
    mv backoffice/backend/* apps/api/ 2>/dev/null
    mv backoffice/frontend/* apps/admin-web/ 2>/dev/null
    mv backoffice/database/* database/ 2>/dev/null
    rm -rf backoffice
fi

# Copiar configuraciones
echo "⚙️ Configurando monorepo..."
cp monorepo-package.json package.json
cp pnpm-workspace.yaml .
cp turbo.json .

echo "✅ Migración completada!"
echo "📝 Próximos pasos:"
echo "1. pnpm install"
echo "2. Actualizar imports en cada app"
echo "3. Configurar variables de entorno"
echo "4. pnpm dev"
```

## 🧪 Verificación Post-Migración

### 1. Verificar Estructura
```bash
tree -I node_modules -L 3
```

### 2. Verificar Dependencias
```bash
pnpm install
pnpm type-check
```

### 3. Verificar Builds
```bash
pnpm build
```

### 4. Verificar Desarrollo
```bash
pnpm dev
```

## 🔄 Rollback (Si es necesario)

Si algo sale mal, puedes volver al estado anterior:

```bash
# Hacer backup antes de migrar
cp -r volcanoApp volcanoApp-backup

# Para rollback
rm -rf volcanoApp
mv volcanoApp-backup volcanoApp
```

## 📚 Recursos Adicionales

- [Turborepo Documentation](https://turbo.build/repo/docs)
- [pnpm Workspaces](https://pnpm.io/workspaces)
- [TypeScript Project References](https://www.typescriptlang.org/docs/handbook/project-references.html)

## 🎯 Beneficios Post-Migración

Una vez completada la migración, tendrás:

✅ **Desarrollo unificado**: Un solo comando para todo  
✅ **Tipos sincronizados**: Cambios se propagan automáticamente  
✅ **Builds optimizados**: Solo reconstruye lo que cambió  
✅ **Código reutilizable**: Componentes y utilidades compartidas  
✅ **Testing centralizado**: Una configuración para todo  
✅ **Deployment coordinado**: Versiones sincronizadas  

¡La migración vale la pena! 🚀
