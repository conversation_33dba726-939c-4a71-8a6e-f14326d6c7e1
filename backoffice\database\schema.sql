-- 🌋 Volcano App - Esquema de Base de Datos
-- Ejecutar en Supabase SQL Editor

-- Habilitar extensiones necesarias
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";

-- Enum para niveles de alerta volcánica
CREATE TYPE alert_level AS ENUM (
    'NORMAL',
    'ADVISORY', 
    'WATCH',
    'WARNING',
    'EMERGENCY'
);

-- Enum para tipos de zonas de seguridad
CREATE TYPE zone_type AS ENUM (
    'SAFE',
    'EMERGENCY',
    'DANGER',
    'EVACUATION',
    'RESTRICTED'
);

-- Enum para roles de usuario
CREATE TYPE user_role AS ENUM (
    'ADMIN',
    'OPERATOR',
    'VIEWER'
);

-- Enum para tipos de acción en auditoría
CREATE TYPE audit_action AS ENUM (
    'CREATE',
    'UPDATE',
    'DELETE',
    'LOGIN',
    'LOGOUT'
);

-- =====================================================
-- TABLA: admin_users
-- Usuarios administradores del sistema
-- =====================================================
CREATE TABLE admin_users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(255) NOT NULL,
    role user_role NOT NULL DEFAULT 'VIEWER',
    is_active BOOLEAN NOT NULL DEFAULT true,
    last_login TIMESTAMPTZ,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- =====================================================
-- TABLA: volcano_alerts
-- Alertas volcánicas del sistema
-- =====================================================
CREATE TABLE volcano_alerts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    alert_level alert_level NOT NULL,
    volcano_name VARCHAR(255) NOT NULL DEFAULT 'Volcán Villarrica',
    volcano_lat DECIMAL(10, 8) NOT NULL DEFAULT -39.420000,
    volcano_lng DECIMAL(11, 8) NOT NULL DEFAULT -71.939167,
    is_active BOOLEAN NOT NULL DEFAULT true,
    is_scheduled BOOLEAN NOT NULL DEFAULT false,
    scheduled_for TIMESTAMPTZ,
    expires_at TIMESTAMPTZ,
    created_by UUID REFERENCES admin_users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Metadatos adicionales
    metadata JSONB DEFAULT '{}',
    
    -- Índices para búsquedas eficientes
    CONSTRAINT valid_schedule CHECK (
        (is_scheduled = false) OR 
        (is_scheduled = true AND scheduled_for IS NOT NULL)
    )
);

-- =====================================================
-- TABLA: safety_zones
-- Zonas de seguridad georreferenciadas
-- =====================================================
CREATE TABLE safety_zones (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    zone_type zone_type NOT NULL,

    -- Geometría usando PostGIS
    geometry GEOMETRY(POLYGON, 4326) NOT NULL,

    -- Coordenadas del centro de la zona para facilitar edición manual
    center_lat DECIMAL(10, 8), -- Latitud del centro (-90 a 90)
    center_lng DECIMAL(11, 8), -- Longitud del centro (-180 a 180)

    -- Propiedades adicionales
    capacity INTEGER, -- Capacidad de personas (para refugios)
    contact_info JSONB DEFAULT '{}', -- Información de contacto
    facilities JSONB DEFAULT '{}', -- Instalaciones disponibles
    
    -- Control de versiones
    is_active BOOLEAN NOT NULL DEFAULT true,
    version INTEGER NOT NULL DEFAULT 1,
    
    -- Auditoría
    created_by UUID REFERENCES admin_users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Metadatos
    metadata JSONB DEFAULT '{}'
);

-- =====================================================
-- TABLA: audit_logs
-- Registro de auditoría del sistema
-- =====================================================
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES admin_users(id),
    action audit_action NOT NULL,
    table_name VARCHAR(255),
    record_id UUID,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- =====================================================
-- TABLA: app_users_locations (opcional)
-- Ubicaciones anonimizadas de usuarios de la app móvil
-- =====================================================
CREATE TABLE app_users_locations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    anonymous_id VARCHAR(255) NOT NULL, -- ID anónimo del dispositivo
    location GEOMETRY(POINT, 4326) NOT NULL,
    accuracy DECIMAL(8, 2), -- Precisión en metros
    reported_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Datos contextuales
    app_version VARCHAR(50),
    device_type VARCHAR(50),
    
    -- Índice temporal para limpieza automática
    expires_at TIMESTAMPTZ NOT NULL DEFAULT (NOW() + INTERVAL '24 hours')
);

-- =====================================================
-- TABLA: system_config
-- Configuración del sistema
-- =====================================================
CREATE TABLE system_config (
    key VARCHAR(255) PRIMARY KEY,
    value JSONB NOT NULL,
    description TEXT,
    updated_by UUID REFERENCES admin_users(id),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- =====================================================
-- ÍNDICES PARA OPTIMIZACIÓN
-- =====================================================

-- Índices para volcano_alerts
CREATE INDEX idx_volcano_alerts_active ON volcano_alerts(is_active);
CREATE INDEX idx_volcano_alerts_level ON volcano_alerts(alert_level);
CREATE INDEX idx_volcano_alerts_scheduled ON volcano_alerts(is_scheduled, scheduled_for);
CREATE INDEX idx_volcano_alerts_created_at ON volcano_alerts(created_at DESC);

-- Índices espaciales para safety_zones
CREATE INDEX idx_safety_zones_geometry ON safety_zones USING GIST(geometry);
CREATE INDEX idx_safety_zones_type ON safety_zones(zone_type);
CREATE INDEX idx_safety_zones_active ON safety_zones(is_active);

-- Índices para app_users_locations
CREATE INDEX idx_app_users_locations_geometry ON app_users_locations USING GIST(location);
CREATE INDEX idx_app_users_locations_reported_at ON app_users_locations(reported_at DESC);
CREATE INDEX idx_app_users_locations_expires_at ON app_users_locations(expires_at);

-- Índices para audit_logs
CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_action ON audit_logs(action);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at DESC);
CREATE INDEX idx_audit_logs_table_record ON audit_logs(table_name, record_id);

-- =====================================================
-- FUNCIONES Y TRIGGERS
-- =====================================================

-- Función para actualizar updated_at automáticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers para updated_at
CREATE TRIGGER update_admin_users_updated_at 
    BEFORE UPDATE ON admin_users 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_volcano_alerts_updated_at 
    BEFORE UPDATE ON volcano_alerts 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_safety_zones_updated_at 
    BEFORE UPDATE ON safety_zones 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Función para limpiar ubicaciones expiradas
CREATE OR REPLACE FUNCTION cleanup_expired_locations()
RETURNS void AS $$
BEGIN
    DELETE FROM app_users_locations WHERE expires_at < NOW();
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- VISTAS ÚTILES
-- =====================================================

-- Vista para alertas activas
CREATE VIEW active_alerts AS
SELECT 
    id,
    title,
    message,
    alert_level,
    volcano_name,
    volcano_lat,
    volcano_lng,
    created_at,
    updated_at
FROM volcano_alerts 
WHERE is_active = true 
  AND (expires_at IS NULL OR expires_at > NOW())
  AND (scheduled_for IS NULL OR scheduled_for <= NOW())
ORDER BY alert_level DESC, created_at DESC;

-- Vista para zonas de seguridad públicas
CREATE VIEW public_safety_zones AS
SELECT 
    id,
    name,
    description,
    zone_type,
    ST_AsGeoJSON(geometry) as geometry_json,
    capacity,
    contact_info,
    facilities
FROM safety_zones 
WHERE is_active = true
ORDER BY zone_type, name;

-- Vista para estadísticas del dashboard
CREATE VIEW dashboard_stats AS
SELECT 
    (SELECT COUNT(*) FROM volcano_alerts WHERE is_active = true) as active_alerts,
    (SELECT COUNT(*) FROM safety_zones WHERE is_active = true) as active_zones,
    (SELECT COUNT(*) FROM app_users_locations WHERE reported_at > NOW() - INTERVAL '1 hour') as users_last_hour,
    (SELECT COUNT(*) FROM audit_logs WHERE created_at > NOW() - INTERVAL '24 hours') as actions_last_24h;

-- =====================================================
-- POLÍTICAS DE SEGURIDAD (RLS)
-- =====================================================

-- Habilitar RLS en tablas sensibles
ALTER TABLE admin_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE volcano_alerts ENABLE ROW LEVEL SECURITY;
ALTER TABLE safety_zones ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;

-- Políticas básicas (se pueden personalizar según necesidades)
CREATE POLICY "Admin users can view all users" ON admin_users
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Users can view active alerts" ON volcano_alerts
    FOR SELECT USING (is_active = true);

CREATE POLICY "Users can view active zones" ON safety_zones
    FOR SELECT USING (is_active = true);

-- =====================================================
-- CONFIGURACIÓN INICIAL
-- =====================================================

-- Insertar configuración inicial del sistema
INSERT INTO system_config (key, value, description) VALUES
('volcano_default_location', '{"lat": -39.420000, "lng": -71.939167}', 'Ubicación por defecto del Volcán Villarrica'),
('alert_auto_expire_hours', '24', 'Horas después de las cuales las alertas expiran automáticamente'),
('max_user_locations_per_day', '100', 'Máximo número de ubicaciones por usuario por día'),
('notification_settings', '{"push_enabled": true, "email_enabled": false}', 'Configuración de notificaciones');

-- Comentarios en las tablas
COMMENT ON TABLE admin_users IS 'Usuarios administradores del sistema de backoffice';
COMMENT ON TABLE volcano_alerts IS 'Alertas volcánicas gestionadas por los administradores';
COMMENT ON TABLE safety_zones IS 'Zonas de seguridad georreferenciadas con geometrías PostGIS';
COMMENT ON TABLE audit_logs IS 'Registro de auditoría de todas las acciones del sistema';
COMMENT ON TABLE app_users_locations IS 'Ubicaciones anonimizadas de usuarios de la app móvil';
COMMENT ON TABLE system_config IS 'Configuración general del sistema';
