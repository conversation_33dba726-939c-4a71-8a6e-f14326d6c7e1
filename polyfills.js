/**
 * 🌋 Volcano App Mobile - Polyfills para React Native
 * Configuración de polyfills necesarios para Supabase y otras dependencias
 */

// Polyfill para URL
import 'react-native-url-polyfill/auto';

// Polyfill para crypto
import 'react-native-get-random-values';

// Polyfill para Buffer
import { <PERSON><PERSON><PERSON> } from 'buffer';

// Configurar variables globales necesarias para Supabase
if (typeof global.Buffer === 'undefined') {
  global.Buffer = Buffer;
}

// Polyfill básico para process
if (typeof global.process === 'undefined') {
  global.process = {
    env: {},
    version: '',
    platform: 'react-native',
    nextTick: (callback) => setTimeout(callback, 0),
  };
}

// Polyfill básico para eventos
if (typeof global.EventTarget === 'undefined') {
  global.EventTarget = class EventTarget {
    constructor() {
      this.listeners = {};
    }

    addEventListener(type, listener) {
      if (!this.listeners[type]) {
        this.listeners[type] = [];
      }
      this.listeners[type].push(listener);
    }

    removeEventListener(type, listener) {
      if (this.listeners[type]) {
        const index = this.listeners[type].indexOf(listener);
        if (index > -1) {
          this.listeners[type].splice(index, 1);
        }
      }
    }

    dispatchEvent(event) {
      if (this.listeners[event.type]) {
        this.listeners[event.type].forEach(listener => listener(event));
      }
    }
  };
}

console.log('✅ Polyfills initialized for React Native');
