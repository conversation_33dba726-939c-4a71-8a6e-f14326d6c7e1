# 🌋 Volcano App Backend - API Documentation

## Overview

The Volcano App Backend provides a comprehensive REST API for managing volcano alerts, safety zones, user authentication, and real-time data synchronization for both the backoffice system and mobile applications.

## Base URL

- **Development**: `http://localhost:3001/api`
- **Production**: `https://api.volcanoapp.com/api`

## Interactive Documentation

- **Swagger UI**: `http://localhost:3001/api-docs`
- **OpenAPI Spec**: `http://localhost:3001/api-docs/swagger.json`

## Authentication

Most endpoints require JWT authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

### Getting a Token

```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "your-password"
}
```

## Rate Limiting

- **Limit**: 100 requests per 15-minute window per IP address
- **Headers**: Rate limit information is included in response headers

## Real-time Features

### WebSocket Connection

Connect to WebSocket for real-time updates:

```javascript
const socket = io('ws://localhost:3001', {
  auth: {
    token: 'your-jwt-token',
    deviceId: 'unique-device-id',
    appVersion: '1.0.0'
  }
});

// Listen for alert updates
socket.on('alert:updated', (data) => {
  console.log('Alert updated:', data);
});

// Listen for zone updates
socket.on('zone:updated', (data) => {
  console.log('Zone updated:', data);
});
```

## API Endpoints

### Authentication

#### Login
```http
POST /api/auth/login
```
Authenticate user and get JWT tokens.

#### Get Profile
```http
GET /api/auth/me
Authorization: Bearer <token>
```
Get current user profile information.

#### Change Password
```http
PUT /api/auth/change-password
Authorization: Bearer <token>
```
Change user password.

#### Refresh Token
```http
POST /api/auth/refresh
```
Refresh JWT access token using refresh token.

### Volcano Alerts

#### List Alerts
```http
GET /api/alerts?page=1&limit=20&sort_by=created_at&sort_order=desc
Authorization: Bearer <token>
```
Get paginated list of volcano alerts with filtering options.

#### Get Alert by ID
```http
GET /api/alerts/{id}
Authorization: Bearer <token>
```
Get specific alert by ID.

#### Create Alert
```http
POST /api/alerts
Authorization: Bearer <token>
Content-Type: application/json

{
  "title": "Volcanic Activity Increase",
  "message": "Increased seismic activity detected...",
  "alert_level": "WATCH",
  "volcano_name": "Volcán Villarrica",
  "volcano_lat": -39.420000,
  "volcano_lng": -71.939167,
  "expires_at": "2024-12-31T23:59:59Z"
}
```

#### Update Alert
```http
PUT /api/alerts/{id}
Authorization: Bearer <token>
```
Update existing alert.

#### Delete Alert
```http
DELETE /api/alerts/{id}
Authorization: Bearer <token>
```
Delete alert (soft delete - sets is_active to false).

#### Get Active Alerts
```http
GET /api/alerts/active
Authorization: Bearer <token>
```
Get all currently active alerts.

### Safety Zones

#### List Zones
```http
GET /api/zones?page=1&limit=20&zone_type=SAFE
Authorization: Bearer <token>
```
Get paginated list of safety zones.

#### Get Zone by ID
```http
GET /api/zones/{id}
Authorization: Bearer <token>
```
Get specific zone by ID.

#### Create Zone
```http
POST /api/zones
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "Emergency Shelter A",
  "description": "Primary emergency shelter",
  "zone_type": "SAFE",
  "geometry": {
    "type": "Polygon",
    "coordinates": [[[-71.95, -39.42], [-71.94, -39.42], [-71.94, -39.41], [-71.95, -39.41], [-71.95, -39.42]]]
  },
  "capacity": 500,
  "contact_info": {
    "phone": "+56-9-1234-5678",
    "email": "<EMAIL>"
  },
  "facilities": {
    "medical": true,
    "food": true,
    "water": true,
    "communications": true
  }
}
```

#### Update Zone
```http
PUT /api/zones/{id}
Authorization: Bearer <token>
```
Update existing zone.

#### Delete Zone
```http
DELETE /api/zones/{id}
Authorization: Bearer <token>
```
Delete zone (soft delete).

#### Get Active Zones
```http
GET /api/zones/active
Authorization: Bearer <token>
```
Get all currently active zones.

### Mobile API

#### Get Current Alert (Mobile)
```http
GET /api/mobile/alerts/current
```
Get current active alert optimized for mobile consumption.

#### Get All Zones (Mobile)
```http
GET /api/mobile/zones/all
```
Get all active safety zones for mobile app.

#### Report Location
```http
POST /api/mobile/location/report
Content-Type: application/json

{
  "anonymous_id": "device-uuid-123",
  "latitude": -39.420000,
  "longitude": -71.939167,
  "accuracy": 10.5,
  "app_version": "1.0.0",
  "device_type": "iOS"
}
```

#### Check Location in Zones
```http
POST /api/mobile/location/check
Content-Type: application/json

{
  "latitude": -39.420000,
  "longitude": -71.939167
}
```

#### Bulk Sync (NEW)
```http
POST /api/mobile/sync
Content-Type: application/json

{
  "lastSync": "2024-01-01T00:00:00Z",
  "deviceInfo": {
    "platform": "iOS",
    "version": "17.0",
    "model": "iPhone 15"
  },
  "appVersion": "1.0.0",
  "requestedData": ["alerts", "zones", "config"]
}
```
Optimized bulk synchronization for offline-first mobile apps.

#### Check App Version (NEW)
```http
GET /api/mobile/version/check?version=1.0.0&platform=iOS
```
Check if app version is supported and get update information.

#### Batch Location Report (NEW)
```http
POST /api/mobile/location/batch
Content-Type: application/json

{
  "locations": [
    {
      "anonymous_id": "device-uuid-123",
      "latitude": -39.420000,
      "longitude": -71.939167,
      "accuracy": 10.5,
      "timestamp": "2024-01-01T12:00:00Z"
    }
  ],
  "deviceInfo": {
    "appVersion": "1.0.0",
    "platform": "iOS"
  }
}
```
Batch location reporting for offline synchronization.

#### Get Mobile Config
```http
GET /api/mobile/config
```
Get configuration settings for mobile app.

### System & Monitoring

#### Health Check
```http
GET /health
```
Basic health check endpoint.

#### Detailed Status
```http
GET /status
```
Detailed system status including services and metrics.

#### API Test
```http
GET /api/test
```
Test endpoint with API information.

### Audit Logs

#### List Audit Logs
```http
GET /api/audit?page=1&limit=50&action=CREATE&start_date=2024-01-01
Authorization: Bearer <token>
```
Get audit logs with filtering (Admin only).

#### Get Audit Statistics
```http
GET /api/audit/stats
Authorization: Bearer <token>
```
Get audit statistics and metrics (Admin only).

#### Get Recent Activity
```http
GET /api/audit/recent
Authorization: Bearer <token>
```
Get recent system activity (Operator+ access).

### Configuration

#### List Configurations
```http
GET /api/config
Authorization: Bearer <token>
```
Get system configuration settings.

#### Update Configuration
```http
PUT /api/config/{key}
Authorization: Bearer <token>
Content-Type: application/json

{
  "value": {
    "setting": "new_value"
  }
}
```

#### Get Public Configuration
```http
GET /api/config/public
```
Get public configuration settings (no auth required).

## Error Handling

All endpoints return consistent error responses:

```json
{
  "success": false,
  "error": "Error message",
  "details": [
    {
      "field": "email",
      "message": "Invalid email format"
    }
  ],
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### HTTP Status Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Validation Error
- `429` - Rate Limited
- `500` - Internal Server Error

## Data Models

### Alert Levels
- `NORMAL` - Normal volcanic activity
- `ADVISORY` - Elevated activity, stay informed
- `WATCH` - Heightened activity, prepare for action
- `WARNING` - Dangerous activity, take action
- `EMERGENCY` - Imminent threat, evacuate immediately

### Zone Types
- `SAFE` - Designated safe area/shelter
- `EMERGENCY` - Emergency assembly point
- `DANGER` - Dangerous area to avoid
- `EVACUATION` - Evacuation route/zone
- `RESTRICTED` - Access restricted area

### User Roles
- `VIEWER` - Read-only access
- `OPERATOR` - Can manage alerts and zones
- `ADMIN` - Full system access

## WebSocket Events

### Client → Server
- `mobile:heartbeat` - Keep connection alive
- `mobile:sync:request` - Request data sync
- `location:update` - Report location update

### Server → Client
- `alert:created` - New alert created
- `alert:updated` - Alert updated
- `alert:deleted` - Alert deleted
- `zone:created` - New zone created
- `zone:updated` - Zone updated
- `zone:deleted` - Zone deleted
- `system:status` - System status update
- `mobile:sync:response` - Sync data response

## Performance & Caching

- **Redis Cache**: Frequently accessed data is cached for improved performance
- **Response Compression**: Gzip compression enabled for all responses
- **Database Optimization**: Indexed queries and connection pooling
- **Rate Limiting**: Prevents API abuse and ensures fair usage

## Security Features

- **JWT Authentication**: Secure token-based authentication
- **Role-based Authorization**: Granular permission control
- **Input Validation**: Comprehensive request validation
- **SQL Injection Protection**: Parameterized queries
- **CORS Configuration**: Controlled cross-origin access
- **Helmet Security**: Security headers and protections
- **Audit Logging**: Complete activity tracking

## Mobile App Integration

The API is specifically designed for mobile app consumption with:

- **Offline-first Support**: Bulk sync and batch operations
- **Optimized Payloads**: Minimal data transfer
- **Real-time Updates**: WebSocket for live data
- **Version Compatibility**: App version checking
- **Anonymous Tracking**: Privacy-preserving location tracking
- **Efficient Caching**: Reduced server requests

## Support

For API support and questions:
- **Documentation**: Available at `/api-docs`
- **Health Monitoring**: Check `/health` and `/status`
- **Logs**: Comprehensive logging for debugging
