/**
 * 🌋 Volcano App Backoffice - Página del Mapa Interactivo
 * Componente que muestra un mapa interactivo con Leaflet para visualizar zonas de seguridad y alertas
 */

import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Badge } from './ui/badge';
import { Button } from './ui/button';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Polygon } from 'react-leaflet';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import { AlertTriangle, Shield, RefreshCw, Maximize2 } from 'lucide-react';

// Fix para los marcadores por defecto en React Leaflet
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

// Tipos para las props
interface Zone {
  id: string;
  name: string;
  description: string;
  zone_type: 'SAFE' | 'EMERGENCY' | 'EVACUATION' | 'DANGER';
  geometry: {
    type: string;
    coordinates: number[][][];
  };
  is_active: boolean;
  version: number;
}

interface Alert {
  id: string;
  title: string;
  message: string;
  alert_level: 'NORMAL' | 'ADVISORY' | 'WATCH' | 'WARNING' | 'EMERGENCY';
  volcano_name: string;
  volcano_lat: number;
  volcano_lng: number;
  is_active: boolean;
}

interface InteractiveMapPageProps {
  zones: Zone[];
  alerts: Alert[];
  onRefresh?: () => void;
}

// Coordenadas del Volcán Villarrica
const VOLCANO_POSITION: [number, number] = [-39.420000, -71.939167];

// Funciones de utilidad para colores
const getZoneColor = (type: string) => {
  switch (type) {
    case 'SAFE': return '#10b981';
    case 'EMERGENCY': return '#3b82f6';
    case 'EVACUATION': return '#f59e0b';
    case 'DANGER': return '#ef4444';
    default: return '#6b7280';
  }
};

const getAlertColor = (level: string) => {
  switch (level) {
    case 'NORMAL': return '#10b981';
    case 'ADVISORY': return '#f59e0b';
    case 'WATCH': return '#f97316';
    case 'WARNING': return '#ef4444';
    case 'EMERGENCY': return '#dc2626';
    default: return '#6b7280';
  }
};

const getZoneIcon = (type: string) => {
  switch (type) {
    case 'SAFE': return '🛡️';
    case 'EMERGENCY': return '🚨';
    case 'EVACUATION': return '🚪';
    case 'DANGER': return '⚠️';
    default: return '📍';
  }
};

// Componente de información de zona para popup
function ZoneInfo({ zone }: { zone: Zone }) {
  return (
    <div className="min-w-[200px]">
      <div className="flex items-center space-x-2 mb-2">
        <span className="text-lg">{getZoneIcon(zone.zone_type)}</span>
        <h3 className="font-semibold text-gray-900">{zone.name}</h3>
      </div>
      <p className="text-sm text-gray-600 mb-3">{zone.description}</p>
      <div className="flex items-center space-x-2 text-xs text-gray-500 mb-3">
        <span 
          className="px-2 py-1 rounded-full text-white font-medium"
          style={{ backgroundColor: getZoneColor(zone.zone_type) }}
        >
          {zone.zone_type}
        </span>
        <span>v{zone.version}</span>
      </div>
    </div>
  );
}

// Componente principal
export function InteractiveMapPage({ zones, alerts, onRefresh }: InteractiveMapPageProps) {
  const [selectedZone, setSelectedZone] = useState<Zone | null>(null);
  const [mapHeight, setMapHeight] = useState<string>('600px');

  // Estadísticas rápidas
  const activeZones = zones.filter(zone => zone.is_active);
  const activeAlerts = alerts.filter(alert => alert.is_active);
  const zonesByType = activeZones.reduce((acc, zone) => {
    acc[zone.zone_type] = (acc[zone.zone_type] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const toggleFullscreen = () => {
    setMapHeight(mapHeight === '600px' ? 'calc(100vh - 200px)' : '600px');
  };

  return (
    <div className="space-y-6">
      {/* Header con estadísticas */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Shield className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm font-medium">Zonas Activas</p>
                <p className="text-2xl font-bold">{activeZones.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5 text-orange-600" />
              <div>
                <p className="text-sm font-medium">Alertas Activas</p>
                <p className="text-2xl font-bold">{activeAlerts.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="space-y-2">
              <p className="text-sm font-medium">Tipos de Zona</p>
              <div className="flex flex-wrap gap-1">
                {Object.entries(zonesByType).map(([type, count]) => (
                  <Badge 
                    key={type} 
                    variant="secondary" 
                    className="text-xs"
                    style={{ backgroundColor: getZoneColor(type), color: 'white' }}
                  >
                    {type}: {count}
                  </Badge>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium">Acciones</p>
                <p className="text-xs text-gray-500">Controles del mapa</p>
              </div>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onRefresh}
                  className="h-8 w-8 p-0"
                >
                  <RefreshCw className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={toggleFullscreen}
                  className="h-8 w-8 p-0"
                >
                  <Maximize2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Mapa principal */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <span>🗺️</span>
            <span>Mapa Interactivo - Volcán Villarrica</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div 
            className="rounded-lg overflow-hidden border border-gray-200"
            style={{ height: mapHeight }}
          >
            <MapContainer
              center={VOLCANO_POSITION}
              zoom={10}
              style={{ height: '100%', width: '100%' }}
            >
              <TileLayer
                attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                maxZoom={18}
              />

              {/* Marcador del volcán */}
              <Marker
                position={VOLCANO_POSITION}
                icon={L.divIcon({
                  className: 'volcano-marker',
                  html: '<div style="background: #EF4444; width: 30px; height: 30px; border-radius: 50%; border: 3px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3); display: flex; align-items: center; justify-content: center; font-size: 16px;">🌋</div>',
                  iconSize: [30, 30],
                  iconAnchor: [15, 15]
                })}
              >
                <Popup>
                  <div className="text-center">
                    <h3 className="font-semibold text-gray-900">Volcán Villarrica</h3>
                    <p className="text-sm text-gray-600">Coordenadas: {VOLCANO_POSITION[0]}, {VOLCANO_POSITION[1]}</p>
                  </div>
                </Popup>
              </Marker>

              {/* Marcadores de alertas */}
              {activeAlerts.map((alert) => (
                <Marker
                  key={alert.id}
                  position={[alert.volcano_lat, alert.volcano_lng]}
                  icon={L.divIcon({
                    className: 'alert-marker',
                    html: `<div style="
                      background-color: ${getAlertColor(alert.alert_level)};
                      width: 20px;
                      height: 20px;
                      border-radius: 50%;
                      border: 2px solid white;
                      box-shadow: 0 2px 4px rgba(0,0,0,0.3);
                    "></div>`,
                    iconSize: [20, 20],
                    iconAnchor: [10, 10]
                  })}
                >
                  <Popup>
                    <div className="min-w-[200px]">
                      <h3 className="font-semibold text-gray-900 mb-1">{alert.title}</h3>
                      <p className="text-sm text-gray-600 mb-2">{alert.message}</p>
                      <p className="text-sm text-gray-600 mb-2">{alert.volcano_name}</p>
                      <Badge 
                        className="text-xs"
                        style={{ backgroundColor: getAlertColor(alert.alert_level), color: 'white' }}
                      >
                        {alert.alert_level}
                      </Badge>
                    </div>
                  </Popup>
                </Marker>
              ))}

              {/* Polígonos de zonas */}
              {activeZones.map((zone) => (
                <Polygon
                  key={zone.id}
                  positions={zone.geometry.coordinates[0].map(coord => [coord[1], coord[0]] as [number, number])}
                  pathOptions={{
                    color: getZoneColor(zone.zone_type),
                    fillColor: getZoneColor(zone.zone_type),
                    fillOpacity: 0.3,
                    weight: 2
                  }}
                  eventHandlers={{
                    click: () => setSelectedZone(zone)
                  }}
                >
                  <Popup>
                    <ZoneInfo zone={zone} />
                  </Popup>
                </Polygon>
              ))}
            </MapContainer>
          </div>
        </CardContent>
      </Card>

      {/* Panel de información de zona seleccionada */}
      {selectedZone && (
        <Card>
          <CardHeader>
            <CardTitle>Información de Zona Seleccionada</CardTitle>
          </CardHeader>
          <CardContent>
            <ZoneInfo zone={selectedZone} />
            <Button
              variant="outline"
              size="sm"
              onClick={() => setSelectedZone(null)}
              className="mt-3"
            >
              Cerrar
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
