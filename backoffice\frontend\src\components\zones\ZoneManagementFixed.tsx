/**
 * 🌋 Volcano App Backoffice - Gestión de Zonas de Seguridad (Versión Corregida)
 * Componente principal para la gestión completa de zonas de seguridad con carga segura de Leaflet
 */

import React, { useState, useEffect, Suspense, lazy } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import { Download, Filter, Plus, Search, Upload, Map, Table, Loader2 } from 'lucide-react';

// Lazy loading del componente de mapa para evitar problemas de SSR
const InteractiveZoneMap = lazy(() => 
  import('./InteractiveZoneMap').catch(() => ({
    default: () => (
      <div className="h-[500px] w-full rounded-lg border flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <Map className="h-12 w-12 mx-auto mb-4 text-gray-400" />
          <h3 className="text-lg font-semibold mb-2">Error al cargar el mapa</h3>
          <p className="text-muted-foreground">
            No se pudo cargar el componente de mapa interactivo.
          </p>
        </div>
      </div>
    )
  }))
);

// Tipos
export interface Zone {
  id: string;
  name: string;
  description: string;
  zone_type: 'SAFE' | 'EMERGENCY' | 'DANGER' | 'EVACUATION' | 'RESTRICTED';
  geometry: {
    type: 'Polygon';
    coordinates: number[][][];
  };
  capacity?: number;
  contact_info?: any;
  facilities?: string[];
  is_active: boolean;
  version: number;
  created_at: string;
  updated_at: string;
  created_by?: {
    full_name: string;
    email: string;
  };
}

export interface ZoneFilters {
  search: string;
  zone_type: string;
  is_active: string;
  sort_by: string;
  sort_order: 'asc' | 'desc';
}

// Componente de carga
function MapLoadingFallback() {
  return (
    <div className="h-[500px] w-full rounded-lg border flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <Loader2 className="h-8 w-8 mx-auto mb-4 text-blue-500 animate-spin" />
        <h3 className="text-lg font-semibold mb-2">Cargando mapa interactivo...</h3>
        <p className="text-muted-foreground">
          Inicializando componentes de Leaflet
        </p>
      </div>
    </div>
  );
}

// Componente principal
export function ZoneManagementFixed() {
  const { toast } = useToast();
  
  console.log('🎛️ ZoneManagementFixed component rendered');
  
  // Estados
  const [zones, setZones] = useState<Zone[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('table');
  const [mapReady, setMapReady] = useState(false);
  
  // Filtros
  const [filters, setFilters] = useState<ZoneFilters>({
    search: '',
    zone_type: '',
    is_active: '',
    sort_by: 'created_at',
    sort_order: 'desc'
  });

  // Estadísticas
  const [stats, setStats] = useState({
    total: 0,
    active: 0,
    by_type: {} as Record<string, number>
  });

  // Verificar si Leaflet está disponible
  useEffect(() => {
    const checkLeaflet = async () => {
      try {
        // Intentar cargar Leaflet dinámicamente
        await import('leaflet');
        await import('react-leaflet');
        setMapReady(true);
        console.log('✅ Leaflet loaded successfully');
      } catch (error) {
        console.error('❌ Error loading Leaflet:', error);
        toast({
          title: 'Error',
          description: 'No se pudo cargar el mapa interactivo. Usando modo tabla.',
          variant: 'destructive',
        });
      }
    };

    checkLeaflet();
  }, [toast]);

  // Simular carga de datos
  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      // Simular delay de carga
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Datos de ejemplo
      const mockZones: Zone[] = [
        {
          id: '1',
          name: 'Zona Segura Centro Pucón',
          description: 'Área segura en el centro de Pucón para evacuación',
          zone_type: 'SAFE',
          geometry: {
            type: 'Polygon',
            coordinates: [[
              [-71.9500, -39.2700],
              [-71.9400, -39.2700],
              [-71.9400, -39.2800],
              [-71.9500, -39.2800],
              [-71.9500, -39.2700]
            ]]
          },
          capacity: 500,
          is_active: true,
          version: 1,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      ];
      
      setZones(mockZones);
      calculateStats(mockZones);
      setLoading(false);
    };

    loadData();
  }, []);

  const calculateStats = (zonesData: Zone[]) => {
    const total = zonesData.length;
    const active = zonesData.filter(z => z.is_active).length;
    const by_type = zonesData.reduce((acc, zone) => {
      acc[zone.zone_type] = (acc[zone.zone_type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    setStats({ total, active, by_type });
  };

  // Manejadores de eventos (simplificados)
  const handleCreateZone = (geometry: any) => {
    console.log('Create zone with geometry:', geometry);
    toast({
      title: 'Información',
      description: 'Función de crear zona en desarrollo',
    });
  };

  const handleEditZone = (zone: Zone, newGeometry: any) => {
    console.log('Edit zone:', zone.id, newGeometry);
    toast({
      title: 'Información',
      description: 'Función de editar zona en desarrollo',
    });
  };

  const handleDeleteZone = (zoneId: string) => {
    console.log('Delete zone:', zoneId);
    toast({
      title: 'Información',
      description: 'Función de eliminar zona en desarrollo',
    });
  };

  const fetchZones = async () => {
    console.log('Refreshing zones...');
    toast({
      title: 'Información',
      description: 'Datos actualizados',
    });
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-center">
              <Loader2 className="h-8 w-8 animate-spin mr-2" />
              <span>Cargando gestión de zonas...</span>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header con estadísticas */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Map className="h-5 w-5" />
            Gestión de Zonas de Seguridad
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{stats.total}</div>
              <div className="text-sm text-muted-foreground">Total de Zonas</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{stats.active}</div>
              <div className="text-sm text-muted-foreground">Zonas Activas</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">{Object.keys(stats.by_type).length}</div>
              <div className="text-sm text-muted-foreground">Tipos de Zona</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Contenido Principal */}
      <Tabs value={activeTab} onValueChange={(value) => {
        console.log('🔄 Changing tab to:', value);
        setActiveTab(value);
      }}>
        <TabsList>
          <TabsTrigger value="table">Vista de Tabla</TabsTrigger>
          <TabsTrigger value="map" disabled={!mapReady}>
            Vista de Mapa {!mapReady && '(Cargando...)'}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="table" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Table className="h-5 w-5" />
                Zonas Registradas ({zones.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              {zones.length === 0 ? (
                <div className="text-center py-12">
                  <Map className="mx-auto h-12 w-12 text-muted-foreground" />
                  <h3 className="mt-4 text-lg font-semibold">No hay zonas</h3>
                  <p className="text-muted-foreground mt-2">
                    Comienza creando una nueva zona de seguridad.
                  </p>
                  <Button className="mt-4 gap-2">
                    <Plus className="h-4 w-4" />
                    Nueva Zona
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  {zones.map((zone) => (
                    <div key={zone.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-semibold">{zone.name}</h4>
                          <p className="text-sm text-muted-foreground">{zone.description}</p>
                          <div className="flex items-center gap-2 mt-2">
                            <span className={`px-2 py-1 rounded text-xs ${
                              zone.zone_type === 'SAFE' ? 'bg-green-100 text-green-800' :
                              zone.zone_type === 'EMERGENCY' ? 'bg-blue-100 text-blue-800' :
                              zone.zone_type === 'DANGER' ? 'bg-red-100 text-red-800' :
                              'bg-gray-100 text-gray-800'
                            }`}>
                              {zone.zone_type}
                            </span>
                            <span className={`px-2 py-1 rounded text-xs ${
                              zone.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                            }`}>
                              {zone.is_active ? 'Activa' : 'Inactiva'}
                            </span>
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <Button variant="outline" size="sm">
                            Editar
                          </Button>
                          <Button variant="outline" size="sm">
                            Eliminar
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="map" className="space-y-4">
          {console.log('🗺️ Rendering map tab content with zones:', zones.length)}
          {mapReady ? (
            <Suspense fallback={<MapLoadingFallback />}>
              <InteractiveZoneMap
                zones={zones}
                onZoneCreate={handleCreateZone}
                onZoneEdit={handleEditZone}
                onZoneDelete={handleDeleteZone}
                onRefresh={fetchZones}
              />
            </Suspense>
          ) : (
            <Card>
              <CardContent className="p-6">
                <div className="text-center">
                  <Map className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                  <h3 className="text-lg font-semibold mb-2">Mapa no disponible</h3>
                  <p className="text-muted-foreground">
                    No se pudieron cargar las dependencias del mapa. Usa la vista de tabla.
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default ZoneManagementFixed;
