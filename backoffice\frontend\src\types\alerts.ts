/**
 * 🌋 Volcano App Frontend - Tipos de Alertas Volcánicas
 */

export enum AlertLevel {
  NORMAL = 'NORMAL',
  ADVISORY = 'ADVISORY',
  WATCH = 'WATCH',
  WARNING = 'WARNING',
  EMERGENCY = 'EMERGENCY'
}

export interface VolcanoAlert {
  id: string;
  title: string;
  message: string;
  alert_level: AlertLevel;
  volcano_name: string;
  volcano_lat: number;
  volcano_lng: number;
  is_active: boolean;
  is_scheduled: boolean;
  scheduled_for?: string;
  expires_at?: string;
  created_by?: string;
  created_at: string;
  updated_at: string;
  metadata?: Record<string, any>;
  admin_users?: {
    full_name: string;
    email: string;
  };
}

export interface CreateAlertRequest {
  title: string;
  message: string;
  alert_level: AlertLevel;
  volcano_name?: string;
  volcano_lat?: number;
  volcano_lng?: number;
  is_scheduled?: boolean;
  scheduled_for?: string;
  expires_at?: string;
  metadata?: Record<string, any>;
}

export interface UpdateAlertRequest {
  title?: string;
  message?: string;
  alert_level?: AlertLevel;
  volcano_name?: string;
  volcano_lat?: number;
  volcano_lng?: number;
  is_active?: boolean;
  is_scheduled?: boolean;
  scheduled_for?: string;
  expires_at?: string;
  metadata?: Record<string, any>;
}

export interface AlertFilters {
  page?: number;
  limit?: number;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
  search?: string;
  alert_level?: AlertLevel;
  is_active?: boolean;
  is_scheduled?: boolean;
}

export const ALERT_LEVEL_COLORS = {
  [AlertLevel.NORMAL]: 'bg-green-100 text-green-800 border-green-200',
  [AlertLevel.ADVISORY]: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  [AlertLevel.WATCH]: 'bg-orange-100 text-orange-800 border-orange-200',
  [AlertLevel.WARNING]: 'bg-red-100 text-red-800 border-red-200',
  [AlertLevel.EMERGENCY]: 'bg-red-200 text-red-900 border-red-300'
} as const;

export const ALERT_LEVEL_LABELS = {
  [AlertLevel.NORMAL]: 'Normal',
  [AlertLevel.ADVISORY]: 'Aviso',
  [AlertLevel.WATCH]: 'Vigilancia',
  [AlertLevel.WARNING]: 'Alerta',
  [AlertLevel.EMERGENCY]: 'Emergencia'
} as const;
