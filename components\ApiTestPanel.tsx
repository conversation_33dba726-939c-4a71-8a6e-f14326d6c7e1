/**
 * 🌋 Volcano App - API Test Panel
 * Componente para probar la integración con la API del backend
 */

import { Colors } from '@/constants/Colors';
import { BorderRadius, Layout, Spacing } from '@/constants/Layout';
import {
    useBulkSync,
    useCurrentAlert,
    useHealthCheck,
    useLocationManager,
    useMobileConfig,
    useSafetyZones
} from '@/hooks/useApi';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useWebSocket } from '@/services/websocket';
import React, { useState } from 'react';
import { ScrollView, StyleSheet, View } from 'react-native';
import { PrimaryButton, SecondaryButton } from './ui/AccessibleButton';
import { AccessibleText } from './ui/AccessibleText';

export function ApiTestPanel() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const [testResults, setTestResults] = useState<string[]>([]);

  // API hooks
  const healthQuery = useHealthCheck();
  const configQuery = useMobileConfig();
  const alertQuery = useCurrentAlert();
  const zonesQuery = useSafetyZones();
  const { reportLocation } = useLocationManager();
  const bulkSync = useBulkSync();
  const { isConnected, requestSync, reportLocation: wsReportLocation } = useWebSocket();

  const addTestResult = (result: string) => {
    setTestResults(prev => [`${new Date().toLocaleTimeString()}: ${result}`, ...prev.slice(0, 9)]);
  };

  const testHealthCheck = async () => {
    try {
      await healthQuery.refetch();
      addTestResult(`✅ Health Check: ${healthQuery.data?.status || 'OK'}`);
    } catch (error) {
      addTestResult(`❌ Health Check failed: ${error}`);
    }
  };

  const testMobileConfig = async () => {
    try {
      await configQuery.refetch();
      addTestResult(`✅ Config: ${configQuery.data?.volcano_coordinates?.name || 'Loaded'}`);
    } catch (error) {
      addTestResult(`❌ Config failed: ${error}`);
    }
  };

  const testCurrentAlert = async () => {
    try {
      await alertQuery.refetch();
      if (alertQuery.data) {
        addTestResult(`✅ Alert: ${alertQuery.data.title} (${alertQuery.data.alert_level})`);
      } else {
        addTestResult(`✅ Alert: No active alerts`);
      }
    } catch (error) {
      addTestResult(`❌ Alert failed: ${error}`);
    }
  };

  const testSafetyZones = async () => {
    try {
      await zonesQuery.refetch();
      addTestResult(`✅ Zones: ${zonesQuery.data?.length || 0} zones loaded`);
    } catch (error) {
      addTestResult(`❌ Zones failed: ${error}`);
    }
  };

  const testLocationReport = async () => {
    try {
      // Ubicación de prueba (Pucón)
      await reportLocation({
        latitude: -39.2706,
        longitude: -71.9728,
        accuracy: 10,
        timestamp: new Date().toISOString(),
        app_version: '1.0.0',
        device_type: 'test',
      });
      addTestResult(`✅ Location reported successfully`);
    } catch (error) {
      addTestResult(`❌ Location report failed: ${error}`);
    }
  };

  const testBulkSync = async () => {
    try {
      await bulkSync.mutateAsync();
      addTestResult(`✅ Bulk sync completed`);
    } catch (error) {
      addTestResult(`❌ Bulk sync failed: ${error}`);
    }
  };

  const testWebSocket = () => {
    if (isConnected) {
      requestSync({ test: true });
      wsReportLocation({ lat: -39.2706, lng: -71.9728 });
      addTestResult(`✅ WebSocket test sent`);
    } else {
      addTestResult(`❌ WebSocket not connected`);
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  const styles = StyleSheet.create({
    container: {
      backgroundColor: colors.background,
      borderRadius: BorderRadius.lg,
      padding: Spacing.md,
      margin: Spacing.md,
      ...Layout.card,
    },
    
    header: {
      marginBottom: Spacing.md,
    },
    
    buttonGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: Spacing.sm,
      marginBottom: Spacing.md,
    },
    
    button: {
      flex: 1,
      minWidth: '45%',
    },
    
    statusSection: {
      backgroundColor: colors.backgroundSecondary,
      padding: Spacing.sm,
      borderRadius: BorderRadius.md,
      marginBottom: Spacing.md,
    },
    
    statusRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: Spacing.xs,
    },
    
    resultsSection: {
      backgroundColor: colors.backgroundSecondary,
      padding: Spacing.sm,
      borderRadius: BorderRadius.md,
      maxHeight: 200,
    },
    
    resultItem: {
      paddingVertical: Spacing.xs,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
  });

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <AccessibleText variant="h3">🧪 API Test Panel</AccessibleText>
        <AccessibleText variant="body" color="secondary">
          Prueba la integración con el backend
        </AccessibleText>
      </View>

      {/* Estado de conexiones */}
      <View style={styles.statusSection}>
        <AccessibleText variant="button" style={{ marginBottom: Spacing.sm }}>
          Estado de Conexiones
        </AccessibleText>
        
        <View style={styles.statusRow}>
          <AccessibleText variant="body">API Health:</AccessibleText>
          <AccessibleText 
            variant="body" 
            color={healthQuery.isError ? "error" : healthQuery.isSuccess ? "success" : "secondary"}
          >
            {healthQuery.isLoading ? "⏳" : healthQuery.isError ? "❌" : "✅"}
          </AccessibleText>
        </View>
        
        <View style={styles.statusRow}>
          <AccessibleText variant="body">WebSocket:</AccessibleText>
          <AccessibleText 
            variant="body" 
            color={isConnected ? "success" : "error"}
          >
            {isConnected ? "✅ Conectado" : "❌ Desconectado"}
          </AccessibleText>
        </View>
        
        <View style={styles.statusRow}>
          <AccessibleText variant="body">Datos cargados:</AccessibleText>
          <AccessibleText variant="body" color="secondary">
            Config: {configQuery.isSuccess ? "✅" : "❌"} | 
            Alert: {alertQuery.isSuccess ? "✅" : "❌"} | 
            Zones: {zonesQuery.isSuccess ? "✅" : "❌"}
          </AccessibleText>
        </View>
      </View>

      {/* Botones de prueba */}
      <View style={styles.buttonGrid}>
        <SecondaryButton style={styles.button} onPress={testHealthCheck}>
          Health
        </SecondaryButton>
        <SecondaryButton style={styles.button} onPress={testMobileConfig}>
          Config
        </SecondaryButton>
        <SecondaryButton style={styles.button} onPress={testCurrentAlert}>
          Alert
        </SecondaryButton>
        <SecondaryButton style={styles.button} onPress={testSafetyZones}>
          Zones
        </SecondaryButton>
        <SecondaryButton style={styles.button} onPress={testLocationReport}>
          Location
        </SecondaryButton>
        <SecondaryButton style={styles.button} onPress={testBulkSync}>
          Sync
        </SecondaryButton>
        <SecondaryButton style={styles.button} onPress={testWebSocket}>
          WebSocket
        </SecondaryButton>
        <PrimaryButton style={styles.button} onPress={clearResults}>
          Clear
        </PrimaryButton>
      </View>

      {/* Resultados de pruebas */}
      <View style={styles.resultsSection}>
        <AccessibleText variant="button" style={{ marginBottom: Spacing.sm }}>
          Resultados de Pruebas
        </AccessibleText>
        <ScrollView showsVerticalScrollIndicator={false}>
          {testResults.length === 0 ? (
            <AccessibleText variant="body" color="muted">
              Presiona los botones para probar la API
            </AccessibleText>
          ) : (
            testResults.map((result, index) => (
              <View key={index} style={styles.resultItem}>
                <AccessibleText variant="bodySmall" color="secondary">
                  {result}
                </AccessibleText>
              </View>
            ))
          )}
        </ScrollView>
      </View>
    </View>
  );
}
