/**
 * 🌋 Volcano App Mobile - Tests para servicio de API
 * Tests de integración para el servicio de API móvil
 */

import { apiService, LocationReport } from '../api';

// Mock para axios
jest.mock('axios', () => ({
  create: jest.fn(() => ({
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    delete: jest.fn(),
    interceptors: {
      request: { use: jest.fn() },
      response: { use: jest.fn() }
    }
  })),
  get: jest.fn(),
  post: jest.fn(),
  put: jest.fn(),
  delete: jest.fn()
}));

// Mock para expo-constants
jest.mock('expo-constants', () => ({
  expoConfig: {
    extra: {
      apiUrl: 'https://test-api.volcanoapp.com'
    }
  }
}));

describe('API Service', () => {
  const mockAxios = require('axios');
  let mockAxiosInstance: any;

  beforeEach(() => {
    jest.clearAllMocks();
    mockAxiosInstance = {
      get: jest.fn(),
      post: jest.fn(),
      put: jest.fn(),
      delete: jest.fn(),
      interceptors: {
        request: { use: jest.fn() },
        response: { use: jest.fn() }
      }
    };
    mockAxios.create.mockReturnValue(mockAxiosInstance);
  });

  describe('Health Check', () => {
    it('performs health check successfully', async () => {
      const mockResponse = {
        data: {
          success: true,
          status: 'healthy',
          timestamp: new Date().toISOString()
        }
      };

      mockAxiosInstance.get.mockResolvedValue(mockResponse);

      const result = await apiService.healthCheck();

      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/health');
      expect(result).toEqual(mockResponse.data);
    });

    it('handles health check error', async () => {
      const mockError = new Error('Network error');
      mockAxiosInstance.get.mockRejectedValue(mockError);

      await expect(apiService.healthCheck()).rejects.toThrow('Network error');
    });
  });

  describe('Mobile Configuration', () => {
    it('fetches mobile config successfully', async () => {
      const mockConfig = {
        mobile_app_version: '1.0.0',
        emergency_contact: '+56912345678',
        update_interval: 60000,
        features: {
          realtime_notifications: true,
          location_tracking: true
        }
      };

      mockAxiosInstance.get.mockResolvedValue({
        data: {
          success: true,
          data: mockConfig
        }
      });

      const result = await apiService.getMobileConfig();

      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/mobile/config');
      expect(result).toEqual(mockConfig);
    });

    it('handles config fetch error', async () => {
      mockAxiosInstance.get.mockRejectedValue(new Error('Config error'));

      await expect(apiService.getMobileConfig()).rejects.toThrow('Config error');
    });
  });

  describe('Current Alert', () => {
    it('fetches current alert successfully', async () => {
      const mockAlert = {
        id: '1',
        title: 'Alerta Volcánica Amarilla',
        message: 'Actividad volcánica moderada',
        alert_level: 'YELLOW',
        volcano_name: 'Villarrica',
        is_active: true,
        created_at: '2024-01-01T00:00:00Z'
      };

      mockAxiosInstance.get.mockResolvedValue({
        data: {
          success: true,
          data: mockAlert
        }
      });

      const result = await apiService.getCurrentAlert();

      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/mobile/alerts/current');
      expect(result).toEqual(mockAlert);
    });

    it('handles no current alert', async () => {
      mockAxiosInstance.get.mockResolvedValue({
        data: {
          success: true,
          data: null
        }
      });

      const result = await apiService.getCurrentAlert();

      expect(result).toBeNull();
    });
  });

  describe('Safety Zones', () => {
    it('fetches safety zones successfully', async () => {
      const mockZones = [
        {
          id: '1',
          name: 'Zona Segura Centro',
          zone_type: 'SAFE',
          geometry: {
            type: 'Polygon',
            coordinates: [[[-71.9048, -39.2904]]]
          },
          is_active: true
        },
        {
          id: '2',
          name: 'Zona de Peligro Norte',
          zone_type: 'DANGER',
          geometry: {
            type: 'Polygon',
            coordinates: [[[-71.9100, -39.2800]]]
          },
          is_active: true
        }
      ];

      mockAxiosInstance.get.mockResolvedValue({
        data: {
          success: true,
          data: mockZones
        }
      });

      const result = await apiService.getSafetyZones();

      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/mobile/zones/all');
      expect(result).toEqual(mockZones);
    });

    it('filters active zones only', async () => {
      const mockZones = [
        {
          id: '1',
          name: 'Zona Activa',
          zone_type: 'SAFE',
          is_active: true
        },
        {
          id: '2',
          name: 'Zona Inactiva',
          zone_type: 'SAFE',
          is_active: false
        }
      ];

      mockAxiosInstance.get.mockResolvedValue({
        data: {
          success: true,
          data: mockZones
        }
      });

      const result = await apiService.getSafetyZones();

      // Debería devolver todas las zonas (el filtrado se hace en el backend)
      expect(result).toEqual(mockZones);
    });
  });

  describe('Location Reporting', () => {
    it('reports location successfully', async () => {
      const locationReport: LocationReport = {
        latitude: -39.2904,
        longitude: -71.9048,
        accuracy: 10,
        timestamp: new Date().toISOString(),
        app_version: '1.0.0',
        device_type: 'ios'
      };

      mockAxiosInstance.post.mockResolvedValue({
        data: {
          success: true,
          data: {
            id: 'report-1',
            status: 'received'
          }
        }
      });

      const result = await apiService.reportLocation(locationReport);

      expect(mockAxiosInstance.post).toHaveBeenCalledWith('/mobile/location/report', locationReport);
      expect(result).toEqual({
        id: 'report-1',
        status: 'received'
      });
    });

    it('handles location report error', async () => {
      const locationReport: LocationReport = {
        latitude: -39.2904,
        longitude: -71.9048,
        accuracy: 10,
        timestamp: new Date().toISOString()
      };

      mockAxiosInstance.post.mockRejectedValue(new Error('Report error'));

      await expect(apiService.reportLocation(locationReport)).rejects.toThrow('Report error');
    });

    it('validates location coordinates', async () => {
      const invalidLocationReport: LocationReport = {
        latitude: 100, // Inválida
        longitude: 200, // Inválida
        accuracy: 10,
        timestamp: new Date().toISOString()
      };

      mockAxiosInstance.post.mockRejectedValue({
        response: {
          status: 400,
          data: {
            success: false,
            error: 'Invalid coordinates'
          }
        }
      });

      await expect(apiService.reportLocation(invalidLocationReport)).rejects.toThrow();
    });
  });

  describe('Location Check', () => {
    it('checks location in zones successfully', async () => {
      const mockResponse = {
        in_safe_zone: true,
        in_danger_zone: false,
        nearest_safe_zone: {
          id: '1',
          name: 'Zona Segura Centro',
          distance: 500
        },
        recommendations: [
          'Manténgase en zona segura',
          'Monitoree alertas oficiales'
        ]
      };

      mockAxiosInstance.post.mockResolvedValue({
        data: {
          success: true,
          data: mockResponse
        }
      });

      const result = await apiService.checkLocationInZones(-39.2904, -71.9048);

      expect(mockAxiosInstance.post).toHaveBeenCalledWith('/mobile/location/check', {
        latitude: -39.2904,
        longitude: -71.9048
      });
      expect(result).toEqual(mockResponse);
    });

    it('handles location check error', async () => {
      mockAxiosInstance.post.mockRejectedValue(new Error('Check error'));

      await expect(apiService.checkLocationInZones(-39.2904, -71.9048))
        .rejects.toThrow('Check error');
    });
  });

  describe('App Version Check', () => {
    it('checks app version successfully', async () => {
      const mockResponse = {
        is_supported: true,
        latest_version: '1.1.0',
        update_required: false,
        update_url: 'https://app.store/volcano-app'
      };

      mockAxiosInstance.get.mockResolvedValue({
        data: {
          success: true,
          data: mockResponse
        }
      });

      const result = await apiService.checkAppVersion('1.0.0');

      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/mobile/version/1.0.0');
      expect(result).toEqual(mockResponse);
    });

    it('handles outdated version', async () => {
      const mockResponse = {
        is_supported: false,
        latest_version: '2.0.0',
        update_required: true,
        update_url: 'https://app.store/volcano-app'
      };

      mockAxiosInstance.get.mockResolvedValue({
        data: {
          success: true,
          data: mockResponse
        }
      });

      const result = await apiService.checkAppVersion('1.0.0');

      expect(result.update_required).toBe(true);
    });
  });

  describe('Bulk Sync', () => {
    it('performs bulk sync successfully', async () => {
      const mockSyncData = {
        alerts: [
          {
            id: '1',
            title: 'Alerta Actual',
            alert_level: 'YELLOW'
          }
        ],
        zones: [
          {
            id: '1',
            name: 'Zona Segura',
            zone_type: 'SAFE'
          }
        ],
        config: {
          mobile_app_version: '1.0.0',
          emergency_contact: '+56912345678'
        },
        last_sync: new Date().toISOString()
      };

      mockAxiosInstance.get.mockResolvedValue({
        data: {
          success: true,
          data: mockSyncData
        }
      });

      const result = await apiService.bulkSync('2024-01-01T00:00:00Z');

      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/mobile/sync', {
        params: { since: '2024-01-01T00:00:00Z' }
      });
      expect(result).toEqual(mockSyncData);
    });

    it('handles initial sync without timestamp', async () => {
      const mockSyncData = {
        alerts: [],
        zones: [],
        config: {},
        last_sync: new Date().toISOString()
      };

      mockAxiosInstance.get.mockResolvedValue({
        data: {
          success: true,
          data: mockSyncData
        }
      });

      const result = await apiService.bulkSync();

      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/mobile/sync', {
        params: {}
      });
      expect(result).toEqual(mockSyncData);
    });
  });

  describe('Error Handling', () => {
    it('handles network timeout', async () => {
      const timeoutError = new Error('timeout of 5000ms exceeded');
      timeoutError.name = 'ECONNABORTED';
      mockAxiosInstance.get.mockRejectedValue(timeoutError);

      await expect(apiService.healthCheck()).rejects.toThrow('timeout of 5000ms exceeded');
    });

    it('handles server error responses', async () => {
      const serverError = {
        response: {
          status: 500,
          data: {
            success: false,
            error: 'Internal server error'
          }
        }
      };

      mockAxiosInstance.get.mockRejectedValue(serverError);

      await expect(apiService.healthCheck()).rejects.toThrow();
    });

    it('handles malformed responses', async () => {
      mockAxiosInstance.get.mockResolvedValue({
        data: 'invalid-json'
      });

      const result = await apiService.healthCheck();

      expect(result).toBe('invalid-json');
    });
  });

  describe('Request Interceptors', () => {
    it('sets up request interceptors correctly', () => {
      expect(mockAxiosInstance.interceptors.request.use).toHaveBeenCalled();
    });

    it('sets up response interceptors correctly', () => {
      expect(mockAxiosInstance.interceptors.response.use).toHaveBeenCalled();
    });
  });
});
