/**
 * 🌋 Volcano App Backend - WebSocket Service
 * Real-time data synchronization for mobile apps and backoffice
 */

import { Server as HttpServer } from 'http';
import { Server as SocketIOServer, Socket } from 'socket.io';
import jwt from 'jsonwebtoken';
import { logger } from '@/utils/logger';
import { CONFIG } from '@/config/env';
import { setCache, getCache, CACHE_KEYS, CACHE_TTL } from './cache';
import { supabaseAdmin } from './supabase';

// =====================================================
// TYPES AND INTERFACES
// =====================================================

interface AuthenticatedSocket extends Socket {
  userId?: string;
  userRole?: string;
  isAuthenticated?: boolean;
  deviceId?: string;
  appVersion?: string;
}

interface WebSocketMessage {
  type: string;
  data: any;
  timestamp: Date;
  source?: string;
}

interface ConnectionInfo {
  socketId: string;
  userId?: string;
  userRole?: string;
  deviceId?: string;
  appVersion?: string;
  connectedAt: Date;
  lastActivity: Date;
}

// =====================================================
// WEBSOCKET SERVER INSTANCE
// =====================================================

let io: SocketIOServer | null = null;
const connections = new Map<string, ConnectionInfo>();

// =====================================================
// WEBSOCKET EVENTS
// =====================================================

export const WEBSOCKET_EVENTS = {
  // Connection events
  CONNECTION: 'connection',
  DISCONNECT: 'disconnect',
  AUTHENTICATE: 'authenticate',
  
  // Alert events
  ALERT_CREATED: 'alert:created',
  ALERT_UPDATED: 'alert:updated',
  ALERT_DELETED: 'alert:deleted',
  ALERT_ACTIVATED: 'alert:activated',
  ALERT_DEACTIVATED: 'alert:deactivated',
  
  // Zone events
  ZONE_CREATED: 'zone:created',
  ZONE_UPDATED: 'zone:updated',
  ZONE_DELETED: 'zone:deleted',
  
  // Location events
  LOCATION_UPDATE: 'location:update',
  LOCATION_BATCH: 'location:batch',
  
  // System events
  SYSTEM_STATUS: 'system:status',
  CONFIG_UPDATED: 'config:updated',
  
  // Mobile specific events
  MOBILE_SYNC_REQUEST: 'mobile:sync:request',
  MOBILE_SYNC_RESPONSE: 'mobile:sync:response',
  MOBILE_HEARTBEAT: 'mobile:heartbeat',
  
  // Error events
  ERROR: 'error',
  VALIDATION_ERROR: 'validation:error'
} as const;

// =====================================================
// WEBSOCKET ROOMS
// =====================================================

export const WEBSOCKET_ROOMS = {
  // Admin rooms
  ADMINS: 'admins',
  OPERATORS: 'operators',
  VIEWERS: 'viewers',
  
  // Mobile rooms
  MOBILE_USERS: 'mobile:users',
  MOBILE_ACTIVE: 'mobile:active',
  
  // Geographic rooms
  ZONE_ALERTS: (zoneId: string) => `zone:${zoneId}`,
  LOCATION_UPDATES: 'location:updates',
  
  // System rooms
  SYSTEM_MONITORING: 'system:monitoring',
  AUDIT_LOGS: 'audit:logs'
} as const;

// =====================================================
// WEBSOCKET INITIALIZATION
// =====================================================

/**
 * Initialize WebSocket server
 */
export function initializeWebSocket(httpServer: HttpServer): SocketIOServer {
  io = new SocketIOServer(httpServer, {
    cors: {
      origin: CONFIG.CORS.CORS_ORIGIN,
      credentials: CONFIG.CORS.CORS_CREDENTIALS,
      methods: ['GET', 'POST']
    },
    transports: ['websocket', 'polling'],
    pingTimeout: 60000,
    pingInterval: 25000,
    upgradeTimeout: 10000,
    maxHttpBufferSize: 1e6 // 1MB
  });

  // Authentication middleware
  io.use(async (socket: AuthenticatedSocket, next) => {
    try {
      const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.replace('Bearer ', '');
      const deviceId = socket.handshake.auth.deviceId;
      const appVersion = socket.handshake.auth.appVersion;
      
      // Allow unauthenticated connections for mobile apps
      if (!token) {
        socket.isAuthenticated = false;
        socket.deviceId = deviceId;
        socket.appVersion = appVersion;
        return next();
      }

      // Verify JWT token
      const decoded = jwt.verify(token, CONFIG.JWT.JWT_SECRET) as any;
      
      // Get user info from database
      const { data: user, error } = await supabaseAdmin
        .from('admin_users')
        .select('id, email, role, is_active')
        .eq('id', decoded.user_id)
        .eq('is_active', true)
        .single();

      if (error || !user) {
        return next(new Error('Authentication failed'));
      }

      socket.userId = user.id;
      socket.userRole = user.role;
      socket.isAuthenticated = true;
      socket.deviceId = deviceId;
      socket.appVersion = appVersion;
      
      next();
    } catch (error) {
      logger.error('WebSocket authentication error:', error);
      next(new Error('Authentication failed'));
    }
  });

  // Connection handler
  io.on(WEBSOCKET_EVENTS.CONNECTION, handleConnection);

  logger.info('WebSocket server initialized');
  return io;
}

/**
 * Handle new WebSocket connection
 */
async function handleConnection(socket: AuthenticatedSocket) {
  const connectionInfo: ConnectionInfo = {
    socketId: socket.id,
    userId: socket.userId,
    userRole: socket.userRole,
    deviceId: socket.deviceId,
    appVersion: socket.appVersion,
    connectedAt: new Date(),
    lastActivity: new Date()
  };

  connections.set(socket.id, connectionInfo);

  logger.info('WebSocket connection established', {
    socketId: socket.id,
    userId: socket.userId,
    userRole: socket.userRole,
    deviceId: socket.deviceId,
    isAuthenticated: socket.isAuthenticated
  });

  // Join appropriate rooms
  await joinRooms(socket);

  // Send initial data
  await sendInitialData(socket);

  // Set up event handlers
  setupEventHandlers(socket);

  // Update connection cache
  await updateConnectionCache();
}

/**
 * Join appropriate rooms based on user role and type
 */
async function joinRooms(socket: AuthenticatedSocket) {
  try {
    if (socket.isAuthenticated && socket.userRole) {
      // Join role-based rooms
      switch (socket.userRole) {
        case 'ADMIN':
          await socket.join(WEBSOCKET_ROOMS.ADMINS);
          await socket.join(WEBSOCKET_ROOMS.OPERATORS);
          await socket.join(WEBSOCKET_ROOMS.VIEWERS);
          await socket.join(WEBSOCKET_ROOMS.SYSTEM_MONITORING);
          await socket.join(WEBSOCKET_ROOMS.AUDIT_LOGS);
          break;
        case 'OPERATOR':
          await socket.join(WEBSOCKET_ROOMS.OPERATORS);
          await socket.join(WEBSOCKET_ROOMS.VIEWERS);
          break;
        case 'VIEWER':
          await socket.join(WEBSOCKET_ROOMS.VIEWERS);
          break;
      }
    } else {
      // Mobile app or unauthenticated connection
      await socket.join(WEBSOCKET_ROOMS.MOBILE_USERS);
      if (socket.deviceId) {
        await socket.join(WEBSOCKET_ROOMS.MOBILE_ACTIVE);
      }
    }

    // All connections join location updates room
    await socket.join(WEBSOCKET_ROOMS.LOCATION_UPDATES);
  } catch (error) {
    logger.error('Error joining WebSocket rooms:', error);
  }
}

/**
 * Send initial data to newly connected client
 */
async function sendInitialData(socket: AuthenticatedSocket) {
  try {
    // Send current alert
    const currentAlert = await getCache(CACHE_KEYS.CURRENT_ALERT);
    if (currentAlert) {
      socket.emit(WEBSOCKET_EVENTS.ALERT_UPDATED, {
        type: 'current_alert',
        data: currentAlert,
        timestamp: new Date()
      });
    }

    // Send active zones for mobile clients
    if (!socket.isAuthenticated || socket.deviceId) {
      const activeZones = await getCache(CACHE_KEYS.ACTIVE_ZONES);
      if (activeZones) {
        socket.emit(WEBSOCKET_EVENTS.ZONE_UPDATED, {
          type: 'active_zones',
          data: activeZones,
          timestamp: new Date()
        });
      }
    }

    // Send system status for authenticated users
    if (socket.isAuthenticated && socket.userRole !== 'VIEWER') {
      const systemStats = await getCache(CACHE_KEYS.SYSTEM_STATS);
      if (systemStats) {
        socket.emit(WEBSOCKET_EVENTS.SYSTEM_STATUS, {
          type: 'system_stats',
          data: systemStats,
          timestamp: new Date()
        });
      }
    }
  } catch (error) {
    logger.error('Error sending initial data:', error);
  }
}

/**
 * Set up event handlers for socket
 */
function setupEventHandlers(socket: AuthenticatedSocket) {
  // Heartbeat for mobile apps
  socket.on(WEBSOCKET_EVENTS.MOBILE_HEARTBEAT, async (data) => {
    const connectionInfo = connections.get(socket.id);
    if (connectionInfo) {
      connectionInfo.lastActivity = new Date();
      connections.set(socket.id, connectionInfo);
    }
    
    socket.emit(WEBSOCKET_EVENTS.MOBILE_HEARTBEAT, {
      timestamp: new Date(),
      status: 'ok'
    });
  });

  // Mobile sync request
  socket.on(WEBSOCKET_EVENTS.MOBILE_SYNC_REQUEST, async (data) => {
    try {
      const syncData = await getMobileSyncData(data.lastSync);
      socket.emit(WEBSOCKET_EVENTS.MOBILE_SYNC_RESPONSE, {
        type: 'sync_data',
        data: syncData,
        timestamp: new Date()
      });
    } catch (error) {
      logger.error('Mobile sync error:', error);
      socket.emit(WEBSOCKET_EVENTS.ERROR, {
        type: 'sync_error',
        message: 'Failed to sync data',
        timestamp: new Date()
      });
    }
  });

  // Location update from mobile
  socket.on(WEBSOCKET_EVENTS.LOCATION_UPDATE, async (data) => {
    try {
      // Validate location data
      if (!data.latitude || !data.longitude) {
        socket.emit(WEBSOCKET_EVENTS.VALIDATION_ERROR, {
          message: 'Invalid location data',
          timestamp: new Date()
        });
        return;
      }

      // Broadcast to location monitoring room
      socket.to(WEBSOCKET_ROOMS.LOCATION_UPDATES).emit(WEBSOCKET_EVENTS.LOCATION_UPDATE, {
        type: 'location_update',
        data: {
          deviceId: socket.deviceId,
          ...data,
          timestamp: new Date()
        }
      });
    } catch (error) {
      logger.error('Location update error:', error);
    }
  });

  // Disconnect handler
  socket.on(WEBSOCKET_EVENTS.DISCONNECT, async (reason) => {
    logger.info('WebSocket disconnection', {
      socketId: socket.id,
      userId: socket.userId,
      reason
    });

    connections.delete(socket.id);
    await updateConnectionCache();
  });

  // Error handler
  socket.on('error', (error) => {
    logger.error('WebSocket error:', error);
  });
}

// =====================================================
// BROADCAST FUNCTIONS
// =====================================================

/**
 * Broadcast alert update to all connected clients
 */
export async function broadcastAlertUpdate(alert: any, action: 'created' | 'updated' | 'deleted' | 'activated' | 'deactivated') {
  if (!io) return;

  const event = `alert:${action}` as keyof typeof WEBSOCKET_EVENTS;
  const message: WebSocketMessage = {
    type: 'alert_update',
    data: alert,
    timestamp: new Date(),
    source: 'backoffice'
  };

  // Broadcast to all rooms
  io.emit(WEBSOCKET_EVENTS[event], message);
  
  logger.info(`Broadcasted alert ${action}`, { alertId: alert.id });
}

/**
 * Broadcast zone update to all connected clients
 */
export async function broadcastZoneUpdate(zone: any, action: 'created' | 'updated' | 'deleted') {
  if (!io) return;

  const event = `zone:${action}` as keyof typeof WEBSOCKET_EVENTS;
  const message: WebSocketMessage = {
    type: 'zone_update',
    data: zone,
    timestamp: new Date(),
    source: 'backoffice'
  };

  io.emit(WEBSOCKET_EVENTS[event], message);
  
  logger.info(`Broadcasted zone ${action}`, { zoneId: zone.id });
}

/**
 * Broadcast system status update
 */
export async function broadcastSystemStatus(status: any) {
  if (!io) return;

  const message: WebSocketMessage = {
    type: 'system_status',
    data: status,
    timestamp: new Date(),
    source: 'system'
  };

  io.to(WEBSOCKET_ROOMS.SYSTEM_MONITORING).emit(WEBSOCKET_EVENTS.SYSTEM_STATUS, message);
}

// =====================================================
// UTILITY FUNCTIONS
// =====================================================

/**
 * Get mobile sync data based on last sync timestamp
 */
async function getMobileSyncData(lastSync?: string): Promise<any> {
  const syncTimestamp = lastSync ? new Date(lastSync) : new Date(0);
  
  // Get updated alerts
  const { data: alerts } = await supabaseAdmin
    .from('volcano_alerts')
    .select('*')
    .eq('is_active', true)
    .gte('updated_at', syncTimestamp.toISOString());

  // Get updated zones
  const { data: zones } = await supabaseAdmin
    .from('safety_zones')
    .select('*')
    .eq('is_active', true)
    .gte('updated_at', syncTimestamp.toISOString());

  return {
    alerts: alerts || [],
    zones: zones || [],
    syncTimestamp: new Date().toISOString()
  };
}

/**
 * Update connection cache
 */
async function updateConnectionCache() {
  const connectionData = Array.from(connections.values());
  await setCache(CACHE_KEYS.ACTIVE_CONNECTIONS, connectionData, CACHE_TTL.SHORT);
}

/**
 * Get WebSocket server instance
 */
export function getWebSocketServer(): SocketIOServer | null {
  return io;
}

/**
 * Get active connections count
 */
export function getActiveConnectionsCount(): number {
  return connections.size;
}

/**
 * Get connections by user role
 */
export function getConnectionsByRole(role: string): ConnectionInfo[] {
  return Array.from(connections.values()).filter(conn => conn.userRole === role);
}

// =====================================================
// EXPORTS
// =====================================================

export default {
  initializeWebSocket,
  broadcastAlertUpdate,
  broadcastZoneUpdate,
  broadcastSystemStatus,
  getWebSocketServer,
  getActiveConnectionsCount,
  getConnectionsByRole,
  WEBSOCKET_EVENTS,
  WEBSOCKET_ROOMS
};
