/**
 * 🌋 Volcano App Backend - Servidor Simplificado
 * Versión básica para testing y desarrollo inicial
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// =====================================================
// CONFIGURACIÓN DEL SERVIDOR
// =====================================================

const app = express();
const PORT = process.env.PORT || 3001;
const HOST = process.env.HOST || 'localhost';
const NODE_ENV = process.env.NODE_ENV || 'development';

console.log('🌋 Starting Volcano App Backend...');
console.log(`📊 Environment: ${NODE_ENV}`);
console.log(`🔧 Port: ${PORT}`);

// =====================================================
// CONFIGURACIÓN DE SUPABASE
// =====================================================

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

// Cliente público (con RLS habilitado)
const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: false
  }
});

// Cliente administrativo (bypassa RLS)
const supabaseAdmin = createClient(
  supabaseUrl, 
  supabaseServiceKey || supabaseAnonKey,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

console.log('✅ Supabase clients initialized');

// =====================================================
// MIDDLEWARE DE SEGURIDAD
// =====================================================

// Helmet para headers de seguridad
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
  crossOriginEmbedderPolicy: false
}));

// CORS
const corsOptions = {
  origin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:5173', 'http://localhost:3000'],
  credentials: process.env.CORS_CREDENTIALS === 'true',
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
};

app.use(cors(corsOptions));

// Rate limiting
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15 minutos
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'),
  message: {
    success: false,
    error: 'Too many requests, please try again later',
    timestamp: new Date()
  },
  standardHeaders: true,
  legacyHeaders: false,
});

app.use('/api/', limiter);

// =====================================================
// MIDDLEWARE GENERAL
// =====================================================

// Compresión
app.use(compression());

// Parsing de JSON y URL
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Logging de requests
app.use(morgan('combined'));

// =====================================================
// FUNCIONES DE UTILIDAD
// =====================================================

/**
 * Verifica la conexión con la base de datos
 */
async function checkDatabaseConnection() {
  try {
    const { data, error } = await supabase
      .from('system_config')
      .select('key')
      .limit(1);

    if (error) {
      console.error('Database connection check failed:', error);
      return false;
    }

    console.log('✅ Database connection successful');
    return true;
  } catch (error) {
    console.error('Database connection error:', error);
    return false;
  }
}

/**
 * Health check de servicios
 */
async function healthCheck() {
  const health = {
    database: false,
    server: true,
    timestamp: new Date()
  };

  try {
    health.database = await checkDatabaseConnection();
  } catch (error) {
    console.error('Health check error:', error);
  }

  return health;
}

// =====================================================
// RUTAS DE SALUD Y ESTADO
// =====================================================

// Health check básico
app.get('/health', async (req, res) => {
  try {
    const health = await healthCheck();
    const status = Object.values(health).every(val => val === true || val instanceof Date) ? 'healthy' : 'unhealthy';
    
    res.status(status === 'healthy' ? 200 : 503).json({
      status,
      timestamp: new Date(),
      services: health,
      version: '1.0.0',
      environment: NODE_ENV
    });
  } catch (error) {
    console.error('Health check error:', error);
    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date(),
      error: 'Health check failed'
    });
  }
});

// Status endpoint más detallado
app.get('/status', async (req, res) => {
  try {
    const health = await healthCheck();
    
    res.json({
      success: true,
      data: {
        server: {
          status: 'running',
          uptime: process.uptime(),
          memory: process.memoryUsage(),
          version: '1.0.0',
          environment: NODE_ENV,
          node_version: process.version
        },
        services: health,
        timestamp: new Date()
      }
    });
  } catch (error) {
    console.error('Status check error:', error);
    res.status(500).json({
      success: false,
      error: 'Status check failed',
      timestamp: new Date()
    });
  }
});

// =====================================================
// UTILIDADES DE AUDITORÍA
// =====================================================

/**
 * Registra una acción en el log de auditoría
 */
async function logAuditAction(action, tableName, recordId, userId = null, oldData = null, newData = null) {
  try {
    const auditLog = {
      action: action.toUpperCase(),
      table_name: tableName,
      record_id: recordId,
      user_id: userId,
      old_values: oldData,
      new_values: newData,
      ip_address: '127.0.0.1', // En producción, obtener la IP real
      user_agent: 'Volcano App Backend'
    };

    const { error } = await supabaseAdmin
      .from('audit_logs')
      .insert([auditLog]);

    if (error) {
      console.error('Error logging audit action:', error);
    }
  } catch (error) {
    console.error('Failed to log audit action:', error);
  }
}

/**
 * Middleware de validación básica
 */
function validateRequired(fields) {
  return (req, res, next) => {
    const missing = fields.filter(field => !req.body[field]);
    if (missing.length > 0) {
      return res.status(400).json({
        success: false,
        error: `Missing required fields: ${missing.join(', ')}`,
        timestamp: new Date()
      });
    }
    next();
  };
}

// =====================================================
// RUTAS DE API - ALERTAS VOLCÁNICAS
// =====================================================

// GET /api/alerts - Obtener todas las alertas
app.get('/api/alerts', async (req, res) => {
  try {
    const { active, limit = 50, offset = 0 } = req.query;

    let query = supabase
      .from('volcano_alerts')
      .select('*')
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (active !== undefined) {
      query = query.eq('is_active', active === 'true');
    }

    const { data, error, count } = await query;

    if (error) {
      throw error;
    }

    res.json({
      success: true,
      data: data,
      total: count,
      timestamp: new Date()
    });
  } catch (error) {
    console.error('Get alerts error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get alerts',
      timestamp: new Date()
    });
  }
});

// GET /api/alerts/active - Obtener alertas activas (para compatibilidad)
app.get('/api/alerts/active', async (req, res) => {
  try {
    const { data, error } = await supabase
      .from('volcano_alerts')
      .select('*')
      .eq('is_active', true)
      .order('created_at', { ascending: false });

    if (error) {
      throw error;
    }

    res.json({
      success: true,
      data: data,
      timestamp: new Date()
    });
  } catch (error) {
    console.error('Active alerts error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get active alerts',
      timestamp: new Date()
    });
  }
});

// GET /api/alerts/:id - Obtener una alerta específica
app.get('/api/alerts/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const { data, error } = await supabase
      .from('volcano_alerts')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return res.status(404).json({
          success: false,
          error: 'Alert not found',
          timestamp: new Date()
        });
      }
      throw error;
    }

    res.json({
      success: true,
      data: data,
      timestamp: new Date()
    });
  } catch (error) {
    console.error('Get alert error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get alert',
      timestamp: new Date()
    });
  }
});

// POST /api/alerts - Crear nueva alerta
app.post('/api/alerts', validateRequired(['title', 'message', 'alert_level', 'volcano_name']), async (req, res) => {
  try {
    const {
      title,
      message,
      alert_level,
      volcano_name,
      volcano_lat = -39.420000,
      volcano_lng = -71.939167,
      is_active = true,
      is_scheduled = false,
      scheduled_for = null,
      expires_at = null,
      metadata = {}
    } = req.body;

    // Validar nivel de alerta
    const validLevels = ['NORMAL', 'ADVISORY', 'WATCH', 'WARNING', 'EMERGENCY'];
    if (!validLevels.includes(alert_level.toUpperCase())) {
      return res.status(400).json({
        success: false,
        error: `Invalid alert level. Must be one of: ${validLevels.join(', ')}`,
        timestamp: new Date()
      });
    }

    const alertData = {
      title,
      message,
      alert_level: alert_level.toUpperCase(),
      volcano_name,
      volcano_lat: parseFloat(volcano_lat),
      volcano_lng: parseFloat(volcano_lng),
      is_active,
      is_scheduled,
      scheduled_for,
      expires_at,
      created_by: null, // En producción, obtener del token JWT
      metadata
    };

    const { data, error } = await supabaseAdmin
      .from('volcano_alerts')
      .insert([alertData])
      .select()
      .single();

    if (error) {
      throw error;
    }

    // Log de auditoría
    await logAuditAction('CREATE', 'volcano_alerts', data.id, null, null, data);

    res.status(201).json({
      success: true,
      data: data,
      message: 'Alert created successfully',
      timestamp: new Date()
    });
  } catch (error) {
    console.error('Create alert error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create alert',
      timestamp: new Date()
    });
  }
});

// PUT /api/alerts/:id - Actualizar alerta
app.put('/api/alerts/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    // Obtener datos actuales para auditoría
    const { data: currentData, error: fetchError } = await supabase
      .from('volcano_alerts')
      .select('*')
      .eq('id', id)
      .single();

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return res.status(404).json({
          success: false,
          error: 'Alert not found',
          timestamp: new Date()
        });
      }
      throw fetchError;
    }

    // Validar nivel de alerta si se proporciona
    if (updateData.alert_level) {
      const validLevels = ['NORMAL', 'ADVISORY', 'WATCH', 'WARNING', 'EMERGENCY'];
      if (!validLevels.includes(updateData.alert_level.toUpperCase())) {
        return res.status(400).json({
          success: false,
          error: `Invalid alert level. Must be one of: ${validLevels.join(', ')}`,
          timestamp: new Date()
        });
      }
      updateData.alert_level = updateData.alert_level.toUpperCase();
    }

    // Actualizar timestamp
    updateData.updated_at = new Date().toISOString();

    const { data, error } = await supabaseAdmin
      .from('volcano_alerts')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw error;
    }

    // Log de auditoría
    await logAuditAction('UPDATE', 'volcano_alerts', id, null, currentData, data);

    res.json({
      success: true,
      data: data,
      message: 'Alert updated successfully',
      timestamp: new Date()
    });
  } catch (error) {
    console.error('Update alert error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update alert',
      timestamp: new Date()
    });
  }
});

// DELETE /api/alerts/:id - Eliminar alerta
app.delete('/api/alerts/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Obtener datos actuales para auditoría
    const { data: currentData, error: fetchError } = await supabase
      .from('volcano_alerts')
      .select('*')
      .eq('id', id)
      .single();

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return res.status(404).json({
          success: false,
          error: 'Alert not found',
          timestamp: new Date()
        });
      }
      throw fetchError;
    }

    const { error } = await supabaseAdmin
      .from('volcano_alerts')
      .delete()
      .eq('id', id);

    if (error) {
      throw error;
    }

    // Log de auditoría
    await logAuditAction('DELETE', 'volcano_alerts', id, null, currentData, null);

    res.json({
      success: true,
      message: 'Alert deleted successfully',
      timestamp: new Date()
    });
  } catch (error) {
    console.error('Delete alert error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete alert',
      timestamp: new Date()
    });
  }
});

// =====================================================
// RUTAS DE API - ZONAS DE SEGURIDAD
// =====================================================

// GET /api/zones - Obtener todas las zonas
app.get('/api/zones', async (req, res) => {
  try {
    const { active, type, limit = 50, offset = 0 } = req.query;

    let query = supabase
      .from('safety_zones')
      .select('*')
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (active !== undefined) {
      query = query.eq('is_active', active === 'true');
    }

    if (type) {
      query = query.eq('zone_type', type.toUpperCase());
    }

    const { data, error, count } = await query;

    if (error) {
      throw error;
    }

    res.json({
      success: true,
      data: data,
      total: count,
      timestamp: new Date()
    });
  } catch (error) {
    console.error('Get zones error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get zones',
      timestamp: new Date()
    });
  }
});

// GET /api/zones/:id - Obtener una zona específica
app.get('/api/zones/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const { data, error } = await supabase
      .from('safety_zones')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return res.status(404).json({
          success: false,
          error: 'Zone not found',
          timestamp: new Date()
        });
      }
      throw error;
    }

    res.json({
      success: true,
      data: data,
      timestamp: new Date()
    });
  } catch (error) {
    console.error('Get zone error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get zone',
      timestamp: new Date()
    });
  }
});

// POST /api/zones - Crear nueva zona
app.post('/api/zones', validateRequired(['name', 'zone_type', 'geometry']), async (req, res) => {
  try {
    const {
      name,
      description = '',
      zone_type,
      geometry,
      capacity = null,
      contact_info = {},
      facilities = {},
      is_active = true,
      metadata = {}
    } = req.body;

    // Validar tipo de zona
    const validTypes = ['SAFE', 'EMERGENCY', 'EVACUATION', 'DANGER'];
    if (!validTypes.includes(zone_type.toUpperCase())) {
      return res.status(400).json({
        success: false,
        error: `Invalid zone type. Must be one of: ${validTypes.join(', ')}`,
        timestamp: new Date()
      });
    }

    // Validar geometría básica
    if (!geometry || !geometry.type || !geometry.coordinates) {
      return res.status(400).json({
        success: false,
        error: 'Invalid geometry. Must include type and coordinates',
        timestamp: new Date()
      });
    }

    const zoneData = {
      name,
      description,
      zone_type: zone_type.toUpperCase(),
      geometry,
      capacity: capacity ? parseInt(capacity) : null,
      contact_info,
      facilities,
      is_active,
      version: 1,
      created_by: null, // En producción, obtener del token JWT
      metadata
    };

    const { data, error } = await supabaseAdmin
      .from('safety_zones')
      .insert([zoneData])
      .select()
      .single();

    if (error) {
      throw error;
    }

    // Log de auditoría
    await logAuditAction('CREATE', 'safety_zones', data.id, null, null, data);

    res.status(201).json({
      success: true,
      data: data,
      message: 'Zone created successfully',
      timestamp: new Date()
    });
  } catch (error) {
    console.error('Create zone error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create zone',
      timestamp: new Date()
    });
  }
});

// PUT /api/zones/:id - Actualizar zona
app.put('/api/zones/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    // Obtener datos actuales para auditoría
    const { data: currentData, error: fetchError } = await supabase
      .from('safety_zones')
      .select('*')
      .eq('id', id)
      .single();

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return res.status(404).json({
          success: false,
          error: 'Zone not found',
          timestamp: new Date()
        });
      }
      throw fetchError;
    }

    // Validar tipo de zona si se proporciona
    if (updateData.zone_type) {
      const validTypes = ['SAFE', 'EMERGENCY', 'EVACUATION', 'DANGER'];
      if (!validTypes.includes(updateData.zone_type.toUpperCase())) {
        return res.status(400).json({
          success: false,
          error: `Invalid zone type. Must be one of: ${validTypes.join(', ')}`,
          timestamp: new Date()
        });
      }
      updateData.zone_type = updateData.zone_type.toUpperCase();
    }

    // Incrementar versión y actualizar timestamp
    updateData.version = (currentData.version || 1) + 1;
    updateData.updated_at = new Date().toISOString();

    const { data, error } = await supabaseAdmin
      .from('safety_zones')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw error;
    }

    // Log de auditoría
    await logAuditAction('UPDATE', 'safety_zones', id, null, currentData, data);

    res.json({
      success: true,
      data: data,
      message: 'Zone updated successfully',
      timestamp: new Date()
    });
  } catch (error) {
    console.error('Update zone error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update zone',
      timestamp: new Date()
    });
  }
});

// DELETE /api/zones/:id - Eliminar zona
app.delete('/api/zones/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Obtener datos actuales para auditoría
    const { data: currentData, error: fetchError } = await supabase
      .from('safety_zones')
      .select('*')
      .eq('id', id)
      .single();

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return res.status(404).json({
          success: false,
          error: 'Zone not found',
          timestamp: new Date()
        });
      }
      throw fetchError;
    }

    const { error } = await supabaseAdmin
      .from('safety_zones')
      .delete()
      .eq('id', id);

    if (error) {
      throw error;
    }

    // Log de auditoría
    await logAuditAction('DELETE', 'safety_zones', id, null, currentData, null);

    res.json({
      success: true,
      message: 'Zone deleted successfully',
      timestamp: new Date()
    });
  } catch (error) {
    console.error('Delete zone error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete zone',
      timestamp: new Date()
    });
  }
});

// =====================================================
// RUTAS DE API - LOGS DE AUDITORÍA
// =====================================================

// GET /api/audit - Obtener logs de auditoría
app.get('/api/audit', async (req, res) => {
  try {
    const {
      table_name,
      action,
      user_id,
      limit = 100,
      offset = 0,
      start_date,
      end_date
    } = req.query;

    let query = supabaseAdmin
      .from('audit_logs')
      .select('*')
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (table_name) {
      query = query.eq('table_name', table_name);
    }

    if (action) {
      query = query.eq('action', action.toUpperCase());
    }

    if (user_id) {
      query = query.eq('user_id', user_id);
    }

    if (start_date) {
      query = query.gte('created_at', start_date);
    }

    if (end_date) {
      query = query.lte('created_at', end_date);
    }

    const { data, error, count } = await query;

    if (error) {
      throw error;
    }

    res.json({
      success: true,
      data: data,
      total: count,
      timestamp: new Date()
    });
  } catch (error) {
    console.error('Get audit logs error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get audit logs',
      timestamp: new Date()
    });
  }
});

// =====================================================
// RUTAS DE API - ENDPOINTS PARA APP MÓVIL
// =====================================================

// GET /api/mobile/alerts/current - Alerta actual para app móvil
app.get('/api/mobile/alerts/current', async (req, res) => {
  try {
    const { data, error } = await supabase
      .from('volcano_alerts')
      .select('*')
      .eq('is_active', true)
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    if (error && error.code !== 'PGRST116') {
      throw error;
    }

    res.json({
      success: true,
      data: data || null,
      timestamp: new Date()
    });
  } catch (error) {
    console.error('Get current alert error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get current alert',
      timestamp: new Date()
    });
  }
});

// GET /api/mobile/zones/all - Todas las zonas para app móvil
app.get('/api/mobile/zones/all', async (req, res) => {
  try {
    const { data, error } = await supabase
      .from('safety_zones')
      .select('id, name, description, zone_type, geometry, is_active, version')
      .eq('is_active', true)
      .order('zone_type', { ascending: true });

    if (error) {
      throw error;
    }

    res.json({
      success: true,
      data: data,
      timestamp: new Date()
    });
  } catch (error) {
    console.error('Get mobile zones error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get zones for mobile app',
      timestamp: new Date()
    });
  }
});

// GET /api/config - Obtener configuración del sistema
app.get('/api/config', async (req, res) => {
  try {
    const { data, error } = await supabase
      .from('system_config')
      .select('*')
      .limit(10);

    if (error) {
      throw error;
    }

    res.json({
      success: true,
      data: data,
      timestamp: new Date()
    });
  } catch (error) {
    console.error('Config error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get configuration',
      timestamp: new Date()
    });
  }
});

// Ruta de testing
app.get('/api/test', (req, res) => {
  res.json({
    success: true,
    message: 'Volcano App Backend API is running!',
    timestamp: new Date(),
    environment: NODE_ENV,
    endpoints: {
      alerts: ['GET /api/alerts', 'POST /api/alerts', 'PUT /api/alerts/:id', 'DELETE /api/alerts/:id'],
      zones: ['GET /api/zones', 'POST /api/zones', 'PUT /api/zones/:id', 'DELETE /api/zones/:id'],
      audit: ['GET /api/audit'],
      mobile: ['GET /api/mobile/alerts/current', 'GET /api/mobile/zones/all']
    }
  });
});

// =====================================================
// MANEJO DE ERRORES
// =====================================================

// Ruta no encontrada
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'Route not found',
    path: req.originalUrl,
    timestamp: new Date()
  });
});

// Middleware de manejo de errores
app.use((error, req, res, next) => {
  const statusCode = error.statusCode || 500;
  const message = error.message || 'Internal server error';
  
  console.error('Unhandled error:', {
    error: {
      message: error.message,
      stack: error.stack,
      name: error.name
    },
    request: {
      method: req.method,
      url: req.url,
      headers: req.headers
    }
  });

  res.status(statusCode).json({
    success: false,
    error: NODE_ENV === 'production' ? 'Internal server error' : message,
    timestamp: new Date()
  });
});

// =====================================================
// INICIALIZACIÓN DEL SERVIDOR
// =====================================================

async function startServer() {
  try {
    console.log('🔍 Checking database connection...');
    
    // Verificar conexión a Supabase
    const isConnected = await checkDatabaseConnection();
    if (!isConnected) {
      console.warn('⚠️  Database connection failed, but server will start anyway');
    }
    
    // Iniciar servidor
    const server = app.listen(PORT, HOST, () => {
      console.log(`🌋 Volcano App Backend running on http://${HOST}:${PORT}`);
      console.log(`📊 Environment: ${NODE_ENV}`);
      console.log(`🔍 Health check: http://${HOST}:${PORT}/health`);
      console.log(`🧪 Test endpoint: http://${HOST}:${PORT}/api/test`);
      console.log(`📡 API endpoints available:`);
      console.log(`   - GET /api/test`);
      console.log(`   - GET /api/config`);
      console.log(`   - GET /api/alerts/active`);
      console.log(`   - GET /api/zones`);
    });

    // Manejo de cierre graceful
    const gracefulShutdown = (signal) => {
      console.log(`Received ${signal}, shutting down gracefully...`);
      
      server.close(() => {
        console.log('Server closed');
        process.exit(0);
      });
      
      // Forzar cierre después de 10 segundos
      setTimeout(() => {
        console.error('Forced shutdown');
        process.exit(1);
      }, 10000);
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

  } catch (error) {
    console.error('Failed to start server:', error);
    process.exit(1);
  }
}

// =====================================================
// MANEJO DE EXCEPCIONES NO CAPTURADAS
// =====================================================

process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// =====================================================
// INICIAR SERVIDOR
// =====================================================

startServer();
