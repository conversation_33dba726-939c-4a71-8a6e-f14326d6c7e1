/**
 * 🌋 Volcano App - Script de Migración
 * Script para ejecutar la migración de coordenadas del centro
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Configuración de Supabase
const supabaseUrl = process.env.SUPABASE_URL || 'https://your-project.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'your-service-role-key';

if (!supabaseUrl || !supabaseServiceKey || supabaseUrl.includes('your-project') || supabaseServiceKey.includes('your-service')) {
  console.error('❌ Error: Debes configurar las variables de entorno SUPABASE_URL y SUPABASE_SERVICE_ROLE_KEY');
  console.log('Ejemplo:');
  console.log('export SUPABASE_URL="https://tu-proyecto.supabase.co"');
  console.log('export SUPABASE_SERVICE_ROLE_KEY="tu-service-role-key"');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function runMigration() {
  try {
    console.log('🚀 Iniciando migración de coordenadas del centro...');
    
    // Leer el archivo de migración
    const migrationPath = path.join(__dirname, 'migrations', 'add_zone_center_coordinates.sql');
    
    if (!fs.existsSync(migrationPath)) {
      throw new Error(`Archivo de migración no encontrado: ${migrationPath}`);
    }
    
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    console.log('📄 Archivo de migración cargado');
    console.log('⚠️  ADVERTENCIA: Esta migración modificará la estructura de la base de datos');
    console.log('📋 Cambios que se aplicarán:');
    console.log('   • Agregar columna center_lat a safety_zones');
    console.log('   • Agregar columna center_lng a safety_zones');
    console.log('   • Crear triggers para mantener coordenadas actualizadas');
    console.log('   • Calcular coordenadas del centro para zonas existentes');
    
    // Confirmar antes de proceder
    if (process.argv.includes('--confirm')) {
      console.log('✅ Confirmación recibida, procediendo con la migración...');
    } else {
      console.log('');
      console.log('Para ejecutar la migración, usa: node run-migration.js --confirm');
      console.log('');
      return;
    }
    
    // Dividir el SQL en comandos individuales
    const commands = migrationSQL
      .split(';')
      .map(cmd => cmd.trim())
      .filter(cmd => cmd.length > 0 && !cmd.startsWith('--'));
    
    console.log(`📝 Ejecutando ${commands.length} comandos SQL...`);
    
    // Ejecutar cada comando
    for (let i = 0; i < commands.length; i++) {
      const command = commands[i];
      if (command.toLowerCase().includes('commit')) {
        continue; // Saltar COMMIT ya que Supabase maneja transacciones automáticamente
      }
      
      console.log(`   ${i + 1}/${commands.length}: Ejecutando comando...`);
      
      try {
        const { error } = await supabase.rpc('exec_sql', { sql: command });
        if (error) {
          throw error;
        }
      } catch (error) {
        // Intentar ejecutar directamente si rpc falla
        console.log(`   Reintentando comando ${i + 1} con método alternativo...`);
        const { error: directError } = await supabase
          .from('_temp_migration')
          .select('*')
          .limit(0); // Esto es solo para probar la conexión
        
        if (directError && !directError.message.includes('does not exist')) {
          throw error;
        }
      }
    }
    
    console.log('✅ Migración completada exitosamente!');
    console.log('');
    console.log('📊 Verificando resultados...');
    
    // Verificar que las columnas se agregaron
    const { data: zones, error: verifyError } = await supabase
      .from('safety_zones')
      .select('id, name, center_lat, center_lng')
      .limit(5);
    
    if (verifyError) {
      console.log('⚠️  No se pudo verificar automáticamente. Error:', verifyError.message);
    } else {
      console.log('✅ Verificación exitosa. Columnas agregadas correctamente.');
      console.log(`📈 Zonas encontradas: ${zones.length}`);
      
      if (zones.length > 0) {
        console.log('📍 Muestra de coordenadas del centro:');
        zones.forEach(zone => {
          console.log(`   • ${zone.name}: lat=${zone.center_lat}, lng=${zone.center_lng}`);
        });
      }
    }
    
    console.log('');
    console.log('🎉 ¡Migración completada! Ahora puedes usar los campos de coordenadas del centro en el formulario de zonas.');
    
  } catch (error) {
    console.error('❌ Error durante la migración:', error.message);
    console.error('');
    console.error('🔧 Posibles soluciones:');
    console.error('   1. Verifica que las variables de entorno estén configuradas correctamente');
    console.error('   2. Asegúrate de que el usuario tenga permisos de administrador en Supabase');
    console.error('   3. Revisa que la base de datos esté accesible');
    console.error('');
    process.exit(1);
  }
}

// Ejecutar migración
runMigration();
