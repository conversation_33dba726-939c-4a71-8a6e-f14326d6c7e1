# 🚨 IMPORTANTE: Configuración de Red para Desarrollo Móvil

## ⚡ Solución Rápida

Si tu app funciona en el navegador pero **NO en el dispositivo móvil**, este es el problema más común:

### 🔧 Fix Rápido (5 minutos)

1. **Obtén tu IP local:**
   ```bash
   # Windows
   ipconfig | findstr "IPv4"
   
   # macOS/Linux  
   ifconfig | grep "inet " | grep -v 127.0.0.1
   ```

2. **Actualiza el backend para escuchar en todas las interfaces:**
   ```typescript
   // backoffice/backend/src/config/env.ts
   export const SERVER_CONFIG = {
     HOST: '0.0.0.0', // ⚠️ CAMBIAR DE 'localhost' A '0.0.0.0'
     PORT: 3002,
   };
   ```

3. **Reinicia el backend:**
   ```bash
   cd backoffice/backend
   npm run dev
   ```

4. **Verifica que funcione:**
   ```bash
   # Reemplaza ************ con tu IP
   curl http://************:3002/api/mobile/health
   ```

---

## 🔍 ¿Por Qué Pasa Esto?

| Entorno | `localhost` se refiere a... | Resultado |
|---------|----------------------------|-----------|
| **🌐 Navegador Web** | Tu computadora | ✅ Funciona |
| **📱 Dispositivo Móvil** | El dispositivo móvil mismo | ❌ Falla |

**Solución**: El backend debe escuchar en `0.0.0.0` (todas las interfaces) y la app móvil debe usar la IP de red de tu computadora.

---

## 📋 Checklist de Verificación

- [ ] Backend escucha en `0.0.0.0:3002`
- [ ] CORS incluye tu IP local
- [ ] API service detecta plataforma automáticamente
- [ ] WebSocket usa la misma IP que la API
- [ ] Probado en dispositivo móvil real

---

## 📚 Documentación Completa

- **[Configuración Detallada](./docs/MOBILE_NETWORK_CONFIGURATION.md)** - Explicación técnica completa
- **[Guía de Setup](./docs/DEVELOPMENT_SETUP_GUIDE.md)** - Scripts y automatización
- **[Troubleshooting](./docs/MOBILE_NETWORK_CONFIGURATION.md#-troubleshooting)** - Solución de problemas comunes

---

## 🆘 Ayuda Rápida

### Problema: "Network Error" en móvil
```bash
# 1. Verificar que backend escuche en red
netstat -an | findstr :3002

# 2. Debería mostrar:
# TCP    0.0.0.0:3002    0.0.0.0:0    LISTENING
```

### Problema: CORS errors
```typescript
// Agregar tu IP a CORS_ORIGIN en .env
CORS_ORIGIN=http://localhost:8081,http://TU_IP:8081
```

### Problema: WebSocket timeout
```typescript
// Verificar que WebSocket use la misma IP que la API
const wsURL = Platform.OS === 'web' 
  ? 'http://localhost:3002' 
  : 'http://TU_IP:3002';
```

---

**⚠️ CRÍTICO PARA PRODUCCIÓN**: Este problema debe resolverse antes de deployment. En producción, usar URLs absolutas con HTTPS.
