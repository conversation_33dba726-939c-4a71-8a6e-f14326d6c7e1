# 🌋 Volcano App Backend - Variables de Entorno

# =====================================================
# CONFIGURACIÓN DEL SERVIDOR
# =====================================================
NODE_ENV=development
PORT=3001
HOST=localhost

# =====================================================
# SUPABASE CONFIGURACIÓN
# =====================================================
SUPABASE_URL=https://gdcbnnlmxwazgnwpfelt.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdkY2JubmxteHdhemdud3BmZWx0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3NDMzODIsImV4cCI6MjA2NDMxOTM4Mn0.MzNjNQiXKDzwL8EL2beFDgTqeN_DJrXILjzRphOPlFg
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdkY2JubmxteHdhemdud3BmZWx0Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODc0MzM4MiwiZXhwIjoyMDY0MzE5MzgyfQ.EZQQW6ZRBI3u-FBfhnMlBq-VrLaWZZZ5UVfOzK8sZ1g

# =====================================================
# JWT CONFIGURACIÓN
# =====================================================
JWT_SECRET=your_super_secret_jwt_key_change_in_production
JWT_EXPIRES_IN=24h
JWT_REFRESH_SECRET=your_super_secret_refresh_key_change_in_production
JWT_REFRESH_EXPIRES_IN=7d

# =====================================================
# CONFIGURACIÓN DE SEGURIDAD
# =====================================================
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# =====================================================
# CONFIGURACIÓN DE LOGS
# =====================================================
LOG_LEVEL=info
LOG_FILE_PATH=./logs/app.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5

# =====================================================
# CONFIGURACIÓN DE CORS
# =====================================================
CORS_ORIGIN=http://localhost:5173,http://localhost:3000,http://localhost:3001,http://localhost:8081
CORS_CREDENTIALS=true

# =====================================================
# REDIS CACHE CONFIGURATION (Optional)
# =====================================================
# Set REDIS_DISABLED=true to run without Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_DISABLED=false

# =====================================================
# WEBSOCKET CONFIGURATION
# =====================================================
WEBSOCKET_ENABLED=true
WEBSOCKET_PING_TIMEOUT=60000
WEBSOCKET_PING_INTERVAL=25000
WEBSOCKET_MAX_BUFFER_SIZE=1048576

# =====================================================
# APPLICATION CONFIGURATION
# =====================================================
SWAGGER_ENABLED=true
SENTRY_DSN=

# =====================================================
# CONFIGURACIÓN DE UPLOADS
# =====================================================
UPLOAD_MAX_SIZE=5242880
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif,application/pdf

# =====================================================
# CONFIGURACIÓN DE NOTIFICACIONES
# =====================================================
PUSH_NOTIFICATION_KEY=your_push_notification_key
EMAIL_SERVICE_API_KEY=your_email_service_key
SMS_SERVICE_API_KEY=your_sms_service_key

# =====================================================
# CONFIGURACIÓN DE MONITOREO
# =====================================================
HEALTH_CHECK_INTERVAL=30000
METRICS_ENABLED=true
SENTRY_DSN=your_sentry_dsn_for_error_tracking

# =====================================================
# CONFIGURACIÓN DE CACHE
# =====================================================
REDIS_URL=redis://localhost:6379
CACHE_TTL=3600

# =====================================================
# CONFIGURACIÓN DE DESARROLLO
# =====================================================
DEBUG=volcano:*
SWAGGER_ENABLED=true
API_DOCS_PATH=/api-docs

# =====================================================
# CONFIGURACIÓN DE PRODUCCIÓN
# =====================================================
# Descomenta y configura para producción
# SSL_CERT_PATH=/path/to/ssl/cert.pem
# SSL_KEY_PATH=/path/to/ssl/key.pem
# CLUSTER_MODE=true
# CLUSTER_WORKERS=4
