/**
 * 🔧 Panel de Debugging para Volcano App Frontend
 * Interfaz visual para monitorear errores, logs y estado del sistema
 */

import React, { useState, useEffect } from 'react';
import { AlertTriangle, Bug, Download, RefreshCw, Trash2, X } from 'lucide-react';
import { <PERSON><PERSON> } from '../ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { debugManager } from '../../utils/debug';

interface DebugPanelProps {
  isOpen: boolean;
  onClose: () => void;
}

export function DebugPanel({ isOpen, onClose }: DebugPanelProps) {
  const [logs, setLogs] = useState(debugManager.getLogs());
  const [errorReports, setErrorReports] = useState(debugManager.getErrorReports());
  const [stats, setStats] = useState(debugManager.getStats());
  const [filter, setFilter] = useState<string>('all');

  // Actualizar datos cada 2 segundos
  useEffect(() => {
    if (!isOpen) return;

    const interval = setInterval(() => {
      setLogs(debugManager.getLogs());
      setErrorReports(debugManager.getErrorReports());
      setStats(debugManager.getStats());
    }, 2000);

    return () => clearInterval(interval);
  }, [isOpen]);

  if (!isOpen) return null;

  const filteredLogs = filter === 'all' 
    ? logs 
    : logs.filter(log => log.level === filter);

  const handleExport = () => {
    debugManager.exportLogs();
  };

  const handleClear = () => {
    debugManager.clearLogs();
    setLogs([]);
    setErrorReports([]);
    setStats(debugManager.getStats());
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'error': return 'bg-red-100 text-red-800 border-red-200';
      case 'warn': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'info': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'debug': return 'bg-gray-100 text-gray-800 border-gray-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-6xl h-[80vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <div className="flex items-center gap-2">
            <Bug className="h-5 w-5 text-blue-600" />
            <h2 className="text-lg font-semibold">Panel de Debugging</h2>
            <Badge variant="outline" className="ml-2">
              {stats.totalLogs} logs
            </Badge>
            {stats.errorCount > 0 && (
              <Badge variant="destructive" className="ml-1">
                {stats.errorCount} errores
              </Badge>
            )}
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={handleExport}>
              <Download className="h-4 w-4 mr-1" />
              Exportar
            </Button>
            <Button variant="outline" size="sm" onClick={handleClear}>
              <Trash2 className="h-4 w-4 mr-1" />
              Limpiar
            </Button>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-hidden">
          <Tabs defaultValue="logs" className="h-full flex flex-col">
            <TabsList className="grid w-full grid-cols-3 mx-4 mt-4">
              <TabsTrigger value="logs">Logs del Sistema</TabsTrigger>
              <TabsTrigger value="errors">Reportes de Errores</TabsTrigger>
              <TabsTrigger value="stats">Estadísticas</TabsTrigger>
            </TabsList>

            <TabsContent value="logs" className="flex-1 overflow-hidden p-4">
              <div className="h-full flex flex-col">
                {/* Filtros */}
                <div className="flex gap-2 mb-4">
                  <Button
                    variant={filter === 'all' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setFilter('all')}
                  >
                    Todos ({logs.length})
                  </Button>
                  <Button
                    variant={filter === 'error' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setFilter('error')}
                  >
                    Errores ({logs.filter(l => l.level === 'error').length})
                  </Button>
                  <Button
                    variant={filter === 'warn' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setFilter('warn')}
                  >
                    Warnings ({logs.filter(l => l.level === 'warn').length})
                  </Button>
                  <Button
                    variant={filter === 'info' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setFilter('info')}
                  >
                    Info ({logs.filter(l => l.level === 'info').length})
                  </Button>
                </div>

                {/* Lista de logs */}
                <div className="flex-1 overflow-auto space-y-2">
                  {filteredLogs.slice(-50).reverse().map((log, index) => (
                    <div
                      key={index}
                      className={`p-3 rounded border text-sm ${getLevelColor(log.level)}`}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="font-mono text-xs">
                              {formatTimestamp(log.timestamp)}
                            </span>
                            <Badge variant="outline" className="text-xs">
                              {log.category}
                            </Badge>
                            <Badge variant="outline" className="text-xs">
                              {log.level.toUpperCase()}
                            </Badge>
                          </div>
                          <div className="font-mono text-xs whitespace-pre-wrap">
                            {log.message}
                          </div>
                          {log.data && (
                            <details className="mt-2">
                              <summary className="cursor-pointer text-xs opacity-70">
                                Ver datos adicionales
                              </summary>
                              <pre className="mt-1 text-xs bg-black bg-opacity-10 p-2 rounded overflow-auto">
                                {JSON.stringify(log.data, null, 2)}
                              </pre>
                            </details>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                  {filteredLogs.length === 0 && (
                    <div className="text-center py-8 text-gray-500">
                      No hay logs para mostrar
                    </div>
                  )}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="errors" className="flex-1 overflow-hidden p-4">
              <div className="h-full overflow-auto space-y-4">
                {errorReports.slice(-20).reverse().map((error, index) => (
                  <Card key={index} className="border-red-200">
                    <CardHeader className="pb-2">
                      <div className="flex items-center gap-2">
                        <AlertTriangle className="h-4 w-4 text-red-600" />
                        <CardTitle className="text-sm">{error.message}</CardTitle>
                        <Badge variant="destructive" className="text-xs">
                          {error.type}
                        </Badge>
                      </div>
                      <p className="text-xs text-gray-500">
                        {formatTimestamp(error.timestamp)} - {error.id}
                      </p>
                    </CardHeader>
                    <CardContent className="pt-0">
                      {error.stack && (
                        <details>
                          <summary className="cursor-pointer text-xs text-gray-600 mb-2">
                            Ver stack trace
                          </summary>
                          <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto">
                            {error.stack}
                          </pre>
                        </details>
                      )}
                      <div className="text-xs text-gray-500 mt-2">
                        <p>URL: {error.url}</p>
                        <p>User Agent: {error.userAgent}</p>
                        {error.buildVersion && <p>Build: {error.buildVersion}</p>}
                      </div>
                    </CardContent>
                  </Card>
                ))}
                {errorReports.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    No hay reportes de errores
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="stats" className="flex-1 overflow-hidden p-4">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm">Total de Logs</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-2xl font-bold">{stats.totalLogs}</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm">Errores</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-2xl font-bold text-red-600">{stats.errorCount}</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm">Warnings</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-2xl font-bold text-yellow-600">{stats.warnCount}</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm">Reportes</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-2xl font-bold">{stats.errorReports}</p>
                  </CardContent>
                </Card>
              </div>
              
              <Card className="mt-4">
                <CardHeader>
                  <CardTitle className="text-sm">Categorías</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {stats.categories.map(category => (
                      <Badge key={category} variant="outline">
                        {category}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}
