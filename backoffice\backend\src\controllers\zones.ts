/**
 * 🌋 Volcano App Backend - Controlador de Zonas de Seguridad
 * CRUD completo para gestión de zonas de seguridad
 */

import { logAuditAction, supabaseAdmin } from '@/services/supabase';
import {
    AuthenticatedRequest,
    CreateZoneDto,
    DEFAULT_PAGINATION,
    UpdateZoneDto,
    ZoneType
} from '@/types';
import { logDatabase, logger } from '@/utils/logger';
import { Response } from 'express';

// =====================================================
// OBTENER TODAS LAS ZONAS
// =====================================================

/**
 * Obtener lista de zonas con paginación y filtros
 * GET /api/zones
 */
export async function getZones(req: AuthenticatedRequest, res: Response) {
  try {
    const {
      page = DEFAULT_PAGINATION.page,
      limit = DEFAULT_PAGINATION.limit,
      sort_by = 'created_at',
      sort_order = 'desc',
      search,
      zone_type,
      is_active
    } = req.query as any;

    // Validar límite máximo
    const validLimit = Math.min(parseInt(limit), DEFAULT_PAGINATION.max_limit);
    const offset = (parseInt(page) - 1) * validLimit;

    // Construir query base
    let query = supabaseAdmin
      .from('safety_zones')
      .select(`
        id,
        name,
        description,
        zone_type,
        geometry,
        capacity,
        contact_info,
        facilities,
        is_active,
        version,
        created_at,
        updated_at,
        metadata,
        admin_users!created_by(full_name, email)
      `, { count: 'exact' });

    // Aplicar filtros
    if (search) {
      query = query.or(`name.ilike.%${search}%,description.ilike.%${search}%`);
    }

    if (zone_type) {
      query = query.eq('zone_type', zone_type);
    }

    if (is_active !== undefined) {
      query = query.eq('is_active', is_active === 'true');
    }

    // Aplicar ordenamiento y paginación
    query = query
      .order(sort_by, { ascending: sort_order === 'asc' })
      .range(offset, offset + validLimit - 1);

    const { data: zones, error, count } = await query;

    if (error) {
      logDatabase('SELECT', 'safety_zones', undefined, req.user?.id, error);
      throw error;
    }

    const totalPages = Math.ceil((count || 0) / validLimit);

    res.json({
      success: true,
      data: zones,
      pagination: {
        page: parseInt(page),
        limit: validLimit,
        total: count || 0,
        totalPages,
        hasNext: parseInt(page) < totalPages,
        hasPrev: parseInt(page) > 1
      },
      timestamp: new Date()
    });

  } catch (error) {
    logger.error('Get zones error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch zones',
      timestamp: new Date()
    });
  }
}

// =====================================================
// OBTENER ZONA POR ID
// =====================================================

/**
 * Obtener una zona específica por ID
 * GET /api/zones/:id
 */
export async function getZoneById(req: AuthenticatedRequest, res: Response) {
  try {
    const { id } = req.params;

    const { data: zone, error } = await supabaseAdmin
      .from('safety_zones')
      .select(`
        *,
        admin_users!created_by(full_name, email)
      `)
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return res.status(404).json({
          success: false,
          error: 'Zone not found',
          timestamp: new Date()
        });
      }
      throw error;
    }

    res.json({
      success: true,
      data: zone,
      timestamp: new Date()
    });

  } catch (error) {
    logger.error('Get zone by ID error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch zone',
      timestamp: new Date()
    });
  }
}

// =====================================================
// CREAR NUEVA ZONA
// =====================================================

/**
 * Crear una nueva zona de seguridad
 * POST /api/zones
 */
export async function createZone(req: AuthenticatedRequest, res: Response) {
  try {
    const zoneData: CreateZoneDto = req.body;

    // Validaciones básicas
    if (!zoneData.name || !zoneData.zone_type || !zoneData.geometry) {
      return res.status(400).json({
        success: false,
        error: 'Name, zone_type, and geometry are required',
        timestamp: new Date()
      });
    }

    // Validar tipo de zona
    if (!Object.values(ZoneType).includes(zoneData.zone_type)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid zone type',
        timestamp: new Date()
      });
    }

    // Preparar datos para inserción
    const insertData = {
      ...zoneData,
      created_by: process.env.NODE_ENV === 'development' ? null : req.user?.id,
      version: 1,
      is_active: zoneData.is_active !== undefined ? zoneData.is_active : true
    };

    const { data: newZone, error } = await supabaseAdmin
      .from('safety_zones')
      .insert(insertData)
      .select(`
        *,
        admin_users!left(full_name, email)
      `)
      .single();

    if (error) {
      logDatabase('INSERT', 'safety_zones', undefined, req.user?.id, error);
      throw error;
    }

    // Log de auditoría
    await logAuditAction(
      req.user?.id || '',
      'CREATE',
      'safety_zones',
      newZone.id,
      null,
      newZone,
      req.ip,
      req.get('User-Agent')
    );

    logDatabase('INSERT', 'safety_zones', newZone.id, req.user?.id);

    res.status(201).json({
      success: true,
      data: newZone,
      message: 'Zone created successfully',
      timestamp: new Date()
    });

  } catch (error) {
    logger.error('Create zone error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create zone',
      timestamp: new Date()
    });
  }
}

// =====================================================
// ACTUALIZAR ZONA
// =====================================================

/**
 * Actualizar una zona existente
 * PUT /api/zones/:id
 */
export async function updateZone(req: AuthenticatedRequest, res: Response) {
  try {
    const { id } = req.params;
    const updateData: UpdateZoneDto = req.body;

    // Obtener zona actual para auditoría
    const { data: currentZone, error: fetchError } = await supabaseAdmin
      .from('safety_zones')
      .select('*')
      .eq('id', id)
      .single();

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return res.status(404).json({
          success: false,
          error: 'Zone not found',
          timestamp: new Date()
        });
      }
      throw fetchError;
    }

    // Validar tipo de zona si se proporciona
    if (updateData.zone_type && !Object.values(ZoneType).includes(updateData.zone_type)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid zone type',
        timestamp: new Date()
      });
    }

    // Incrementar versión y actualizar timestamp
    const finalUpdateData = {
      ...updateData,
      version: (currentZone.version || 1) + 1,
      updated_at: new Date().toISOString()
    };

    // Actualizar zona
    const { data: updatedZone, error } = await supabaseAdmin
      .from('safety_zones')
      .update(finalUpdateData)
      .eq('id', id)
      .select(`
        *,
        admin_users!created_by(full_name, email)
      `)
      .single();

    if (error) {
      logDatabase('UPDATE', 'safety_zones', id, req.user?.id, error);
      throw error;
    }

    // Log de auditoría
    await logAuditAction(
      req.user?.id || '',
      'UPDATE',
      'safety_zones',
      id,
      currentZone,
      updatedZone,
      req.ip,
      req.get('User-Agent')
    );

    logDatabase('UPDATE', 'safety_zones', id, req.user?.id);

    res.json({
      success: true,
      data: updatedZone,
      message: 'Zone updated successfully',
      timestamp: new Date()
    });

  } catch (error) {
    logger.error('Update zone error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update zone',
      timestamp: new Date()
    });
  }
}

// =====================================================
// ELIMINAR ZONA
// =====================================================

/**
 * Eliminar una zona (soft delete)
 * DELETE /api/zones/:id
 */
export async function deleteZone(req: AuthenticatedRequest, res: Response) {
  try {
    const { id } = req.params;

    // Obtener zona actual para auditoría
    const { data: currentZone, error: fetchError } = await supabaseAdmin
      .from('safety_zones')
      .select('*')
      .eq('id', id)
      .single();

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return res.status(404).json({
          success: false,
          error: 'Zone not found',
          timestamp: new Date()
        });
      }
      throw fetchError;
    }

    // Soft delete (marcar como inactiva)
    const { error } = await supabaseAdmin
      .from('safety_zones')
      .update({
        is_active: false,
        updated_at: new Date().toISOString()
      })
      .eq('id', id);

    if (error) {
      logDatabase('DELETE', 'safety_zones', id, req.user?.id, error);
      throw error;
    }

    // Log de auditoría
    await logAuditAction(
      req.user?.id || '',
      'DELETE',
      'safety_zones',
      id,
      currentZone,
      { is_active: false },
      req.ip,
      req.get('User-Agent')
    );

    logDatabase('DELETE', 'safety_zones', id, req.user?.id);

    res.json({
      success: true,
      message: 'Zone deleted successfully',
      timestamp: new Date()
    });

  } catch (error) {
    logger.error('Delete zone error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete zone',
      timestamp: new Date()
    });
  }
}

// =====================================================
// OBTENER ZONAS ACTIVAS (PARA APP MÓVIL)
// =====================================================

/**
 * Obtener zonas activas para la app móvil
 * GET /api/zones/active
 */
export async function getActiveZones(req: AuthenticatedRequest, res: Response) {
  try {
    const { data: zones, error } = await supabaseAdmin
      .from('safety_zones')
      .select('id, name, description, zone_type, geometry, capacity, is_active, version')
      .eq('is_active', true)
      .order('zone_type', { ascending: true });

    if (error) {
      throw error;
    }

    res.json({
      success: true,
      data: zones || [],
      timestamp: new Date()
    });

  } catch (error) {
    logger.error('Get active zones error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch active zones',
      timestamp: new Date()
    });
  }
}

// =====================================================
// EXPORTACIONES
// =====================================================

export default {
  getZones,
  getZoneById,
  createZone,
  updateZone,
  deleteZone,
  getActiveZones
};
