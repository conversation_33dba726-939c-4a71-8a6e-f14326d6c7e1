# 🌋 Implementación de Coordenadas del Centro para Zonas

## 📋 Resumen

Se han agregado campos de latitud y longitud del centro a las zonas de seguridad para permitir la edición manual de posición en el formulario del backoffice.

## 🔧 Cambios Implementados

### 1. **Base de Datos**
- ✅ Agregados campos `center_lat` y `center_lng` al esquema
- ✅ Creada migración automática con triggers
- ✅ Validaciones de rango (-90 a 90 para lat, -180 a 180 para lng)

### 2. **Backend**
- ✅ Actualizados DTOs (`CreateZoneDto`, `UpdateZoneDto`)
- ✅ Actualizadas consultas SQL para incluir nuevos campos
- ✅ Agregadas validaciones en middleware

### 3. **Frontend**
- ✅ Actualizado formulario de edición con campos de coordenadas
- ✅ Agregada validación de rangos en el frontend
- ✅ Interfaz mejorada con información contextual

### 4. **Tipos Compartidos**
- ✅ Actualizadas interfaces TypeScript en `packages/shared-types`

## 🚀 Instrucciones de Implementación

### Paso 1: Aplicar Migración de Base de Datos

```bash
# Navegar al directorio de base de datos
cd backoffice/database

# Configurar variables de entorno
export SUPABASE_URL="https://tu-proyecto.supabase.co"
export SUPABASE_SERVICE_ROLE_KEY="tu-service-role-key"

# Ejecutar migración
node run-migration.js --confirm
```

### Paso 2: Verificar Backend

```bash
# Navegar al backend
cd backoffice/backend

# Instalar dependencias si es necesario
npm install

# Ejecutar backend
npm run dev
```

### Paso 3: Verificar Frontend

```bash
# Navegar al frontend
cd backoffice/frontend

# Instalar dependencias si es necesario
npm install

# Ejecutar frontend
npm run dev
```

## 📱 Funcionalidades Nuevas

### En el Formulario de Edición de Zonas:

1. **Campos de Coordenadas del Centro**
   - Latitud: Campo numérico con validación (-90 a 90)
   - Longitud: Campo numérico con validación (-180 a 180)
   - Ambos campos son opcionales

2. **Información Contextual**
   - Rangos válidos de coordenadas
   - Coordenadas del Volcán Villarrica como referencia
   - Explicación de cálculo automático

3. **Validación Automática**
   - Validación en tiempo real de rangos
   - Mensajes de error descriptivos
   - Integración con sistema de validación existente

## 🔄 Comportamiento Automático

### Triggers de Base de Datos:

1. **Al Crear Zona**: Si no se proporcionan coordenadas del centro, se calculan automáticamente desde la geometría
2. **Al Actualizar Geometría**: Las coordenadas del centro se recalculan automáticamente
3. **Al Actualizar Coordenadas**: Se mantienen las coordenadas especificadas manualmente

## 🧪 Pruebas Recomendadas

### 1. Crear Nueva Zona
- [ ] Crear zona sin especificar coordenadas → Verificar cálculo automático
- [ ] Crear zona con coordenadas específicas → Verificar que se mantienen
- [ ] Probar validación de rangos inválidos

### 2. Editar Zona Existente
- [ ] Editar zona sin cambiar coordenadas → Verificar que se mantienen
- [ ] Cambiar coordenadas manualmente → Verificar actualización
- [ ] Cambiar geometría en mapa → Verificar recálculo automático

### 3. Validaciones
- [ ] Latitud > 90 → Error
- [ ] Latitud < -90 → Error
- [ ] Longitud > 180 → Error
- [ ] Longitud < -180 → Error

## 📊 Datos de Referencia

### Volcán Villarrica
- **Latitud**: -39.420000
- **Longitud**: -71.939167

### Rangos Válidos
- **Latitud**: -90 (Polo Sur) a 90 (Polo Norte)
- **Longitud**: -180 (Oeste) a 180 (Este)

## 🔍 Verificación Post-Implementación

### Base de Datos
```sql
-- Verificar estructura de tabla
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'safety_zones' 
AND column_name IN ('center_lat', 'center_lng');

-- Verificar datos existentes
SELECT id, name, center_lat, center_lng 
FROM safety_zones 
LIMIT 5;
```

### API
```bash
# Probar endpoint de zonas
curl -X GET "http://localhost:3001/api/zones" \
  -H "Content-Type: application/json"
```

### Frontend
1. Abrir formulario de edición de zona
2. Verificar presencia de campos de coordenadas
3. Probar validación ingresando valores inválidos
4. Verificar información contextual

## 🐛 Solución de Problemas

### Error: "Column does not exist"
- Verificar que la migración se ejecutó correctamente
- Revisar logs de Supabase para errores de migración

### Error: "Validation failed"
- Verificar que los valores están en rangos válidos
- Revisar configuración de validación en backend

### Frontend no muestra campos
- Verificar que el frontend se recompiló después de los cambios
- Revisar consola del navegador para errores TypeScript

## 📝 Notas Adicionales

- Los campos de coordenadas son **opcionales** para mantener compatibilidad
- El sistema calcula automáticamente las coordenadas si no se proporcionan
- Los triggers mantienen la consistencia entre geometría y coordenadas del centro
- La implementación es **backward compatible** con zonas existentes
