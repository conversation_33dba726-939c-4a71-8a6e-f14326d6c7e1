#!/usr/bin/env node

/**
 * 🚀 Script de Inicio Automático - Volcano App
 * 
 * Este script arranca automáticamente los 3 proyectos principales:
 * 1. Backend API (Node.js + Express)
 * 2. Frontend Admin (React + Vite) 
 * 3. App <PERSON> (React Native + Expo)
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Colores para la consola
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// Configuración de los proyectos
const projects = [
  {
    name: '🔧 Backend API',
    color: colors.blue,
    cwd: path.join(__dirname, '../backoffice/backend'),
    command: 'npm',
    args: ['run', 'dev'],
    port: 3001,
    healthCheck: 'http://localhost:3001/health'
  },
  {
    name: '💻 Frontend Admin',
    color: colors.green,
    cwd: path.join(__dirname, '../backoffice/frontend'),
    command: 'npm',
    args: ['run', 'dev'],
    port: 5173,
    healthCheck: 'http://localhost:5173'
  },
  {
    name: '📱 App Móvil',
    color: colors.magenta,
    cwd: path.join(__dirname, '..'),
    command: 'npm',
    args: ['run', 'dev'],
    port: 8081,
    healthCheck: 'http://localhost:8081'
  }
];

// Función para imprimir con colores
function colorLog(color, message) {
  console.log(`${color}${message}${colors.reset}`);
}

// Función para verificar si un directorio existe
function checkDirectory(dir) {
  if (!fs.existsSync(dir)) {
    colorLog(colors.red, `❌ Error: Directorio no encontrado: ${dir}`);
    return false;
  }
  return true;
}

// Función para verificar si package.json existe
function checkPackageJson(dir) {
  const packagePath = path.join(dir, 'package.json');
  if (!fs.existsSync(packagePath)) {
    colorLog(colors.red, `❌ Error: package.json no encontrado en: ${dir}`);
    return false;
  }
  return true;
}

// Función para verificar dependencias
function checkDependencies(dir) {
  const nodeModulesPath = path.join(dir, 'node_modules');
  if (!fs.existsSync(nodeModulesPath)) {
    colorLog(colors.yellow, `⚠️  Advertencia: node_modules no encontrado en: ${dir}`);
    colorLog(colors.yellow, `   Ejecuta: cd ${dir} && npm install`);
    return false;
  }
  return true;
}

// Función para arrancar un proyecto
function startProject(project) {
  return new Promise((resolve, reject) => {
    colorLog(colors.bright, `\n🚀 Iniciando ${project.name}...`);
    colorLog(colors.cyan, `   Directorio: ${project.cwd}`);
    colorLog(colors.cyan, `   Comando: ${project.command} ${project.args.join(' ')}`);
    colorLog(colors.cyan, `   Puerto: ${project.port}`);

    // Verificar directorio y dependencias
    if (!checkDirectory(project.cwd)) {
      reject(new Error(`Directorio no encontrado: ${project.cwd}`));
      return;
    }

    if (!checkPackageJson(project.cwd)) {
      reject(new Error(`package.json no encontrado en: ${project.cwd}`));
      return;
    }

    if (!checkDependencies(project.cwd)) {
      colorLog(colors.yellow, `   Continuando sin node_modules...`);
    }

    // Arrancar el proceso
    const child = spawn(project.command, project.args, {
      cwd: project.cwd,
      stdio: 'pipe',
      shell: true
    });

    // Manejar salida estándar
    child.stdout.on('data', (data) => {
      const output = data.toString().trim();
      if (output) {
        colorLog(project.color, `[${project.name}] ${output}`);
      }
    });

    // Manejar errores
    child.stderr.on('data', (data) => {
      const error = data.toString().trim();
      if (error && !error.includes('Warning')) {
        colorLog(colors.red, `[${project.name}] ERROR: ${error}`);
      }
    });

    // Manejar cierre del proceso
    child.on('close', (code) => {
      if (code !== 0) {
        colorLog(colors.red, `❌ ${project.name} terminó con código: ${code}`);
        reject(new Error(`Proceso terminó con código: ${code}`));
      } else {
        colorLog(colors.green, `✅ ${project.name} terminó correctamente`);
        resolve();
      }
    });

    // Manejar errores del proceso
    child.on('error', (error) => {
      colorLog(colors.red, `❌ Error al iniciar ${project.name}: ${error.message}`);
      reject(error);
    });

    // Guardar referencia del proceso
    project.process = child;

    // Resolver después de un tiempo (el proceso seguirá corriendo)
    setTimeout(() => {
      colorLog(colors.green, `✅ ${project.name} iniciado correctamente`);
      resolve();
    }, 3000);
  });
}

// Función para mostrar información de los proyectos
function showProjectInfo() {
  colorLog(colors.bright, '\n📋 Información de los Proyectos:');
  colorLog(colors.cyan, '━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
  
  projects.forEach(project => {
    colorLog(colors.bright, `\n${project.name}`);
    colorLog(colors.cyan, `   📁 Directorio: ${project.cwd}`);
    colorLog(colors.cyan, `   🌐 Puerto: ${project.port}`);
    colorLog(colors.cyan, `   🔗 URL: ${project.healthCheck}`);
    colorLog(colors.cyan, `   ⚡ Comando: ${project.command} ${project.args.join(' ')}`);
  });
  
  colorLog(colors.cyan, '\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
}

// Función para mostrar URLs de acceso
function showAccessUrls() {
  colorLog(colors.bright, '\n🌐 URLs de Acceso:');
  colorLog(colors.cyan, '━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
  colorLog(colors.blue, '   🔧 Backend API: http://localhost:3001');
  colorLog(colors.blue, '   📚 API Docs: http://localhost:3001/api-docs');
  colorLog(colors.green, '   💻 Frontend Admin: http://localhost:5173');
  colorLog(colors.magenta, '   📱 Expo DevTools: http://localhost:8081');
  colorLog(colors.cyan, '━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
}

// Función para manejar la interrupción del script
function handleExit() {
  colorLog(colors.yellow, '\n🛑 Deteniendo todos los procesos...');
  
  projects.forEach(project => {
    if (project.process) {
      colorLog(colors.yellow, `   Deteniendo ${project.name}...`);
      project.process.kill('SIGTERM');
    }
  });
  
  setTimeout(() => {
    colorLog(colors.red, '\n👋 ¡Hasta luego!');
    process.exit(0);
  }, 2000);
}

// Función principal
async function main() {
  try {
    // Mostrar banner
    colorLog(colors.bright, '\n🌋 VOLCANO APP - INICIO AUTOMÁTICO');
    colorLog(colors.cyan, '═══════════════════════════════════════════════════');
    
    // Mostrar información de los proyectos
    showProjectInfo();
    
    colorLog(colors.bright, '\n🚀 Iniciando todos los proyectos...');
    
    // Arrancar proyectos en secuencia
    for (const project of projects) {
      await startProject(project);
      // Esperar un poco entre cada proyecto
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
    
    // Mostrar URLs de acceso
    showAccessUrls();
    
    colorLog(colors.green, '\n✅ ¡Todos los proyectos están corriendo!');
    colorLog(colors.yellow, '\n💡 Presiona Ctrl+C para detener todos los procesos');
    
    // Mantener el script corriendo
    process.stdin.resume();
    
  } catch (error) {
    colorLog(colors.red, `\n❌ Error: ${error.message}`);
    process.exit(1);
  }
}

// Manejar señales de interrupción
process.on('SIGINT', handleExit);
process.on('SIGTERM', handleExit);

// Ejecutar script principal
if (require.main === module) {
  main();
}

module.exports = { startProject, projects };
